{"name": "heroui-native-example", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start -c", "android": "expo start --android -c", "ios": "expo start --ios -c", "web": "expo start --web -c"}, "dependencies": {"@expo-google-fonts/abel": "^0.4.0", "@expo-google-fonts/inter": "^0.4.1", "@expo/metro-runtime": "~6.1.1", "clsx": "^2.1.1", "expo": "^54.0.8", "expo-blur": "~15.0.6", "expo-constants": "~18.0.9", "expo-font": "~14.0.7", "expo-haptics": "~15.0.6", "expo-image": "~3.0.7", "expo-linear-gradient": "~15.0.6", "expo-linking": "~8.0.7", "expo-router": "~6.0.6", "expo-status-bar": "~3.0.7", "expo-updates": "~29.0.9", "lucide-react-native": "^0.544.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.1", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-web": "^0.21.0", "react-native-worklets": "^0.5.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^3.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "babel-plugin-module-resolver": "^5.0.2", "prettier-plugin-tailwindcss": "^0.5.11", "react-native-builder-bob": "^0.40.12", "react-native-monorepo-config": "^0.1.9", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7"}, "private": true}