PODS:
  - EASClient (1.0.7):
    - ExpoModulesCore
  - EXConstants (18.0.9):
    - ExpoModulesCore
  - EXJSONUtils (0.15.0)
  - EXManifests (1.0.8):
    - ExpoModulesCore
  - Expo (54.0.8):
    - ExpoModulesCore
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactAppDependencyProvider
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - ExpoAsset (12.0.8):
    - ExpoModulesCore
  - ExpoBlur (15.0.7):
    - ExpoModulesCore
  - ExpoFileSystem (19.0.14):
    - ExpoModulesCore
  - ExpoFont (14.0.8):
    - ExpoModulesCore
  - ExpoHaptics (15.0.7):
    - ExpoModulesCore
  - ExpoHead (6.0.6):
    - ExpoModulesCore
    - RNScreens
  - ExpoImage (3.0.8):
    - ExpoModulesCore
    - libavif/libdav1d
    - SDWebImage (~> 5.21.0)
    - SDWebImageAVIFCoder (~> 0.11.0)
    - SDWebImageSVGCoder (~> 1.7.0)
    - SDWebImageWebPCoder (~> 0.14.6)
  - ExpoKeepAwake (15.0.7):
    - ExpoModulesCore
  - ExpoLinearGradient (15.0.7):
    - ExpoModulesCore
  - ExpoLinking (8.0.8):
    - ExpoModulesCore
  - ExpoModulesCore (3.0.16):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - EXStructuredHeaders (5.0.0)
  - EXUpdates (29.0.10):
    - EASClient
    - EXManifests
    - ExpoModulesCore
    - EXStructuredHeaders
    - EXUpdatesInterface
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - ReachabilitySwift
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - EXUpdatesInterface (2.0.0):
    - ExpoModulesCore
  - FBLazyVector (0.81.4)
  - hermes-engine (0.81.4):
    - hermes-engine/Pre-built (= 0.81.4)
  - hermes-engine/Pre-built (0.81.4)
  - libavif/core (0.11.1)
  - libavif/libdav1d (0.11.1):
    - libavif/core
    - libdav1d (>= 0.6.0)
  - libdav1d (1.2.0)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - RCTDeprecation (0.81.4)
  - RCTRequired (0.81.4)
  - RCTTypeSafety (0.81.4):
    - FBLazyVector (= 0.81.4)
    - RCTRequired (= 0.81.4)
    - React-Core (= 0.81.4)
  - ReachabilitySwift (5.2.4)
  - React (0.81.4):
    - React-Core (= 0.81.4)
    - React-Core/DevSupport (= 0.81.4)
    - React-Core/RCTWebSocket (= 0.81.4)
    - React-RCTActionSheet (= 0.81.4)
    - React-RCTAnimation (= 0.81.4)
    - React-RCTBlob (= 0.81.4)
    - React-RCTImage (= 0.81.4)
    - React-RCTLinking (= 0.81.4)
    - React-RCTNetwork (= 0.81.4)
    - React-RCTSettings (= 0.81.4)
    - React-RCTText (= 0.81.4)
    - React-RCTVibration (= 0.81.4)
  - React-callinvoker (0.81.4)
  - React-Core (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core-prebuilt (0.81.4):
    - ReactNativeDependencies
  - React-Core/CoreModulesHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/Default (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/DevSupport (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.4)
    - React-Core/RCTWebSocket (= 0.81.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTAnimationHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTBlobHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTImageHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTLinkingHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTNetworkHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTSettingsHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTTextHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTVibrationHeaders (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-Core/RCTWebSocket (0.81.4):
    - hermes-engine
    - RCTDeprecation
    - React-Core-prebuilt
    - React-Core/Default (= 0.81.4)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-CoreModules (0.81.4):
    - RCTTypeSafety (= 0.81.4)
    - React-Core-prebuilt
    - React-Core/CoreModulesHeaders (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.81.4)
    - React-runtimeexecutor
    - ReactCommon
    - ReactNativeDependencies
  - React-cxxreact (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-debug (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - React-timing (= 0.81.4)
    - ReactNativeDependencies
  - React-debug (0.81.4)
  - React-defaultsnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - ReactNativeDependencies
  - React-domnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-Fabric
    - React-Fabric/bridging
    - React-FabricComponents
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-Fabric (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.81.4)
    - React-Fabric/attributedstring (= 0.81.4)
    - React-Fabric/bridging (= 0.81.4)
    - React-Fabric/componentregistry (= 0.81.4)
    - React-Fabric/componentregistrynative (= 0.81.4)
    - React-Fabric/components (= 0.81.4)
    - React-Fabric/consistency (= 0.81.4)
    - React-Fabric/core (= 0.81.4)
    - React-Fabric/dom (= 0.81.4)
    - React-Fabric/imagemanager (= 0.81.4)
    - React-Fabric/leakchecker (= 0.81.4)
    - React-Fabric/mounting (= 0.81.4)
    - React-Fabric/observers (= 0.81.4)
    - React-Fabric/scheduler (= 0.81.4)
    - React-Fabric/telemetry (= 0.81.4)
    - React-Fabric/templateprocessor (= 0.81.4)
    - React-Fabric/uimanager (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/animations (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/attributedstring (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/bridging (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/componentregistry (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/componentregistrynative (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.81.4)
    - React-Fabric/components/root (= 0.81.4)
    - React-Fabric/components/scrollview (= 0.81.4)
    - React-Fabric/components/view (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/legacyviewmanagerinterop (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/root (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/scrollview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/components/view (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-Fabric/consistency (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/core (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/dom (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/imagemanager (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/leakchecker (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/mounting (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/observers (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/observers/events (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/scheduler (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/telemetry (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/templateprocessor (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/uimanager (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-Fabric/uimanager/consistency (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-FabricComponents (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.81.4)
    - React-FabricComponents/textlayoutmanager (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.81.4)
    - React-FabricComponents/components/iostextinput (= 0.81.4)
    - React-FabricComponents/components/modal (= 0.81.4)
    - React-FabricComponents/components/rncore (= 0.81.4)
    - React-FabricComponents/components/safeareaview (= 0.81.4)
    - React-FabricComponents/components/scrollview (= 0.81.4)
    - React-FabricComponents/components/switch (= 0.81.4)
    - React-FabricComponents/components/text (= 0.81.4)
    - React-FabricComponents/components/textinput (= 0.81.4)
    - React-FabricComponents/components/unimplementedview (= 0.81.4)
    - React-FabricComponents/components/virtualview (= 0.81.4)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/iostextinput (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/modal (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/rncore (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/safeareaview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/scrollview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/switch (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/text (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/textinput (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/components/virtualview (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-RCTFBReactNativeSpec
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-FabricImage (0.81.4):
    - hermes-engine
    - RCTRequired (= 0.81.4)
    - RCTTypeSafety (= 0.81.4)
    - React-Core-prebuilt
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.81.4)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
    - Yoga
  - React-featureflags (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-featureflagsnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-graphics (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - ReactNativeDependencies
  - React-hermes (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi
    - React-jsiexecutor (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-idlecallbacksnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimeexecutor
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-ImageManager (0.81.4):
    - React-Core-prebuilt
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - ReactNativeDependencies
  - React-jserrorhandler (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - ReactNativeDependencies
  - React-jsi (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-jsiexecutor (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsinspector (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-oscompat
    - React-perflogger (= 0.81.4)
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsinspectorcdp (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-jsinspectornetwork (0.81.4):
    - React-Core-prebuilt
    - React-featureflags
    - React-jsinspectorcdp
    - React-performancetimeline
    - React-timing
    - ReactNativeDependencies
  - React-jsinspectortracing (0.81.4):
    - React-Core-prebuilt
    - React-oscompat
    - React-timing
    - ReactNativeDependencies
  - React-jsitooling (0.81.4):
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-runtimeexecutor
    - ReactNativeDependencies
  - React-jsitracing (0.81.4):
    - React-jsi
  - React-logger (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-Mapbuffer (0.81.4):
    - React-Core-prebuilt
    - React-debug
    - ReactNativeDependencies
  - React-microtasksnativemodule (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - react-native-safe-area-context (5.6.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.6.1)
    - react-native-safe-area-context/fabric (= 5.6.1)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - react-native-safe-area-context/common (5.6.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - react-native-safe-area-context/fabric (5.6.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - React-NativeModulesApple (0.81.4):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-Core-prebuilt
    - React-cxxreact
    - React-featureflags
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - React-oscompat (0.81.4)
  - React-perflogger (0.81.4):
    - React-Core-prebuilt
    - ReactNativeDependencies
  - React-performancetimeline (0.81.4):
    - React-Core-prebuilt
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - ReactNativeDependencies
  - React-RCTActionSheet (0.81.4):
    - React-Core/RCTActionSheetHeaders (= 0.81.4)
  - React-RCTAnimation (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTAppDelegate (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTBlob (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTFabric (0.81.4):
    - hermes-engine
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
    - Yoga
  - React-RCTFBReactNativeSpec (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec/components (= 0.81.4)
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTFBReactNativeSpec/components (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - ReactNativeDependencies
    - Yoga
  - React-RCTImage (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTLinking (0.81.4):
    - React-Core/RCTLinkingHeaders (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.81.4)
  - React-RCTNetwork (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTRuntime (0.81.4):
    - hermes-engine
    - React-Core
    - React-Core-prebuilt
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - ReactNativeDependencies
  - React-RCTSettings (0.81.4):
    - RCTTypeSafety
    - React-Core-prebuilt
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-RCTText (0.81.4):
    - React-Core/RCTTextHeaders (= 0.81.4)
    - Yoga
  - React-RCTVibration (0.81.4):
    - React-Core-prebuilt
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactNativeDependencies
  - React-rendererconsistency (0.81.4)
  - React-renderercss (0.81.4):
    - React-debug
    - React-utils
  - React-rendererdebug (0.81.4):
    - React-Core-prebuilt
    - React-debug
    - ReactNativeDependencies
  - React-RuntimeApple (0.81.4):
    - hermes-engine
    - React-callinvoker
    - React-Core-prebuilt
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
  - React-RuntimeCore (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - ReactNativeDependencies
  - React-runtimeexecutor (0.81.4):
    - React-Core-prebuilt
    - React-debug
    - React-featureflags
    - React-jsi (= 0.81.4)
    - React-utils
    - ReactNativeDependencies
  - React-RuntimeHermes (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-utils
    - ReactNativeDependencies
  - React-runtimescheduler (0.81.4):
    - hermes-engine
    - React-callinvoker
    - React-Core-prebuilt
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - ReactNativeDependencies
  - React-timing (0.81.4):
    - React-debug
  - React-utils (0.81.4):
    - hermes-engine
    - React-Core-prebuilt
    - React-debug
    - React-jsi (= 0.81.4)
    - ReactNativeDependencies
  - ReactAppDependencyProvider (0.81.4):
    - ReactCodegen
  - ReactCodegen (0.81.4):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
  - ReactCommon (0.81.4):
    - React-Core-prebuilt
    - ReactCommon/turbomodule (= 0.81.4)
    - ReactNativeDependencies
  - ReactCommon/turbomodule (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - ReactCommon/turbomodule/bridging (= 0.81.4)
    - ReactCommon/turbomodule/core (= 0.81.4)
    - ReactNativeDependencies
  - ReactCommon/turbomodule/bridging (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - ReactNativeDependencies
  - ReactCommon/turbomodule/core (0.81.4):
    - hermes-engine
    - React-callinvoker (= 0.81.4)
    - React-Core-prebuilt
    - React-cxxreact (= 0.81.4)
    - React-debug (= 0.81.4)
    - React-featureflags (= 0.81.4)
    - React-jsi (= 0.81.4)
    - React-logger (= 0.81.4)
    - React-perflogger (= 0.81.4)
    - React-utils (= 0.81.4)
    - ReactNativeDependencies
  - ReactNativeDependencies (0.81.4)
  - RNGestureHandler (2.28.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNReanimated (4.1.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNReanimated/reanimated (= 4.1.0)
    - RNWorklets
    - Yoga
  - RNReanimated/reanimated (4.1.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNReanimated/reanimated/apple (= 4.1.0)
    - RNWorklets
    - Yoga
  - RNReanimated/reanimated/apple (4.1.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNWorklets
    - Yoga
  - RNScreens (4.16.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNScreens/common (= 4.16.0)
    - Yoga
  - RNScreens/common (4.16.0):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNSVG (15.12.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNSVG/common (= 15.12.1)
    - Yoga
  - RNSVG/common (15.12.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - RNWorklets (0.5.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNWorklets/worklets (= 0.5.1)
    - Yoga
  - RNWorklets/worklets (0.5.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - RNWorklets/worklets/apple (= 0.5.1)
    - Yoga
  - RNWorklets/worklets/apple (0.5.1):
    - hermes-engine
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-Core-prebuilt
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - ReactNativeDependencies
    - Yoga
  - SDWebImage (5.21.2):
    - SDWebImage/Core (= 5.21.2)
  - SDWebImage/Core (5.21.2)
  - SDWebImageAVIFCoder (0.11.1):
    - libavif/core (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageSVGCoder (1.7.0):
    - SDWebImage/Core (~> 5.6)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - Yoga (0.0.0)

DEPENDENCIES:
  - "EASClient (from `../node_modules/.pnpm/expo-eas-client@1.0.7/node_modules/expo-eas-client/ios`)"
  - "EXConstants (from `../node_modules/.pnpm/expo-constants@18.0.9_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0_/node_modules/expo-constants/ios`)"
  - "EXJSONUtils (from `../node_modules/.pnpm/expo-json-utils@0.15.0/node_modules/expo-json-utils/ios`)"
  - "EXManifests (from `../node_modules/.pnpm/expo-manifests@1.0.8_expo@54.0.8/node_modules/expo-manifests/ios`)"
  - "Expo (from `../node_modules/.pnpm/expo@54.0.8_@babel+core@7.28.4_@expo+metro-runtime@6.1.2_expo-router@6.0.6_react-native_c016dcdb98529dc74db267d779f9928f/node_modules/expo`)"
  - "ExpoAsset (from `../node_modules/.pnpm/expo-asset@12.0.8_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-asset/ios`)"
  - "ExpoBlur (from `../node_modules/.pnpm/expo-blur@15.0.7_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-blur/ios`)"
  - "ExpoFileSystem (from `../node_modules/.pnpm/expo-file-system@19.0.14_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0_/node_modules/expo-file-system/ios`)"
  - "ExpoFont (from `../node_modules/.pnpm/expo-font@14.0.8_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-font/ios`)"
  - "ExpoHaptics (from `../node_modules/.pnpm/expo-haptics@15.0.7_expo@54.0.8/node_modules/expo-haptics/ios`)"
  - "ExpoHead (from `../node_modules/.pnpm/expo-router@6.0.6_@expo+metro-runtime@6.1.2_expo-constants@18.0.9_expo-linking@8.0.8_ex_2e7547a3971f9342be3267eaf914fb42/node_modules/expo-router/ios`)"
  - "ExpoImage (from `../node_modules/.pnpm/expo-image@3.0.8_expo@54.0.8_react-native-web@0.21.1_react-dom@19.1.0_react@19.1.0__rea_c42be68c7e2ba5d74956381944527e15/node_modules/expo-image/ios`)"
  - "ExpoKeepAwake (from `../node_modules/.pnpm/expo-keep-awake@15.0.7_expo@54.0.8_react@19.1.0/node_modules/expo-keep-awake/ios`)"
  - "ExpoLinearGradient (from `../node_modules/.pnpm/expo-linear-gradient@15.0.7_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-linear-gradient/ios`)"
  - "ExpoLinking (from `../node_modules/.pnpm/expo-linking@8.0.8_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-linking/ios`)"
  - "ExpoModulesCore (from `../node_modules/.pnpm/expo-modules-core@3.0.16_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-modules-core`)"
  - "EXStructuredHeaders (from `../node_modules/.pnpm/expo-structured-headers@5.0.0/node_modules/expo-structured-headers/ios`)"
  - "EXUpdates (from `../node_modules/.pnpm/expo-updates@29.0.10_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-updates/ios`)"
  - "EXUpdatesInterface (from `../node_modules/.pnpm/expo-updates-interface@2.0.0_expo@54.0.8/node_modules/expo-updates-interface/ios`)"
  - "FBLazyVector (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/FBLazyVector`)"
  - "hermes-engine (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)"
  - "RCTDeprecation (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)"
  - "RCTRequired (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Required`)"
  - "RCTTypeSafety (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/TypeSafety`)"
  - "React (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/`)"
  - "React-callinvoker (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/callinvoker`)"
  - "React-Core (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/`)"
  - "React-Core-prebuilt (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React-Core-prebuilt.podspec`)"
  - "React-Core/RCTWebSocket (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/`)"
  - "React-CoreModules (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React/CoreModules`)"
  - "React-cxxreact (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/cxxreact`)"
  - "React-debug (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/debug`)"
  - "React-defaultsnativemodule (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/defaults`)"
  - "React-domnativemodule (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/dom`)"
  - "React-Fabric (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon`)"
  - "React-FabricComponents (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon`)"
  - "React-FabricImage (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon`)"
  - "React-featureflags (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/featureflags`)"
  - "React-featureflagsnativemodule (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)"
  - "React-graphics (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/graphics`)"
  - "React-hermes (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/hermes`)"
  - "React-idlecallbacksnativemodule (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)"
  - "React-ImageManager (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)"
  - "React-jserrorhandler (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jserrorhandler`)"
  - "React-jsi (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsi`)"
  - "React-jsiexecutor (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsiexecutor`)"
  - "React-jsinspector (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern`)"
  - "React-jsinspectorcdp (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)"
  - "React-jsinspectornetwork (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern/network`)"
  - "React-jsinspectortracing (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)"
  - "React-jsitooling (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsitooling`)"
  - "React-jsitracing (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/hermes/executor/`)"
  - "React-logger (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/logger`)"
  - "React-Mapbuffer (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon`)"
  - "React-microtasksnativemodule (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)"
  - "react-native-safe-area-context (from `../node_modules/.pnpm/react-native-safe-area-context@5.6.1_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-safe-area-context`)"
  - "React-NativeModulesApple (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)"
  - "React-oscompat (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/oscompat`)"
  - "React-perflogger (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/reactperflogger`)"
  - "React-performancetimeline (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/performance/timeline`)"
  - "React-RCTActionSheet (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/ActionSheetIOS`)"
  - "React-RCTAnimation (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/NativeAnimation`)"
  - "React-RCTAppDelegate (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/AppDelegate`)"
  - "React-RCTBlob (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Blob`)"
  - "React-RCTFabric (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React`)"
  - "React-RCTFBReactNativeSpec (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React`)"
  - "React-RCTImage (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Image`)"
  - "React-RCTLinking (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/LinkingIOS`)"
  - "React-RCTNetwork (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Network`)"
  - "React-RCTRuntime (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React/Runtime`)"
  - "React-RCTSettings (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Settings`)"
  - "React-RCTText (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Text`)"
  - "React-RCTVibration (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Vibration`)"
  - "React-rendererconsistency (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/consistency`)"
  - "React-renderercss (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/css`)"
  - "React-rendererdebug (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/debug`)"
  - "React-RuntimeApple (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/runtime/platform/ios`)"
  - "React-RuntimeCore (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/runtime`)"
  - "React-runtimeexecutor (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/runtimeexecutor`)"
  - "React-RuntimeHermes (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/runtime`)"
  - "React-runtimescheduler (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)"
  - "React-timing (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/timing`)"
  - "React-utils (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/utils`)"
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - "ReactCommon/turbomodule/core (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon`)"
  - "ReactNativeDependencies (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/third-party-podspecs/ReactNativeDependencies.podspec`)"
  - "RNGestureHandler (from `../node_modules/.pnpm/react-native-gesture-handler@2.28.0_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-gesture-handler`)"
  - "RNReanimated (from `../node_modules/.pnpm/react-native-reanimated@4.1.0_@babel+core@7.28.4_react-native-worklets@0.5.1_@babel+cor_0e74ef6bfa4d61ebb9243f8ec1fd5271/node_modules/react-native-reanimated`)"
  - "RNScreens (from `../node_modules/.pnpm/react-native-screens@4.16.0_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-screens`)"
  - "RNSVG (from `../node_modules/.pnpm/react-native-svg@15.12.1_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-svg`)"
  - "RNWorklets (from `../node_modules/.pnpm/react-native-worklets@0.5.1_@babel+core@7.28.4_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-worklets`)"
  - "Yoga (from `../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/yoga`)"

SPEC REPOS:
  trunk:
    - libavif
    - libdav1d
    - libwebp
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageSVGCoder
    - SDWebImageWebPCoder

EXTERNAL SOURCES:
  EASClient:
    :path: "../node_modules/.pnpm/expo-eas-client@1.0.7/node_modules/expo-eas-client/ios"
  EXConstants:
    :path: "../node_modules/.pnpm/expo-constants@18.0.9_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0_/node_modules/expo-constants/ios"
  EXJSONUtils:
    :path: "../node_modules/.pnpm/expo-json-utils@0.15.0/node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/.pnpm/expo-manifests@1.0.8_expo@54.0.8/node_modules/expo-manifests/ios"
  Expo:
    :path: "../node_modules/.pnpm/expo@54.0.8_@babel+core@7.28.4_@expo+metro-runtime@6.1.2_expo-router@6.0.6_react-native_c016dcdb98529dc74db267d779f9928f/node_modules/expo"
  ExpoAsset:
    :path: "../node_modules/.pnpm/expo-asset@12.0.8_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../node_modules/.pnpm/expo-blur@15.0.7_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-blur/ios"
  ExpoFileSystem:
    :path: "../node_modules/.pnpm/expo-file-system@19.0.14_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0_/node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/.pnpm/expo-font@14.0.8_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-font/ios"
  ExpoHaptics:
    :path: "../node_modules/.pnpm/expo-haptics@15.0.7_expo@54.0.8/node_modules/expo-haptics/ios"
  ExpoHead:
    :path: "../node_modules/.pnpm/expo-router@6.0.6_@expo+metro-runtime@6.1.2_expo-constants@18.0.9_expo-linking@8.0.8_ex_2e7547a3971f9342be3267eaf914fb42/node_modules/expo-router/ios"
  ExpoImage:
    :path: "../node_modules/.pnpm/expo-image@3.0.8_expo@54.0.8_react-native-web@0.21.1_react-dom@19.1.0_react@19.1.0__rea_c42be68c7e2ba5d74956381944527e15/node_modules/expo-image/ios"
  ExpoKeepAwake:
    :path: "../node_modules/.pnpm/expo-keep-awake@15.0.7_expo@54.0.8_react@19.1.0/node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/.pnpm/expo-linear-gradient@15.0.7_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-linear-gradient/ios"
  ExpoLinking:
    :path: "../node_modules/.pnpm/expo-linking@8.0.8_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-linking/ios"
  ExpoModulesCore:
    :path: "../node_modules/.pnpm/expo-modules-core@3.0.16_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-modules-core"
  EXStructuredHeaders:
    :path: "../node_modules/.pnpm/expo-structured-headers@5.0.0/node_modules/expo-structured-headers/ios"
  EXUpdates:
    :path: "../node_modules/.pnpm/expo-updates@29.0.10_expo@54.0.8_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/expo-updates/ios"
  EXUpdatesInterface:
    :path: "../node_modules/.pnpm/expo-updates-interface@2.0.0_expo@54.0.8/node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/FBLazyVector"
  hermes-engine:
    :podspec: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-07-07-RNv0.81.0-e0fc67142ec0763c6b6153ca2bf96df815539782
  RCTDeprecation:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/"
  React-Core-prebuilt:
    :podspec: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React-Core-prebuilt.podspec"
  React-CoreModules:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/.pnpm/react-native-safe-area-context@5.6.1_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/debug"
  React-RuntimeApple:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon"
  ReactNativeDependencies:
    :podspec: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/third-party-podspecs/ReactNativeDependencies.podspec"
  RNGestureHandler:
    :path: "../node_modules/.pnpm/react-native-gesture-handler@2.28.0_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/.pnpm/react-native-reanimated@4.1.0_@babel+core@7.28.4_react-native-worklets@0.5.1_@babel+cor_0e74ef6bfa4d61ebb9243f8ec1fd5271/node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/.pnpm/react-native-screens@4.16.0_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/.pnpm/react-native-svg@15.12.1_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-svg"
  RNWorklets:
    :path: "../node_modules/.pnpm/react-native-worklets@0.5.1_@babel+core@7.28.4_react-native@0.81.4_@babel+core@7.28.4_react@19.1.0__react@19.1.0/node_modules/react-native-worklets"
  Yoga:
    :path: "../node_modules/.pnpm/react-native@0.81.4_@babel+core@7.28.4_react@19.1.0/node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  EASClient: 68127f1248d2b25fdc82dbbfb17be95d1c4700be
  EXConstants: a95804601ee4a6aa7800645f9b070d753b1142b3
  EXJSONUtils: 1d3e4590438c3ee593684186007028a14b3686cd
  EXManifests: 224345a575fca389073c416297b6348163f28d1a
  Expo: 4875e1cc72ae1398e33bcd6632943a4e93926ecd
  ExpoAsset: 84810d6fed8179f04d4a7a4a6b37028bbd726e26
  ExpoBlur: 2dd8f64aa31f5d405652c21d3deb2d2588b1852f
  ExpoFileSystem: 4fb06865906e781329eb67166bd64fc4749c3019
  ExpoFont: 86ceec09ffed1c99cfee36ceb79ba149074901b5
  ExpoHaptics: 807476b0c39e9d82b7270349d6487928ce32df84
  ExpoHead: 78f14a8573ae5b882123b272c0af20a80bfa58f6
  ExpoImage: e88f500585913969b930e13a4be47277eb7c6de8
  ExpoKeepAwake: 1a2e820692e933c94a565ec3fbbe38ac31658ffe
  ExpoLinearGradient: a464898cb95153125e3b81894fd479bcb1c7dd27
  ExpoLinking: f051f28e50ea9269ff539317c166adec81d9342d
  ExpoModulesCore: a28d1b4a5a9fbdacc41ca8d0c777a2d73496b022
  EXStructuredHeaders: c951e77f2d936f88637421e9588c976da5827368
  EXUpdates: 17123d23c68b75fc4dc484078bc1f98e8699e667
  EXUpdatesInterface: 5adf50cb41e079c861da6d9b4b954c3db9a50734
  FBLazyVector: 9e0cd874afd81d9a4d36679daca991b58b260d42
  hermes-engine: 35c763d57c9832d0eef764316ca1c4d043581394
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libdav1d: 23581a4d8ec811ff171ed5e2e05cd27bad64c39f
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  RCTDeprecation: 7487d6dda857ccd4cb3dd6ecfccdc3170e85dcbc
  RCTRequired: 54128b7df8be566881d48c7234724a78cb9b6157
  RCTTypeSafety: d2b07797a79e45d7b19e1cd2f53c79ab419fe217
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  React: 2073376f47c71b7e9a0af7535986a77522ce1049
  React-callinvoker: 00fa0972a70df7408a4f088144b67207b157e386
  React-Core: d375dd308561785c739a621a21802e5e7e047dee
  React-Core-prebuilt: dde79b89f8863efebb1d532a3335f472927da669
  React-CoreModules: 3eb9b1410a317987c557afc683cc50099562c91d
  React-cxxreact: 724210b64158d97f150d8d254a7319e73ef77ee7
  React-debug: c01d176522cf57cdc4a4a66d1974968fcf497f32
  React-defaultsnativemodule: 3953ff49013fa997e72586628e1d218fdaf3abdb
  React-domnativemodule: 540b9c7a8f31b6f4ed449aafd3a272e1f1107089
  React-Fabric: 00b792be016edad758a63c4ebac15e01d35f6355
  React-FabricComponents: 16ebdb9245d91ec27985a038d0a6460f499db54e
  React-FabricImage: 2a967b5f0293c1c49ec883babfd4992d161e3583
  React-featureflags: 4150b4ddac8210b1e3c538cfb455050b5ee05d8d
  React-featureflagsnativemodule: ff977040205b96818ac1f884846493cb8a2aca28
  React-graphics: ec689ac1c13a9ddb1af83baf195264676ecdbeb6
  React-hermes: ff60a3407f27f3fc82f661774a7ab6559a24ab69
  React-idlecallbacksnativemodule: 5f5ce3c424941f77da4ac3adba681149e68b1221
  React-ImageManager: 8d87296a86f9ee290c1d32c68c7be1be63492467
  React-jserrorhandler: 072756f12136284c86e96c33cdfece4d7286a99f
  React-jsi: b507852b42a9125dffbf6ae7a33792fb521b29a2
  React-jsiexecutor: f970eed6debb91fe5d5d6cb5734d39cf86c59896
  React-jsinspector: 766e113e9482b22971b30236d10c04d8af38269e
  React-jsinspectorcdp: 5b60350e29fe2566d9ed9799858c04b8e6095a3e
  React-jsinspectornetwork: b3cc9a20c6b270f792eaaaa14313019a031b327d
  React-jsinspectortracing: d99120fcf0864209c45cefbc9fc4605c8189c0ef
  React-jsitooling: 9e41724cc47feadefbede31ca91d70f6ff079656
  React-jsitracing: ca020d934502de8e02cccf451501434a5e584027
  React-logger: 7b234de35acb469ce76d6bbb0457f664d6f32f62
  React-Mapbuffer: fbe1da882a187e5898bdf125e1cc6e603d27ecae
  React-microtasksnativemodule: 76905804171d8ccbe69329fc84c57eb7934add7f
  react-native-safe-area-context: 42a1b4f8774b577d03b53de7326e3d5757fe9513
  React-NativeModulesApple: a9464983ccc0f66f45e93558671f60fc7536e438
  React-oscompat: 73db7dbc80edef36a9d6ed3c6c4e1724ead4236d
  React-perflogger: 123272debf907cc423962adafcf4513320e43757
  React-performancetimeline: 095146e4dc8fa4568e44d7a9debc134f27e103f9
  React-RCTActionSheet: 9fc2a0901af63cefe09c8df95a08c2cf8bb7797b
  React-RCTAnimation: 785e743e489bc7aec14415dbc15f4f275b2c0276
  React-RCTAppDelegate: 0602c9e13130edcde4661ea66d11122a3a66f11a
  React-RCTBlob: ae53b7508a5ced43378de2a88816f63423df1f24
  React-RCTFabric: 687a0cfb5726adea7fac63560b04410c86d97134
  React-RCTFBReactNativeSpec: 7c55cf4fb4d2baad32ce3850b8504a6ee22e11ce
  React-RCTImage: f45474c75cdf1526114f75b27e86d004aa171b90
  React-RCTLinking: 56622ff97570e15e01dd9b5a657010c756a9e2d8
  React-RCTNetwork: 3fffa1ab5d6981f839e7679d56f8cb731ba92c07
  React-RCTRuntime: f38c04f744596fc8e1b4c5f6a57fc05c26955257
  React-RCTSettings: f4a8e1bd36f58ec8273c73d3deefdcf90143ac6a
  React-RCTText: da852a51dd1d169b38136a4f4d1eaed35376556b
  React-RCTVibration: ff92ef336e32e18efff0fa83c798a2dbbebe09bd
  React-rendererconsistency: b83b300e607f4e30478a5c3365e260a760232b04
  React-renderercss: aa6a3cdd4fa4e3726123c42b49ba4dd978f81688
  React-rendererdebug: 6b12a782caf2e7e2f730434264357b7b6aed1781
  React-RuntimeApple: 8934aab108dcab957a87208fef4b6f1b3a04973a
  React-RuntimeCore: 1d4345561ecc402e9e88b38e1d9b059a7a13b113
  React-runtimeexecutor: a9a059f222e4d78f45a4e92cada48a5fde989fb8
  React-RuntimeHermes: 05b955709a75038d282a9420342d7bea5857768a
  React-runtimescheduler: 4ce23c9157b51101092537d4171ea4de48a5b863
  React-timing: 62441edf291b91ab5b96ab8f2f8fb648c063ce6f
  React-utils: 485abe7eaefa04b20e0ef442593e022563a1419b
  ReactAppDependencyProvider: 433ddfb4536948630aadd5bd925aff8a632d2fe3
  ReactCodegen: e0e50393383b1b6a1dccecbed06b5472dc9b416d
  ReactCommon: 149b6c05126f2e99f2ed0d3c63539369546f8cae
  ReactNativeDependencies: ed6d1e64802b150399f04f1d5728ec16b437251e
  RNGestureHandler: 2914750df066d89bf9d8f48a10ad5f0051108ac3
  RNReanimated: cec184d046a6ff01473eb59607199eb193a498c8
  RNScreens: d8d6f1792f6e7ac12b0190d33d8d390efc0c1845
  RNSVG: 31d6639663c249b7d5abc9728dde2041eb2a3c34
  RNWorklets: 6ad55640178df6854b98bd63c956aa8d3368532e
  SDWebImage: 9f177d83116802728e122410fb25ad88f5c7608a
  SDWebImageAVIFCoder: afe194a084e851f70228e4be35ef651df0fc5c57
  SDWebImageSVGCoder: 15a300a97ec1c8ac958f009c02220ac0402e936c
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  Yoga: 051f086b5ccf465ff2ed38a2cf5a558ae01aaaa1

PODFILE CHECKSUM: 9ed6f33ebaf388f045ed3d876a129ecf6cc205c8

COCOAPODS: 1.16.2
