<html lang="en"><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>ALIAS — Storyboard</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&amp;family=Manrope:wght@400;500;600&amp;display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<style>
@keyframes neonPulse {
0%, 100% {
box-shadow:
inset 0 0 0 1px rgba(255,255,255,0.1),
inset 0 2px 4px rgba(0,0,0,0.3),
inset 0 0 15px rgba(6,182,212,0.3),
inset 0 0 25px rgba(59,130,246,0.2);
}
50% {
box-shadow:
inset 0 0 0 1px rgba(255,255,255,0.15),
inset 0 2px 4px rgba(0,0,0,0.3),
inset 0 0 25px rgba(6,182,212,0.5),
inset 0 0 40px rgba(59,130,246,0.3),
inset 0 0 60px rgba(6,182,212,0.2);
}
}
/* Outer edge glow off */
.neon-border { animation: none !important; box-shadow: none !important; }
@keyframes entranceSlideUp {
0% { opacity: 0; transform: translateY(80px) scale(0.95); }
100% { opacity: 1; transform: translateY(0) scale(1); }
}
.entrance-animation-1 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.2s; opacity: 0; }
.entrance-animation-2 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.4s; opacity: 0; }
.entrance-animation-3 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.6s; opacity: 0; }
</style></head>
  <body class="min-h-screen bg-neutral-950 text-neutral-100 antialiased" style="font-family: Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, Apple Color Emoji, Segoe UI Emoji;">
    <!-- Backdrop -->
    <div class="pointer-events-none fixed inset-0 -z-10">
      <div class="absolute -top-32 left-1/2 -translate-x-1/2 w-[1200px] h-[1200px] bg-gradient-to-br from-cyan-400/20 via-blue-500/10 to-cyan-500/20 blur-3xl rounded-full"></div>
    </div>

    <!-- Top Nav -->
    <header class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-neutral-950/60 bg-neutral-950/80 border-b border-white/10">
      <div class="max-w-[1600px] mx-auto px-4 sm:px-6">
        <div class="h-14 flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="size-8 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 shadow-[0_0_24px_rgba(34,211,238,0.35)] flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-4.5 h-4.5"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
            </div>
            <div class="text-[15px] tracking-tight">ALIAS</div>
            <div class="text-neutral-600">/</div>
            <div class="text-[14px] text-neutral-300">Mobile v1</div>
            <div class="hidden md:flex items-center gap-2 ml-4 text-[12px] text-neutral-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="map" class="lucide lucide-map w-4 h-4"><path d="M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z"></path><path d="M15 5.764v15"></path><path d="M9 3.236v15"></path></svg>
              <span class="">Storyboard</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <button class="hidden sm:flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="zoom-in" class="lucide lucide-zoom-in w-4.5 h-4.5"><circle cx="11" cy="11" r="8"></circle><line x1="21" x2="16.65" y1="21" y2="16.65"></line><line x1="11" x2="11" y1="8" y2="14"></line><line x1="8" x2="14" y1="11" y2="11"></line></svg>
              Zoom
            </button>
            <button class="hidden md:flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="share" class="lucide lucide-share w-4.5 h-4.5"><path d="M12 2v13"></path><path d="m16 6-4-4-4 4"></path><path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path></svg>
              Export
            </button>
            <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="more-horizontal" class="lucide lucide-more-horizontal w-4.5 h-4.5 text-neutral-300"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <main class="w-full">
      <div class="max-w-[1800px] mx-auto">
        <div class="flex">
          <!-- Left Rail -->
          <aside class="hidden lg:block w-64 shrink-0 border-r border-white/10 min-h-[calc(100vh-56px)]">
            <div class="p-4">
              <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Flows</div>
              <nav class="space-y-1">
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg bg-neutral-900/60 border border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="workflow" class="lucide lucide-workflow w-4.5 h-4.5 text-cyan-300"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg>
                  Core: Tailor → Toolbox → Chat
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="log-in" class="lucide lucide-log-in w-4.5 h-4.5 text-neutral-400"><path d="m10 17 5-5-5-5"></path><path d="M15 12H3"></path><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path></svg>
                  Onboarding
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="house" class="lucide lucide-house w-4.5 h-4.5 text-neutral-400"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>
                  Home
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="audio-lines" class="lucide lucide-audio-lines w-4.5 h-4.5 text-neutral-400"><path d="M2 10v3"></path><path d="M6 6v11"></path><path d="M10 3v18"></path><path d="M14 8v7"></path><path d="M18 5v13"></path><path d="M22 10v3"></path></svg>
                  Voice Picker
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="target" class="lucide lucide-target w-4.5 h-4.5 text-neutral-400"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg>
                  Goal Setup
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bell" class="lucide lucide-bell w-4.5 h-4.5 text-neutral-400"><path d="M10.268 21a2 2 0 0 0 3.464 0"></path><path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"></path></svg>
                  Notifications
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="settings" class="lucide lucide-settings w-4.5 h-4.5 text-neutral-400"><path d="M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915"></path><circle cx="12" cy="12" r="3"></circle></svg>
                  Settings
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="credit-card" class="lucide lucide-credit-card w-4.5 h-4.5 text-neutral-400"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg>
                  Billing
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="history" class="lucide lucide-history w-4.5 h-4.5 text-neutral-400"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M12 7v5l4 2"></path></svg>
                  History
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="shield-alert" class="lucide lucide-shield-alert w-4.5 h-4.5 text-neutral-400"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg>
                  Error States
                </a>
              </nav>
            </div>
            <div class="px-4">
              <div class="h-px w-full bg-gradient-to-r from-transparent via-white/10 to-transparent my-3"></div>
              <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Artifacts</div>
              <nav class="space-y-1">
                <div class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 text-[13px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="library" class="lucide lucide-library w-4.5 h-4.5 text-neutral-400"><path d="m16 6 4 14"></path><path d="M12 6v14"></path><path d="M8 8v12"></path><path d="M4 4v16"></path></svg>
                  Components
                </div>
                <div class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 text-[13px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="list-tree" class="lucide lucide-list-tree w-4.5 h-4.5 text-neutral-400"><path d="M21 12h-8"></path><path d="M21 6H8"></path><path d="M21 18h-8"></path><path d="M3 6v4c0 1.1.9 2 2 2h3"></path><path d="M3 10v6c0 1.1.9 2 2 2h3"></path></svg>
                  User Journeys
                </div>
              </nav>
            </div>
          </aside>

          <!-- Storyboard Canvas -->
          <section class="flex-1 min-h-[calc(100vh-56px)]">
            <div class="sm:px-8 pt-6 pr-4 pb-6 pl-4">
              <div class="flex items-center justify-between">
  <div class="">
    <h1 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent">Storyboard</h1>
    <p class="text-[13px] text-neutral-400">High-level flow with in-progress frames</p>
  </div>
  <div class="hidden sm:flex items-center gap-2">
    <div class="h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] flex items-center gap-2">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="grid-2x2-check" class="lucide lucide-grid-2x2-check w-4.5 h-4.5 text-neutral-300"><path d="M12 3v17a1 1 0 0 1-1 1H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6a1 1 0 0 1-1 1H3"></path><path d="m16 19 2 2 4-4"></path></svg>
      Snap to grid
    </div>

    <!-- Device selector -->
    <div class="h-9 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] flex items-center p-1 gap-1">
      <button class="h-7 px-2.5 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 text-white border border-cyan-300/60 flex items-center gap-1.5">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="smartphone" class="lucide lucide-smartphone w-4 h-4 text-white"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg>
        <span class="hidden xl:block">Mobile</span>
        <span class="hidden lg:block text-white/80">390×844</span>
      </button>
      <button class="h-7 px-2.5 rounded-lg border border-white/10 text-neutral-300 hover:border-cyan-400/40 flex items-center gap-1.5">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="tablet" class="lucide lucide-tablet w-4 h-4 text-neutral-300"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><line x1="12" x2="12.01" y1="18" y2="18"></line></svg>
        <span class="hidden xl:block">Tablet</span>
        <span class="hidden lg:block text-neutral-400">768×1024</span>
      </button>
      <button class="h-7 px-2.5 rounded-lg border border-white/10 text-neutral-300 hover:border-cyan-400/40 flex items-center gap-1.5">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="monitor" class="lucide lucide-monitor w-4 h-4 text-neutral-300"><rect width="20" height="14" x="2" y="3" rx="2"></rect><line x1="8" x2="16" y1="21" y2="21"></line><line x1="12" x2="12" y1="17" y2="21"></line></svg>
        <span class="hidden xl:block">Desktop</span>
        <span class="hidden lg:block text-neutral-400">1280×800</span>
      </button>
    </div>

    <div class="h-9 text-[13px] flex gap-2 bg-neutral-900/60 border-white/10 border rounded-xl pr-3 pl-3 items-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="ruler" class="lucide lucide-ruler w-4.5 h-4.5 text-neutral-300"><path d="M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z"></path><path d="m14.5 12.5 2-2"></path><path d="m11.5 9.5 2-2"></path><path d="m8.5 6.5 2-2"></path><path d="m17.5 15.5 2-2"></path></svg>
      390 × 844
    </div>
  </div>
</div>

              <!-- Canvas backdrop -->
              <div class="mt-5 rounded-2xl border border-white/10 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.04)_1px,transparent_1px),linear-gradient(to_right,rgba(255,255,255,0.04)_1px,transparent_1px)] bg-[size:32px_32px] p-4 sm:p-6">
                <!-- Lane: Core Flow -->
                <div class="">
                  <div class="flex items-center gap-2 mb-4">
                    <div class="size-2 rounded-full bg-cyan-400"></div>
                    <div class="text-[13px] text-neutral-300 tracking-tight">Core Flow</div>
                    <div class="text-[12px] text-neutral-500">Tailor → Toolbox → Conversation</div>
                  </div>

                  <div class="relative">
                    <div class="flex gap-6 overflow-x-auto pb-2 items-start">
                      <!-- Screen 4 — Tailor Your Agent -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="lucide lucide-signal w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="lucide lucide-wifi w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="text-center mt-2">
                                <h2 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(6,182,212,0.45)]">Tailor Your Agent</h2>
                                <p class="text-[13px] text-neutral-400 mt-2">Customize appearance, voice, and capabilities</p>
                              </div>

                              <!-- Avatar Orb -->
                              <div class="relative mx-auto mt-7">
                                <div class="absolute inset-0 -z-10 blur-2xl bg-gradient-to-br from-cyan-500/35 via-blue-500/25 to-cyan-400/30 rounded-full w-64 h-64"></div>
                                <div class="relative w-64 h-64 rounded-full overflow-hidden border border-cyan-400/30 bg-gradient-to-br from-neutral-900 via-neutral-950 to-black shadow-[0_0_40px_4px_rgba(34,211,238,0.25)]">
                                  <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,211,238,0.35),transparent_60%),radial-gradient(circle_at_70%_70%,rgba(59,130,246,0.35),transparent_55%)]"></div>
                                  <div class="absolute inset-0 mix-blend-screen opacity-70">
                                    <video autoplay="" muted="" loop="" playsinline="" class="w-full h-full object-cover" style="filter: brightness(1.05) contrast(1.1) saturate(1.2);">
                                      <source src="https://cdn.midjourney.com/video/5029e61e-010a-4f9f-8daf-ef0a0ff34af6/3.mp4" type="video/mp4">
                                    </video>
                                  </div>
                                  <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="p-4 rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 shadow-[0_0_24px_8px_rgba(34,211,238,0.35)]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-8 h-8"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                    </div>
                                  </div>
                                </div>

                                <!-- Customization quick picks -->
                                <div class="flex items-center justify-center gap-3 mt-4">
                                  <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="lucide lucide-bot w-5 h-5 text-cyan-300"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                  </button>
                                  <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="headphones" class="lucide lucide-headphones w-5 h-5 text-blue-300"><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"></path></svg>
                                  </button>
                                  <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wand-2" class="lucide lucide-wand-2 w-5 h-5 text-cyan-200"><path d="m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72"></path><path d="m14 7 3 3"></path><path d="M5 6v4"></path><path d="M19 14v4"></path><path d="M10 2v2"></path><path d="M7 8H3"></path><path d="M21 16h-4"></path><path d="M11 3H9"></path></svg>
                                  </button>
                                </div>
                              </div>

                              <!-- Stepper -->
                              <div class="mt-7">
                                <div class="flex items-center justify-between">
                                  <div class="flex-1 flex items-center">
                                    <div class="size-7 rounded-full bg-gradient-to-br from-cyan-500 to-blue-500 text-white text-[12px] flex items-center justify-center shadow-[0_0_16px_rgba(34,211,238,0.5)]">1</div>
                                    <div class="h-0.5 flex-1 mx-2 bg-gradient-to-r from-cyan-500/50 to-blue-500/40"></div>
                                    <div class="size-7 rounded-full bg-neutral-900/70 border border-white/10 text-[12px] text-neutral-300 flex items-center justify-center">2</div>
                                    <div class="h-0.5 flex-1 mx-2 bg-white/10"></div>
                                    <div class="size-7 rounded-full bg-neutral-900/70 border border-white/10 text-[12px] text-neutral-300 flex items-center justify-center">3</div>
                                  </div>
                                </div>
                                <div class="mt-2 flex items-center justify-between text-[11px] text-neutral-400 px-1">
                                  <span>Choose Skills</span>
                                  <span>Pick Voice</span>
                                  <span>Set Goals</span>
                                </div>
                              </div>

                              <!-- Skills -->
                              <div class="mt-5">
                                <div class="text-[13px] text-neutral-300 mb-2">Example skills</div>
                                <div class="flex flex-wrap gap-2.5">
                                  <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition shadow-[0_0_12px_rgba(34,211,238,0.25)]">
                                    <div class="flex items-center gap-1.5">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="search" class="lucide lucide-search w-3.5 h-3.5"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg>
                                      <span>Research</span>
                                    </div>
                                  </button>
                                  <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition">
                                    <div class="flex items-center gap-1.5">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="workflow" class="lucide lucide-workflow w-3.5 h-3.5"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg>
                                      <span>Project Management</span>
                                    </div>
                                  </button>
                                  <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition">
                                    <div class="flex items-center gap-1.5">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="headset" class="lucide lucide-headset w-3.5 h-3.5"><path d="M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z"></path><path d="M21 16v2a4 4 0 0 1-4 4h-5"></path></svg>
                                      <span>Customer Support</span>
                                    </div>
                                  </button>
                                </div>
                              </div>

                              <!-- CTA -->
                              <div class="mt-auto">
                                <button class="w-full h-14 rounded-2xl bg-gradient-to-r from-cyan-500 to-blue-500 text-[15px] tracking-tight text-white border border-cyan-300/60 shadow-[0_10px_40px_-8px_rgba(34,211,238,0.55)] hover:shadow-[0_10px_50px_-6px_rgba(34,211,238,0.75)] transition">
                                  Deploy My Agent
                                </button>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">1 · Tailor Your Agent</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="arrow-right" class="lucide lucide-arrow-right w-5 h-5 text-neutral-300"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                      <!-- Screen 5 — ALIAS Toolbox -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 rounded-[3rem] p-[2px]">
                          <div class="overflow-hidden h-[844px] relative bg-black rounded-[2.9rem] neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="lucide lucide-signal w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="lucide lucide-wifi w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="flex items-center justify-between mb-4">
                                <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-cyan-300 via-cyan-200 to-blue-300 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(6,182,212,0.45)]">ALIAS Toolbox</h2>
                                <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sliders" class="lucide lucide-sliders w-4.5 h-4.5 text-neutral-300"><line x1="4" x2="4" y1="21" y2="14"></line><line x1="4" x2="4" y1="10" y2="3"></line><line x1="12" x2="12" y1="21" y2="12"></line><line x1="12" x2="12" y1="8" y2="3"></line><line x1="20" x2="20" y1="21" y2="16"></line><line x1="20" x2="20" y1="12" y2="3"></line><line x1="2" x2="6" y1="14" y2="14"></line><line x1="10" x2="14" y1="8" y2="8"></line><line x1="18" x2="22" y1="16" y2="16"></line></svg>
                                </button>
                              </div>

                              <!-- Tools Grid -->
                              <div class="grid grid-cols-2 gap-3">
                                <!-- Finance Tracker -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-green-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-green-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-green-400/20 to-emerald-400/10 border border-green-300/30 shadow-[0_0_24px_rgba(74,222,128,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wallet" class="lucide lucide-wallet w-5 h-5 text-green-300"><path d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1"></path><path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"></path></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Finance Tracker</div>
                                    <div class="text-[12px] text-neutral-400">Spending insights</div>
                                  </div>
                                </div>

                                <!-- Task Automator -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-cyan-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-cyan-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-cyan-400/20 to-blue-400/10 border border-cyan-300/30 shadow-[0_0_24px_rgba(34,211,238,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="lucide lucide-bot w-5 h-5 text-cyan-300"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Task Automator</div>
                                    <div class="text-[12px] text-neutral-400">Workflow scripts</div>
                                  </div>
                                </div>

                                <!-- Meeting Summarizer -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-blue-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-blue-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-blue-400/20 to-cyan-400/10 border border-blue-300/30 shadow-[0_0_24px_rgba(59,130,246,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="file-text" class="lucide lucide-file-text w-5 h-5 text-blue-300"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Meeting Summarizer</div>
                                    <div class="text-[12px] text-neutral-400">Notes &amp; action items</div>
                                  </div>
                                </div>

                                <!-- Idea Generator -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-purple-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-purple-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-purple-400/20 to-fuchsia-400/10 border border-purple-300/30 shadow-[0_0_24px_rgba(168,85,247,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-5 h-5 text-purple-300"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Idea Generator</div>
                                    <div class="text-[12px] text-neutral-400">Creative prompts</div>
                                  </div>
                                </div>
                              </div>

                              <!-- Subscription Banner -->
                              <div class="mt-auto">
                                <div class="mt-6 p-4 rounded-2xl bg-gradient-to-br from-neutral-900/70 to-black/80 border border-white/10 backdrop-blur-xl relative overflow-hidden">
                                  <div class="absolute inset-0 bg-[radial-gradient(600px_200px_at_50%_-20%,rgba(34,211,238,0.2),transparent)]"></div>
                                  <div class="flex items-center justify-between relative z-10">
                                    <div>
                                      <div class="text-[14px] text-neutral-100 tracking-tight">Unlock the full toolbox</div>
                                      <div class="text-[12px] text-neutral-400">Advanced agents, priority compute</div>
                                    </div>
                                    <button class="px-3.5 h-10 rounded-xl bg-gradient-to-r from-cyan-500 to-blue-500 text-white text-[13px] border border-cyan-300/60 shadow-[0_10px_30px_-10px_rgba(34,211,238,0.65)] hover:shadow-[0_10px_40px_-8px_rgba(34,211,238,0.75)]">
                                      Upgrade to Pro
                                    </button>
                                  </div>
                                </div>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">2 · Toolbox</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="arrow-right" class="lucide lucide-arrow-right w-5 h-5 text-neutral-300"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                                           <!-- Screen 6 — Conversation -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-4 pr-4 pb-6 pl-4">
                              <!-- Chat Header -->
                              <div class="flex items-center gap-3 px-2 py-2">
                                <div class="size-9 rounded-xl bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 shadow-[0_0_16px_rgba(34,211,238,0.35)] flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                </div>
                                <div>
                                  <div class="text-[14px] tracking-tight text-neutral-100">Conversation</div>
                                  <div class="text-[12px] text-neutral-500">Connected · Realtime</div>
                                </div>
                                <div class="ml-auto flex items-center gap-2">
                                  <button class="size-9 rounded-xl bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="New chat">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 text-neutral-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 5v14"></path><path d="M5 12h14"></path></svg>
                                  </button>
                                  <button class="size-9 rounded-xl bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="More">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4.5 h-4.5 text-neutral-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
                                  </button>
                                </div>
                              </div>

                              <!-- Messages -->
                              <div class="mt-2 flex-1 overflow-y-auto space-y-3 px-2">
                                <!-- System bubble -->
                                <div class="flex items-start gap-2">
                                  <div class="size-7 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 flex items-center justify-center shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path></svg>
                                  </div>
                                  <div class="max-w-[78%] rounded-2xl rounded-tl-sm bg-neutral-900/60 border border-white/10 p-3">
                                    <p class="text-[13px] text-neutral-200">Hi! I’m your ALIAS. I can manage tasks, search, and create. What can I help with today?</p>
                                  </div>
                                </div>

                                <!-- User bubble -->
                                <div class="flex items-end gap-2 justify-end">
                                  <div class="max-w-[78%] rounded-2xl rounded-tr-sm bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 p-3">
                                    <p class="text-[13px] text-neutral-100">Summarize this week’s meetings and create an action list.</p>
                                  </div>
                                </div>

                                <!-- Assistant bubble with card -->
                                <div class="flex items-start gap-2">
                                  <div class="size-7 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 flex items-center justify-center shrink-0">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect></svg>
                                  </div>
                                  <div class="max-w-[78%] space-y-2">
                                    <div class="rounded-2xl bg-neutral-900/60 border border-white/10 p-3">
                                      <p class="text-[13px] text-neutral-200">Here’s a concise summary and prioritized actions:</p>
                                      <ul class="mt-2 text-[13px] text-neutral-300 list-disc pl-4 space-y-1">
                                        <li>Finalize Q3 KPI dashboards by Friday</li>
                                        <li>Draft vendor RFP outline</li>
                                        <li>Schedule team retro for Tue 10am</li>
                                      </ul>
                                    </div>
                                    <div class="flex gap-2">
                                      <button class="px-3 h-9 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-200 hover:border-cyan-400/40 transition">Add to Tasks</button>
                                      <button class="px-3 h-9 rounded-xl bg-gradient-to-r from-cyan-500 to-blue-500 text-white text-[12px] border border-cyan-300/60">Open in Toolbox</button>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Composer -->
                              <div class="mt-3 px-2">
                                <div class="h-14 rounded-2xl bg-neutral-900/60 border border-white/10 flex items-center px-2 gap-2">
                                  <button class="size-10 rounded-xl bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="Attach">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-neutral-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M13.5 6.5 7 13a4 4 0 1 0 5.66 5.66L19 12.33a6 6 0 1 0-8.49-8.49L5 9.35"></path></svg>
                                  </button>
                                  <input class="flex-1 bg-transparent outline-none text-[13px] placeholder:text-neutral-500" placeholder="Type a message or use the mic…">
                                  <button class="size-10 rounded-xl bg-gradient-to-br from-cyan-500 to-blue-600 text-white border border-cyan-300/60 flex items-center justify-center shadow-[0_0_16px_rgba(34,211,238,0.4)] hover:shadow-[0_0_26px_rgba(34,211,238,0.55)] transition" aria-label="Voice">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5"><path d="M12 1v11"></path><rect x="7" y="8" width="10" height="6" rx="3"></rect><path d="M5 10v2a7 7 0 0 0 14 0v-2"></path><path d="M12 19v4"></path></svg>
                                  </button>
                                  <button class="size-10 rounded-xl bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="Send">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-neutral-200" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="m22 2-7 20-4-9-9-4Z"></path><path d="M22 2 11 13"></path></svg>
                                  </button>
                                </div>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">3 · Conversation</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Lane: Voice Assistant -->
                <div class="mt-10">
                  <div class="flex items-center gap-2 mb-4">
                    <div class="size-2 rounded-full bg-fuchsia-400"></div>
                    <div class="text-[13px] text-neutral-300 tracking-tight">Voice Assistant</div>
                    <div class="text-[12px] text-neutral-500">Idle → Listening → Transcribing → Responding</div>
                  </div>

                  <div class="relative">
                    <div class="flex gap-6 overflow-x-auto pb-2 items-start">
                      <!-- VA Screen 1 — Idle -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-fuchsia-400/60 via-purple-500/40 to-fuchsia-500/60 shadow-[0_20px_80px_-12px_rgba(217,70,239,0.35)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-8 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="text-center">
                                <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-fuchsia-300 via-purple-300 to-fuchsia-200 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(217,70,239,0.45)]">ALIAS Voice</h2>
                                <p class="text-[13px] text-neutral-400 mt-1.5">Tap the mic and speak naturally</p>
                              </div>

                              <!-- Idle Orb -->
                              <div class="mt-10 mx-auto relative">
                                <div class="absolute inset-0 -z-10 blur-3xl bg-gradient-to-br from-fuchsia-500/30 via-purple-500/20 to-fuchsia-400/25 rounded-full w-64 h-64"></div>
                                <button class="group relative w-64 h-64 rounded-full border border-fuchsia-300/40 bg-gradient-to-br from-neutral-900 via-neutral-950 to-black overflow-hidden shadow-[0_0_40px_6px_rgba(217,70,239,0.25)] focus:outline-none focus-visible:ring-2 focus-visible:ring-fuchsia-400/70" aria-label="Tap to talk">
                                  <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(217,70,239,0.35),transparent_60%),radial-gradient(circle_at_70%_70%,rgba(168,85,247,0.35),transparent_55%)] transition transform group-active:scale-[1.03]"></div>
                                  <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="p-5 rounded-full bg-gradient-to-br from-fuchsia-500 to-purple-600 border border-white/20 shadow-[0_0_28px_8px_rgba(217,70,239,0.35)] group-active:scale-110 transition">
                                      <svg xmlns="http://www.w3.org/2000/svg" class="w-10 h-10 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 1v11"></path><rect x="7" y="8" width="10" height="6" rx="3"></rect><path d="M5 10v2a7 7 0 0 0 14 0v-2"></path><path d="M12 19v4"></path></svg>
                                    </div>
                                  </div>
                                </button>
                                <div class="mt-4 text-center text-[12px] text-neutral-400">Hold to talk • Release to send</div>
                              </div>

                              <!-- Quick intents -->
                              <div class="mt-10 grid grid-cols-3 gap-2">
                                <button class="h-10 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-300 hover:border-fuchsia-400/40 transition">Summarize</button>
                                <button class="h-10 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-300 hover:border-fuchsia-400/40 transition">Remind me</button>
                                <button class="h-10 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-300 hover:border-fuchsia-400/40 transition">Search</button>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-auto"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">A · Idle</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-neutral-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                      <!-- VA Screen 2 — Listening -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-fuchsia-400/60 via-purple-500/40 to-fuchsia-500/60 shadow-[0_20px_80px_-12px_rgba(217,70,239,0.35)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-8 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="text-center">
                                <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-fuchsia-300 via-purple-300 to-fuchsia-200 bg-clip-text text-transparent">Listening…</h2>
                                <p class="text-[12px] text-neutral-400 mt-1">Speak now</p>
                              </div>

                              <!-- Listening visualizer -->
                              <div class="mt-12 flex-1 flex items-center justify-center">
                                <div class="relative w-64 h-64 rounded-full bg-gradient-to-br from-neutral-900 via-neutral-950 to-black border border-fuchsia-300/30 overflow-hidden">
                                  <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(217,70,239,0.3),transparent_55%),radial-gradient(circle_at_70%_70%,rgba(168,85,247,0.3),transparent_55%)]"></div>
                                  <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="flex items-end gap-1.5">
                                      <span class="w-1.5 h-6 bg-fuchsia-400/70 rounded-full animate-[pulse_1s_ease-in-out_infinite]"></span>
                                      <span class="w-1.5 h-10 bg-purple-400/70 rounded-full animate-[pulse_1.2s_ease-in-out_infinite]"></span>
                                      <span class="w-1.5 h-7 bg-fuchsia-400/70 rounded-full animate-[pulse_0.9s_ease-in-out_infinite]"></span>
                                      <span class="w-1.5 h-12 bg-purple-400/70 rounded-full animate-[pulse_1.1s_ease-in-out_infinite]"></span>
                                      <span class="w-1.5 h-8 bg-fuchsia-400/70 rounded-full animate-[pulse_1.05s_ease-in-out_infinite]"></span>
                                      <span class="w-1.5 h-11 bg-purple-400/70 rounded-full animate-[pulse_1.3s_ease-in-out_infinite]"></span>
                                      <span class="w-1.5 h-7 bg-fuchsia-400/70 rounded-full animate-[pulse_1.15s_ease-in-out_infinite]"></span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Controls -->
                              <div class="mt-6 flex items-center justify-center gap-3">
                                <button class="px-4 h-10 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-300 hover:border-fuchsia-400/40 transition">Cancel</button>
                                <button class="px-4 h-10 rounded-xl bg-gradient-to-r from-fuchsia-500 to-purple-500 text-white text-[12px] border border-fuchsia-300/60">Finish</button>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">B · Listening</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-neutral-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                      <!-- VA Screen 3 — Transcribing -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-fuchsia-400/60 via-purple-500/40 to-fuchsia-500/60 shadow-[0_20px_80px_-12px_rgba(217,70,239,0.35)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:42</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-8 pr-6 pb-6 pl-6">
                              <div class="text-center">
                                <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-fuchsia-300 via-purple-300 to-fuchsia-200 bg-clip-text text-transparent">Transcribing…</h2>
                                <p class="text-[12px] text-neutral-400 mt-1">Generating accurate transcript</p>
                              </div>

                              <!-- Transcript preview -->
                              <div class="mt-6 space-y-3">
                                <div class="p-3 rounded-2xl bg-neutral-900/60 border border-white/10">
                                  <div class="text-[12px] text-neutral-500 mb-1">You</div>
                                  <p class="text-[13px] text-neutral-200">Hey ALIAS, summarize my meetings and make a prioritized to-do list for the team. Also draft an email to stakeholders.</p>
                                </div>
                                <div class="p-3 rounded-2xl bg-neutral-900/40 border border-white/10">
                                  <div class="text-[12px] text-neutral-500 mb-1">Detecting context</div>
                                  <div class="flex items-center gap-1.5 text-[13px] text-neutral-300">
                                    <span class="inline-block size-1.5 rounded-full bg-fuchsia-400 animate-pulse"></span>
                                    <span>Fetching calendar and notes</span>
                                  </div>
                                </div>
                              </div>

                              <!-- Progress -->
                              <div class="mt-6">
                                <div class="h-2 w-full rounded-full bg-white/10 overflow-hidden">
                                  <div class="h-full w-1/2 bg-gradient-to-r from-fuchsia-500 to-purple-500 animate-[pulse_1.4s_ease-in-out_infinite]"></div>
                                </div>
                                <div class="mt-2 text-[12px] text-neutral-400">Transcription 52%</div>
                              </div>

                              <div class="mt-auto flex items-center justify-between">
                                <button class="px-3 h-10 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-300 hover:border-fuchsia-400/40 transition">Edit text</button>
                                <button class="px-3 h-10 rounded-xl bg-gradient-to-r from-fuchsia-500 to-purple-500 text-white text-[12px] border border-fuchsia-300/60">Continue</button>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">C · Transcribing</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-neutral-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                      <!-- VA Screen 4 — Responding -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-fuchsia-400/60 via-purple-500/40 to-fuchsia-500/60 shadow-[0_20px_80px_-12px_rgba(217,70,239,0.35)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:43</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-8 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="text-center">
                                <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-fuchsia-300 via-purple-300 to-fuchsia-200 bg-clip-text text-transparent">Responding</h2>
                                <p class="text-[12px] text-neutral-400 mt-1">AI is preparing your summary</p>
                              </div>

                              <!-- Response card -->
                              <div class="mt-6 rounded-2xl bg-neutral-900/60 border border-white/10 p-4 overflow-hidden relative">
                                <div class="absolute inset-0 bg-[radial-gradient(600px_200px_at_50%_-20%,rgba(217,70,239,0.15),transparent)] pointer-events-none"></div>
                                <div class="text-[12px] text-neutral-500 mb-1">ALIAS</div>
                                <div class="space-y-2">
                                  <p class="text-[13px] text-neutral-200">I drafted a recap and action plan based on your meetings. Shall I send it to the team and add tasks to your board?</p>
                                  <ul class="text-[13px] text-neutral-300 list-disc pl-4">
                                    <li>Q3 KPI dashboard — due Fri</li>
                                    <li>Vendor RFP — outline today</li>
                                    <li>Team retro — Tue 10:00</li>
                                  </ul>
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                  <button class="px-3 h-9 rounded-xl bg-gradient-to-r from-fuchsia-500 to-purple-500 text-white text-[12px] border border-fuchsia-300/60">Send recap</button>
                                  <button class="px-3 h-9 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-200 hover:border-fuchsia-400/40 transition">Review first</button>
                                  <button class="px-3 h-9 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-200 hover:border-fuchsia-400/40 transition">Open transcript</button>
                                </div>
                              </div>

                              <!-- Footer actions -->
                              <div class="mt-auto flex items-center justify-between">
                                <button class="px-4 h-10 rounded-xl bg-neutral-900/60 border border-white/10 text-[12px] text-neutral-300 hover:border-fuchsia-400/40 transition">Discard</button>
                                <button class="px-4 h-10 rounded-xl bg-gradient-to-r from-fuchsia-500 to-purple-500 text-white text-[12px] border border-fuchsia-300/60">Send to Chat</button>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">D · Responding</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div> <!-- /Canvas backdrop -->
            </div>
          </section>
        </div>
      </div>
    </main>
  
</body></html>