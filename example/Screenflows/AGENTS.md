# Repository Guidelines

## Project Structure & Module Organization
- Keep `haus-mobile-screen-flows.html` as the source of truth for HAUS mobile journeys; treat other HTML files as archived references.
- Store any supporting assets under `assets/` (e.g., `assets/images/`, `assets/css/`) and reference them with relative paths so the bundle works offline.
- Avoid generating ad-hoc HTML copies; instead, refresh the primary file or remove stale duplicates before committing.
- Maintain a lightweight directory by excluding tooling outputs, screenshots, or editor caches from version control.

## Build, Test, and Development Commands
- `open haus-mobile-screen-flows.html` (macOS) — launch the flow preview in the default browser for a quick visual check.
- `tidy -qe haus-mobile-screen-flows.html` — report HTML validation errors without rewriting the file; resolve all errors and major warnings.
- `npx prettier --write "*.html"` — normalize indentation, whitespace, and attribute wrapping across all flows when formatting drifts.

## Coding Style & Naming Conventions
- Format HTML with 2-space indentation, UTF-8 encoding, LF endings, and no trailing whitespace to keep diffs clean.
- Use kebab-case for filenames (`haus-storyboard.html`) and in-document IDs/classes (`screen-header`, `flow-step`).
- Prefer semantic HTML and keep inline CSS/JS minimal; place shared styles or scripts in `assets/` and link them relatively.

## Testing Guidelines
- Validate markup with `tidy` or the W3C validator before submitting changes, documenting any unavoidable warnings.
- Conduct an accessibility spot check: confirm heading order, descriptive link text, and sufficient color contrast.
- Capture before/after screenshots for updated flows to support design review and regression tracking.

## Commit & Pull Request Guidelines
- Follow Conventional Commit prefixes (`feat:`, `fix:`, `docs:`, `chore:`, `refactor:`) and keep each commit focused on a discrete change.
- Summarize PRs with the problem solved, highlight affected screens, link issues when available, and note validation steps (validators run, screenshots attached).
- Ensure no secrets or external CDN dependencies are introduced; rely on local assets and relative paths for portability.

## Security & Configuration Tips
- Never embed credentials, analytics IDs, or environment-specific URLs in HTML; scrub exports before committing.
- Optimize and compress large images prior to adding them under `assets/images/` for faster loading and smaller diffs.
- Review relative paths after reorganizing assets to prevent broken references in offline previews.
