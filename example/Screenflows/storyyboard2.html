<html lang="en"><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>ALIAS — Storyboard</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&amp;family=Manrope:wght@400;500;600&amp;display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<style>
@keyframes neonPulse {
0%, 100% {
box-shadow:
inset 0 0 0 1px rgba(255,255,255,0.1),
inset 0 2px 4px rgba(0,0,0,0.3),
inset 0 0 15px rgba(6,182,212,0.3),
inset 0 0 25px rgba(59,130,246,0.2);
}
50% {
box-shadow:
inset 0 0 0 1px rgba(255,255,255,0.15),
inset 0 2px 4px rgba(0,0,0,0.3),
inset 0 0 25px rgba(6,182,212,0.5),
inset 0 0 40px rgba(59,130,246,0.3),
inset 0 0 60px rgba(6,182,212,0.2);
}
}
.neon-border { animation: neonPulse 3s ease-in-out infinite; }
@keyframes entranceSlideUp {
0% { opacity: 0; transform: translateY(80px) scale(0.95); }
100% { opacity: 1; transform: translateY(0) scale(1); }
}
.entrance-animation-1 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.2s; opacity: 0; }
.entrance-animation-2 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.4s; opacity: 0; }
.entrance-animation-3 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.6s; opacity: 0; }
</style></head>
  <body class="min-h-screen bg-neutral-950 text-neutral-100 antialiased" style="font-family: Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, Apple Color Emoji, Segoe UI Emoji;">
    <!-- Backdrop -->
    <div class="pointer-events-none fixed inset-0 -z-10">
      <div class="absolute -top-32 left-1/2 -translate-x-1/2 w-[1200px] h-[1200px] bg-gradient-to-br from-cyan-400/20 via-blue-500/10 to-cyan-500/20 blur-3xl rounded-full"></div>
    </div>

    <!-- Top Nav -->
    <header class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-neutral-950/60 bg-neutral-950/80 border-b border-white/10">
      <div class="max-w-[1600px] mx-auto px-4 sm:px-6">
        <div class="h-14 flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="size-8 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 shadow-[0_0_24px_rgba(34,211,238,0.35)] flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-4.5 h-4.5"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
            </div>
            <div class="text-[15px] tracking-tight">ALIAS</div>
            <div class="text-neutral-600">/</div>
            <div class="text-[14px] text-neutral-300">Mobile v1</div>
            <div class="hidden md:flex items-center gap-2 ml-4 text-[12px] text-neutral-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="map" class="lucide lucide-map w-4 h-4"><path d="M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z"></path><path d="M15 5.764v15"></path><path d="M9 3.236v15"></path></svg>
              <span class="">Storyboard</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <button class="hidden sm:flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="zoom-in" class="lucide lucide-zoom-in w-4.5 h-4.5"><circle cx="11" cy="11" r="8"></circle><line x1="21" x2="16.65" y1="21" y2="16.65"></line><line x1="11" x2="11" y1="8" y2="14"></line><line x1="8" x2="14" y1="11" y2="11"></line></svg>
              Zoom
            </button>
            <button class="hidden md:flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="share" class="lucide lucide-share w-4.5 h-4.5"><path d="M12 2v13"></path><path d="m16 6-4-4-4 4"></path><path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path></svg>
              Export
            </button>
            <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="more-horizontal" class="lucide lucide-more-horizontal w-4.5 h-4.5 text-neutral-300"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <main class="w-full">
      <div class="max-w-[1800px] mx-auto">
        <div class="flex">
          <!-- Left Rail -->
          <aside class="hidden lg:block w-64 shrink-0 border-r border-white/10 min-h-[calc(100vh-56px)]">
            <div class="p-4">
              <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Flows</div>
              <nav class="space-y-1">
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg bg-neutral-900/60 border border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="workflow" class="lucide lucide-workflow w-4.5 h-4.5 text-cyan-300"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg>
                  Core: Tailor → Toolbox → Chat
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="log-in" class="lucide lucide-log-in w-4.5 h-4.5 text-neutral-400"><path d="m10 17 5-5-5-5"></path><path d="M15 12H3"></path><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path></svg>
                  Onboarding
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="house" class="lucide lucide-house w-4.5 h-4.5 text-neutral-400"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>
                  Home
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="audio-lines" class="lucide lucide-audio-lines w-4.5 h-4.5 text-neutral-400"><path d="M2 10v3"></path><path d="M6 6v11"></path><path d="M10 3v18"></path><path d="M14 8v7"></path><path d="M18 5v13"></path><path d="M22 10v3"></path></svg>
                  Voice Picker
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="target" class="lucide lucide-target w-4.5 h-4.5 text-neutral-400"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg>
                  Goal Setup
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bell" class="lucide lucide-bell w-4.5 h-4.5 text-neutral-400"><path d="M10.268 21a2 2 0 0 0 3.464 0"></path><path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"></path></svg>
                  Notifications
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="settings" class="lucide lucide-settings w-4.5 h-4.5 text-neutral-400"><path d="M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915"></path><circle cx="12" cy="12" r="3"></circle></svg>
                  Settings
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="credit-card" class="lucide lucide-credit-card w-4.5 h-4.5 text-neutral-400"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg>
                  Billing
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="history" class="lucide lucide-history w-4.5 h-4.5 text-neutral-400"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M12 7v5l4 2"></path></svg>
                  History
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="shield-alert" class="lucide lucide-shield-alert w-4.5 h-4.5 text-neutral-400"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg>
                  Error States
                </a>
              </nav>
            </div>
            <div class="px-4">
              <div class="h-px w-full bg-gradient-to-r from-transparent via-white/10 to-transparent my-3"></div>
              <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Artifacts</div>
              <nav class="space-y-1">
                <div class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 text-[13px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="library" class="lucide lucide-library w-4.5 h-4.5 text-neutral-400"><path d="m16 6 4 14"></path><path d="M12 6v14"></path><path d="M8 8v12"></path><path d="M4 4v16"></path></svg>
                  Components
                </div>
                <div class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 text-[13px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="list-tree" class="lucide lucide-list-tree w-4.5 h-4.5 text-neutral-400"><path d="M21 12h-8"></path><path d="M21 6H8"></path><path d="M21 18h-8"></path><path d="M3 6v4c0 1.1.9 2 2 2h3"></path><path d="M3 10v6c0 1.1.9 2 2 2h3"></path></svg>
                  User Journeys
                </div>
              </nav>
            </div>
          </aside>

          <!-- Storyboard Canvas -->
          <section class="flex-1 min-h-[calc(100vh-56px)]">
            <div class="sm:px-8 pt-6 pr-4 pb-6 pl-4">
              <div class="flex items-center justify-between">
  <div class="">
    <h1 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent">Storyboard</h1>
    <p class="text-[13px] text-neutral-400">High-level flow with in-progress frames</p>
  </div>
  <div class="hidden sm:flex items-center gap-2">
    <div class="h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] flex items-center gap-2">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="grid-2x2-check" class="lucide lucide-grid-2x2-check w-4.5 h-4.5 text-neutral-300"><path d="M12 3v17a1 1 0 0 1-1 1H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6a1 1 0 0 1-1 1H3"></path><path d="m16 19 2 2 4-4"></path></svg>
      Snap to grid
    </div>

    <!-- Device selector -->
    <div class="h-9 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] flex items-center p-1 gap-1">
      <button class="h-7 px-2.5 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 text-white border border-cyan-300/60 flex items-center gap-1.5">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="smartphone" class="lucide lucide-smartphone w-4 h-4 text-white"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg>
        <span class="hidden xl:block">Mobile</span>
        <span class="hidden lg:block text-white/80">390×844</span>
      </button>
      <button class="h-7 px-2.5 rounded-lg border border-white/10 text-neutral-300 hover:border-cyan-400/40 flex items-center gap-1.5">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="tablet" class="lucide lucide-tablet w-4 h-4 text-neutral-300"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><line x1="12" x2="12.01" y1="18" y2="18"></line></svg>
        <span class="hidden xl:block">Tablet</span>
        <span class="hidden lg:block text-neutral-400">768×1024</span>
      </button>
      <button class="h-7 px-2.5 rounded-lg border border-white/10 text-neutral-300 hover:border-cyan-400/40 flex items-center gap-1.5">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="monitor" class="lucide lucide-monitor w-4 h-4 text-neutral-300"><rect width="20" height="14" x="2" y="3" rx="2"></rect><line x1="8" x2="16" y1="21" y2="21"></line><line x1="12" x2="12" y1="17" y2="21"></line></svg>
        <span class="hidden xl:block">Desktop</span>
        <span class="hidden lg:block text-neutral-400">1280×800</span>
      </button>
    </div>

    <div class="h-9 text-[13px] flex gap-2 bg-neutral-900/60 border-white/10 border rounded-xl pr-3 pl-3 items-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="ruler" class="lucide lucide-ruler w-4.5 h-4.5 text-neutral-300"><path d="M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z"></path><path d="m14.5 12.5 2-2"></path><path d="m11.5 9.5 2-2"></path><path d="m8.5 6.5 2-2"></path><path d="m17.5 15.5 2-2"></path></svg>
      390 × 844
    </div>
  </div>
</div>

              <!-- Canvas backdrop -->
              <div class="mt-5 rounded-2xl border border-white/10 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.04)_1px,transparent_1px),linear-gradient(to_right,rgba(255,255,255,0.04)_1px,transparent_1px)] bg-[size:32px_32px] p-4 sm:p-6">
                <!-- Lane: Core Flow -->
                <div class="">
                  <div class="flex items-center gap-2 mb-4">
                    <div class="size-2 rounded-full bg-cyan-400"></div>
                    <div class="text-[13px] text-neutral-300 tracking-tight">Core Flow</div>
                    <div class="text-[12px] text-neutral-500">Tailor → Toolbox → Conversation</div>
                  </div>

                  <div class="relative">
                    <div class="flex gap-6 overflow-x-auto pb-2 items-start">
                      <!-- Screen 4 — Tailor Your Agent -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="lucide lucide-signal w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="lucide lucide-wifi w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="text-center mt-2">
                                <h2 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(6,182,212,0.45)]">Tailor Your Agent</h2>
                                <p class="text-[13px] text-neutral-400 mt-2">Customize appearance, voice, and capabilities</p>
                              </div>

                              <!-- Avatar Orb -->
                              <div class="relative mx-auto mt-7">
                                <div class="absolute inset-0 -z-10 blur-2xl bg-gradient-to-br from-cyan-500/35 via-blue-500/25 to-cyan-400/30 rounded-full w-64 h-64"></div>
                                <div class="relative w-64 h-64 rounded-full overflow-hidden border border-cyan-400/30 bg-gradient-to-br from-neutral-900 via-neutral-950 to-black shadow-[0_0_40px_4px_rgba(34,211,238,0.25)]">
                                  <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,211,238,0.35),transparent_60%),radial-gradient(circle_at_70%_70%,rgba(59,130,246,0.35),transparent_55%)]"></div>
                                  <div class="absolute inset-0 mix-blend-screen opacity-70">
                                    <video autoplay="" muted="" loop="" playsinline="" class="w-full h-full object-cover" style="filter: brightness(1.05) contrast(1.1) saturate(1.2);">
                                      <source src="https://cdn.midjourney.com/video/5029e61e-010a-4f9f-8daf-ef0a0ff34af6/3.mp4" type="video/mp4">
                                    </video>
                                  </div>
                                  <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="p-4 rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 shadow-[0_0_24px_8px_rgba(34,211,238,0.35)]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-8 h-8"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                    </div>
                                  </div>
                                </div>

                                <!-- Customization quick picks -->
                                <div class="flex items-center justify-center gap-3 mt-4">
                                  <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="lucide lucide-bot w-5 h-5 text-cyan-300"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                  </button>
                                  <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="headphones" class="lucide lucide-headphones w-5 h-5 text-blue-300"><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"></path></svg>
                                  </button>
                                  <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wand-2" class="lucide lucide-wand-2 w-5 h-5 text-cyan-200"><path d="m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72"></path><path d="m14 7 3 3"></path><path d="M5 6v4"></path><path d="M19 14v4"></path><path d="M10 2v2"></path><path d="M7 8H3"></path><path d="M21 16h-4"></path><path d="M11 3H9"></path></svg>
                                  </button>
                                </div>
                              </div>

                              <!-- Stepper -->
                              <div class="mt-7">
                                <div class="flex items-center justify-between">
                                  <div class="flex-1 flex items-center">
                                    <div class="size-7 rounded-full bg-gradient-to-br from-cyan-500 to-blue-500 text-white text-[12px] flex items-center justify-center shadow-[0_0_16px_rgba(34,211,238,0.5)]">1</div>
                                    <div class="h-0.5 flex-1 mx-2 bg-gradient-to-r from-cyan-500/50 to-blue-500/40"></div>
                                    <div class="size-7 rounded-full bg-neutral-900/70 border border-white/10 text-[12px] text-neutral-300 flex items-center justify-center">2</div>
                                    <div class="h-0.5 flex-1 mx-2 bg-white/10"></div>
                                    <div class="size-7 rounded-full bg-neutral-900/70 border border-white/10 text-[12px] text-neutral-300 flex items-center justify-center">3</div>
                                  </div>
                                </div>
                                <div class="mt-2 flex items-center justify-between text-[11px] text-neutral-400 px-1">
                                  <span>Choose Skills</span>
                                  <span>Pick Voice</span>
                                  <span>Set Goals</span>
                                </div>
                              </div>

                              <!-- Skills -->
                              <div class="mt-5">
                                <div class="text-[13px] text-neutral-300 mb-2">Example skills</div>
                                <div class="flex flex-wrap gap-2.5">
                                  <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition shadow-[0_0_12px_rgba(34,211,238,0.25)]">
                                    <div class="flex items-center gap-1.5">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="search" class="lucide lucide-search w-3.5 h-3.5"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg>
                                      <span>Research</span>
                                    </div>
                                  </button>
                                  <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition">
                                    <div class="flex items-center gap-1.5">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="workflow" class="lucide lucide-workflow w-3.5 h-3.5"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg>
                                      <span>Project Management</span>
                                    </div>
                                  </button>
                                  <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition">
                                    <div class="flex items-center gap-1.5">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="headset" class="lucide lucide-headset w-3.5 h-3.5"><path d="M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z"></path><path d="M21 16v2a4 4 0 0 1-4 4h-5"></path></svg>
                                      <span>Customer Support</span>
                                    </div>
                                  </button>
                                </div>
                              </div>

                              <!-- CTA -->
                              <div class="mt-auto">
                                <button class="w-full h-14 rounded-2xl bg-gradient-to-r from-cyan-500 to-blue-500 text-[15px] tracking-tight text-white border border-cyan-300/60 shadow-[0_10px_40px_-8px_rgba(34,211,238,0.55)] hover:shadow-[0_10px_50px_-6px_rgba(34,211,238,0.75)] transition">
                                  Deploy My Agent
                                </button>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">1 · Tailor Your Agent</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="arrow-right" class="lucide lucide-arrow-right w-5 h-5 text-neutral-300"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                      <!-- Screen 5 — ALIAS Toolbox -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 rounded-[3rem] p-[2px]">
                          <div class="overflow-hidden h-[844px] relative bg-black rounded-[2.9rem] neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="lucide lucide-signal w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="lucide lucide-wifi w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                              <!-- Header -->
                              <div class="flex items-center justify-between mb-4">
                                <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-cyan-300 via-cyan-200 to-blue-300 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(6,182,212,0.45)]">ALIAS Toolbox</h2>
                                <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sliders" class="lucide lucide-sliders w-4.5 h-4.5 text-neutral-300"><line x1="4" x2="4" y1="21" y2="14"></line><line x1="4" x2="4" y1="10" y2="3"></line><line x1="12" x2="12" y1="21" y2="12"></line><line x1="12" x2="12" y1="8" y2="3"></line><line x1="20" x2="20" y1="21" y2="16"></line><line x1="20" x2="20" y1="12" y2="3"></line><line x1="2" x2="6" y1="14" y2="14"></line><line x1="10" x2="14" y1="8" y2="8"></line><line x1="18" x2="22" y1="16" y2="16"></line></svg>
                                </button>
                              </div>

                              <!-- Tools Grid -->
                              <div class="grid grid-cols-2 gap-3">
                                <!-- Finance Tracker -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-green-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-green-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-green-400/20 to-emerald-400/10 border border-green-300/30 shadow-[0_0_24px_rgba(74,222,128,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wallet" class="lucide lucide-wallet w-5 h-5 text-green-300"><path d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1"></path><path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"></path></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Finance Tracker</div>
                                    <div class="text-[12px] text-neutral-400">Spending insights</div>
                                  </div>
                                </div>

                                <!-- Task Automator -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-cyan-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-cyan-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-cyan-400/20 to-blue-400/10 border border-cyan-300/30 shadow-[0_0_24px_rgba(34,211,238,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="lucide lucide-bot w-5 h-5 text-cyan-300"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Task Automator</div>
                                    <div class="text-[12px] text-neutral-400">Workflow scripts</div>
                                  </div>
                                </div>

                                <!-- Meeting Summarizer -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-blue-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-blue-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-blue-400/20 to-cyan-400/10 border border-blue-300/30 shadow-[0_0_24px_rgba(59,130,246,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="file-text" class="lucide lucide-file-text w-5 h-5 text-blue-300"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Meeting Summarizer</div>
                                    <div class="text-[12px] text-neutral-400">Notes &amp; action items</div>
                                  </div>
                                </div>

                                <!-- Idea Generator -->
                                <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-purple-400/40 transition relative overflow-hidden">
                                  <div class="absolute -inset-6 bg-gradient-to-br from-purple-400/10 via-transparent to-transparent pointer-events-none"></div>
                                  <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-purple-400/20 to-fuchsia-400/10 border border-purple-300/30 shadow-[0_0_24px_rgba(168,85,247,0.35)]">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-5 h-5 text-purple-300"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                  </div>
                                  <div class="mt-3">
                                    <div class="text-[14px] text-neutral-100 tracking-tight">Idea Generator</div>
                                    <div class="text-[12px] text-neutral-400">Creative prompts</div>
                                  </div>
                                </div>
                              </div>

                              <!-- Subscription Banner -->
                              <div class="mt-auto">
                                <div class="mt-6 p-4 rounded-2xl bg-gradient-to-br from-neutral-900/70 to-black/80 border border-white/10 backdrop-blur-xl relative overflow-hidden">
                                  <div class="absolute inset-0 bg-[radial-gradient(600px_200px_at_50%_-20%,rgba(34,211,238,0.2),transparent)]"></div>
                                  <div class="flex items-center justify-between relative z-10">
                                    <div>
                                      <div class="text-[14px] text-neutral-100 tracking-tight">Unlock the full toolbox</div>
                                      <div class="text-[12px] text-neutral-400">Advanced agents, priority compute</div>
                                    </div>
                                    <button class="px-3.5 h-10 rounded-xl bg-gradient-to-r from-cyan-500 to-blue-500 text-white text-[13px] border border-cyan-300/60 shadow-[0_10px_30px_-10px_rgba(34,211,238,0.65)] hover:shadow-[0_10px_40px_-8px_rgba(34,211,238,0.75)]">
                                      Upgrade to Pro
                                    </button>
                                  </div>
                                </div>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">2 · Toolbox</div>
                      </div>

                      <!-- Connector -->
                      <div class="shrink-0 mt-[180px]">
                        <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="arrow-right" class="lucide lucide-arrow-right w-5 h-5 text-neutral-300"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                        </div>
                      </div>

                      <!-- Screen 6 — Conversation Detail -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="lucide lucide-signal w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="lucide lucide-wifi w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative px-6 pb-6 pt-4 flex flex-col h-full">
                              <!-- Header -->
                              <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                  <div class="relative">
                                    <div class="absolute -inset-1 rounded-full bg-cyan-400/20 blur-md"></div>
                                    <div class="p-[2px] rounded-full bg-gradient-to-tr from-cyan-400 to-blue-500 relative">
                                      <div class="h-8 w-8 rounded-full bg-neutral-900/70 border border-cyan-300/40 flex items-center justify-center">
                                        <div class="size-3 rounded-full bg-cyan-400 shadow-[0_0_14px_rgba(34,211,238,0.8)]"></div>
                                      </div>
                                    </div>
                                  </div>
                                  <div>
                                    <h2 class="text-[18px] tracking-tight text-neutral-100">Conversation with ALIAS</h2>
                                    <div class="flex items-center gap-1.5">
                                      <span class="w-1.5 h-1.5 rounded-full bg-cyan-400 animate-pulse"></span>
                                      <span class="text-[11px] text-neutral-400">Processing</span>
                                    </div>
                                  </div>
                                </div>
                                <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="ellipsis" class="lucide lucide-ellipsis w-4.5 h-4.5 text-neutral-300"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
                                </button>
                              </div>

                              <!-- Chat -->
                              <div class="mt-4 flex-1 overflow-y-auto space-y-3 pr-1">
                                <!-- ALIAS -->
                                <div class="max-w-[80%]">
                                  <div class="">
                                      <div class="text-[13px] text-neutral-300">Hi, I’m ALIAS. I can summarize your meeting, extract action items, and create follow-up tasks. What would you like me to do?</div>
                                      <div class="mt-2 flex items-center gap-2">
                                        <span class="inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-[11px] bg-cyan-400/10 border border-cyan-400/30 text-cyan-200">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5" data-lucide="file-text"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg>
                                          Meeting Summarizer
                                        </span>
                                        <span class="text-[11px] text-neutral-500">09:41</span>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- User -->
                                  <div class="flex justify-end">
                                    <div class="max-w-[80%]">
                                      <div class="rounded-2xl border border-cyan-300/40 bg-gradient-to-br from-cyan-500/15 to-blue-500/10 text-neutral-50 p-3.5 shadow-[0_0_20px_-6px_rgba(34,211,238,0.6)]">
                                        Can you summarize my 10AM sync with the design team and create follow-up tasks for the dashboard polish?
                                      </div>
                                      <div class="mt-1 flex items-center justify-end gap-1.5 text-[11px]">
                                        <span class="text-neutral-500">09:42</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-3.5 h-3.5 text-cyan-300" data-lucide="check-check"><path d="M7 13l-3-3"></path><path d="M10 13l-3-3"></path><path d="M14 6l-1.5 2"></path><path d="M16 6l-3 4"></path></svg>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- ALIAS – Structured Card -->
                                  <div class="max-w-[85%]">
                                    <div class="rounded-2xl border border-white/10 bg-neutral-900/60 backdrop-blur p-0.5 overflow-hidden">
                                      <div class="rounded-[1rem] p-4 bg-neutral-950/60">
                                        <div class="flex items-center justify-between">
                                          <div class="flex items-center gap-2">
                                            <div class="size-8 rounded-lg flex items-center justify-center bg-gradient-to-br from-blue-400/20 to-cyan-400/10 border border-cyan-300/30">
                                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-cyan-300" data-lucide="file-text"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg>
                                            </div>
                                            <div>
                                              <div class="text-[13px] text-neutral-100 tracking-tight">Summary draft</div>
                                              <div class="text-[11px] text-neutral-500">from Meeting Summarizer</div>
                                            </div>
                                          </div>
                                          <div class="flex items-center gap-2">
                                            <button aria-label="Copy summary" class="size-8 rounded-lg bg-neutral-900/60 border border-white/10 hover:border-cyan-400/40 transition flex items-center justify-center">
                                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-[18px] h-[18px] text-neutral-300" data-lucide="copy"><rect width="14" height="14" x="8" y="8" rx="2"></rect><path d="M4 16V6a2 2 0 0 1 2-2h10"></path></svg>
                                            </button>
                                            <button aria-label="Open actions" class="size-8 rounded-lg bg-neutral-900/60 border border-white/10 hover:border-cyan-400/40 transition flex items-center justify-center">
                                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-[18px] h-[18px] text-neutral-300" data-lucide="more-horizontal"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
                                            </button>
                                          </div>
                                        </div>

                                        <div class="mt-3 text-[13px] text-neutral-300 leading-6">
                                          • The design team aligned on dashboard polish scope for v1.3.<br>
                                          • Prioritized: spacing, typography tokens, and empty states.<br>
                                          • Risks: time for accessibility pass may slip into v1.3.1.
                                        </div>

                                        <div class="mt-3 rounded-xl border border-white/10 bg-neutral-900/50 p-3">
                                          <div class="text-[12px] text-neutral-400 mb-2">Proposed tasks</div>
                                          <ul class="space-y-2">
                                            <li class="flex items-start gap-2">
                                              <span class="mt-1 w-1.5 h-1.5 rounded-full bg-cyan-400"></span>
                                              <span class="text-[13px] text-neutral-200">Normalize H4/H5 sizes and line-heights in token set</span>
                                            </li>
                                            <li class="flex items-start gap-2">
                                              <span class="mt-1 w-1.5 h-1.5 rounded-full bg-cyan-400"></span>
                                              <span class="text-[13px] text-neutral-200">Tighten card padding to 16px on mobile</span>
                                            </li>
                                            <li class="flex items-start gap-2">
                                              <span class="mt-1 w-1.5 h-1.5 rounded-full bg-cyan-400"></span>
                                              <span class="text-[13px] text-neutral-200">Add empty state illustrations + copy</span>
                                            </li>
                                          </ul>
                                        </div>

                                        <div class="mt-3 flex items-center gap-2">
                                          <button class="px-3 h-9 rounded-lg text-[12px] bg-gradient-to-r from-cyan-500 to-blue-500 text-white border border-cyan-300/60 shadow-[0_8px_24px_-10px_rgba(34,211,238,0.7)] hover:shadow-[0_8px_28px_-10px_rgba(34,211,238,0.9)]">Create tasks</button>
                                          <button class="px-3 h-9 rounded-lg text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-200 hover:border-cyan-400/40">Refine</button>
                                          <span class="ml-auto text-[11px] text-neutral-500">09:43</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Suggestions -->
                                  <div class="flex flex-wrap gap-2 pt-2">
                                    <button class="px-3 h-8 rounded-full text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-300 hover:border-cyan-400/40 transition">Add acceptance criteria</button>
                                    <button class="px-3 h-8 rounded-full text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-300 hover:border-cyan-400/40 transition">Share with team</button>
                                    <button class="px-3 h-8 rounded-full text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-300 hover:border-cyan-400/40 transition">Estimate effort</button>
                                  </div>
                                </div>

                              <!-- Composer -->
                              <div class="mt-4">
                                <div class="h-[52px] rounded-2xl bg-neutral-900/60 border border-white/10 flex items-center gap-2 px-2">
                                  <button aria-label="Attach" class="size-9 rounded-xl bg-neutral-900/60 border border-white/10 hover:border-cyan-400/40 transition flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-[18px] h-[18px] text-neutral-300" data-lucide="paperclip"><path d="M13.5 6.5L7 13a4.243 4.243 0 1 0 6 6l7-7a6 6 0 1 0-8.5-8.5l-7 7"></path></svg>
                                  </button>
                                  <input aria-label="Message input" type="text" placeholder="Message ALIAS…" class="flex-1 bg-transparent outline-none text-[14px] placeholder:text-neutral-500 text-neutral-100">
                                  <button aria-label="Voice" class="size-9 rounded-xl bg-neutral-900/60 border border-white/10 hover:border-cyan-400/40 transition flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-[18px] h-[18px] text-neutral-300" data-lucide="mic"><path d="M12 1a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3Z"></path><path d="M19 10a7 7 0 0 1-14 0"></path><path d="M12 19v4"></path></svg>
                                  </button>
                                  <button aria-label="Send" class="size-10 rounded-xl bg-gradient-to-r from-cyan-500 to-blue-500 border border-cyan-300/60 text-white shadow-[0_8px_24px_-10px_rgba(34,211,238,0.7)] hover:shadow-[0_8px_28px_-8px_rgba(34,211,238,0.9)] flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-[18px] h-[18px]" data-lucide="send"><path d="m22 2-7 20-4-9-9-4Z"></path><path d="M22 2 11 13"></path></svg>
                                  </button>
                                </div>
                                <div class="mt-2 flex items-center gap-3 px-1">
                                  <div class="flex items-center gap-1.5 text-[11px] text-neutral-500">
                                    <span class="w-1.5 h-1.5 rounded-full bg-cyan-400 animate-pulse"></span>
                                    Smart compose enabled
                                  </div>
                                  <button class="ml-auto text-[12px] text-neutral-400 hover:text-neutral-200 transition flex items-center gap-1">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-[16px] h-[16px]" data-lucide="settings-2"><path d="M20 7h-9"></path><path d="M14 17H5"></path><circle cx="17" cy="17" r="3"></circle><circle cx="7" cy="7" r="3"></circle></svg>
                                    Composer settings
                                  </button>
                                </div>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">3 · Conversation</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Lane: Pending Mockups -->
                <div class="mt-10">
                  <div class="flex items-center gap-2 mb-4">
                    <div class="size-2 rounded-full bg-amber-400"></div>
                    <div class="text-[13px] text-neutral-300 tracking-tight">Pending Mockups</div>
                    <div class="text-[12px] text-neutral-500">Work in progress</div>
                  </div>

                  <div class="relative">
                    <div class="flex gap-6 overflow-x-auto pb-2 items-start">
                      <!-- Onboarding — Full Screen -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-cyan-400/60 via-blue-500/40 to-cyan-500/60 shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                          <div class="rounded-[2.9rem] bg-black overflow-hidden h-[844px] relative neon-border">
                            <!-- Status bar -->
                            <div class="flex items-center justify-between px-8 pt-4 pb-2">
                              <div class="text-white text-sm">9:41</div>
                              <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                              <div class="flex items-center gap-1 text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4" data-lucide="signal"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4" data-lucide="wifi"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                  <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                  <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                </div>
                              </div>
                            </div>

                            <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                              <!-- Hero -->
                              <div class="mt-6 text-center entrance-animation-1">
                                <div class="mx-auto size-16 rounded-2xl bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 shadow-[0_0_40px_rgba(34,211,238,0.45)] flex items-center justify-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-7 h-7" data-lucide="sparkles"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                </div>
                                <h2 class="mt-5 text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent">Welcome to ALIAS</h2>
                                <p class="mt-2 text-[13px] text-neutral-400">Your personal AI for work and life. Private by default, powerful when you need it.</p>
                              </div>

                              <!-- Value bullets -->
                              <div class="mt-6 space-y-2 entrance-animation-2">
                                <div class="flex items-center gap-2 text-[13px] text-neutral-300">
                                  <span class="size-5 rounded-full bg-cyan-400/15 border border-cyan-300/40 flex items-center justify-center">
                                    <span class="w-2 h-2 rounded-full bg-cyan-400"></span>
                                  </span>
                                  Summaries, tasks, and insights—on any conversation
                                </div>
                                <div class="flex items-center gap-2 text-[13px] text-neutral-300">
                                  <span class="size-5 rounded-full bg-cyan-400/15 border border-cyan-300/40 flex items-center justify-center">
                                    <span class="w-2 h-2 rounded-full bg-cyan-400"></span>
                                  </span>
                                  Works with your favorite tools
                                </div>
                                <div class="flex items-center gap-2 text-[13px] text-neutral-300">
                                  <span class="size-5 rounded-full bg-cyan-400/15 border border-cyan-300/40 flex items-center justify-center">
                                    <span class="w-2 h-2 rounded-full bg-cyan-400"></span>
                                  </span>
                                  Designed for privacy and control
                                </div>
                              </div>

                              <!-- Auth actions -->
                              <div class="mt-8 space-y-2 entrance-animation-3">
                                <button class="w-full h-12 rounded-xl bg-neutral-900/70 border border-white/10 hover:border-cyan-400/40 transition flex items-center justify-center gap-2 text-[13px]">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 48 48" class="w-[18px] h-[18px]"><path fill="#fff" d="M24 9.5c3.34 0 6.36 1.27 8.67 3.36l6.35-6.35C35.64 2.5 30.17 0 24 0 14.7 0 6.6 5.37 2.69 13.22l7.43 5.77C12.01 13.6 17.47 9.5 24 9.5z"></path></svg>
                                  Continue with Google
                                </button>
                                <button class="w-full h-12 rounded-xl bg-neutral-900/70 border border-white/10 hover:border-cyan-400/40 transition flex items-center justify-center gap-2 text-[13px]">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" class="w-[18px] h-[18px]" viewBox="0 0 24 24" fill="currentColor"><path fill="#fff" d="M16.365 1.43c-.9.053-1.96.62-2.57 1.35-.56.672-1.05 1.77-.86 2.81.95.03 1.94-.54 2.54-1.29.58-.71 1.05-1.8.89-2.87zm-3.26 4.33c-1.43-.09-2.64.81-3.32.81-.68 0-1.73-.78-2.85-.76-1.47.02-2.82.85-3.58 2.15-1.53 2.64-.39 6.52 1.08 8.65.72 1.03 1.59 2.2 2.74 2.17 1.07-.04 1.47-.7 2.76-.7 1.29 0 1.64.7 2.77.68 1.16-.02 1.9-1.05 2.62-2.08.83-1.21 1.17-2.39 1.18-2.45-.03-.01-2.27-.9-2.3-3.57-.02-2.23 1.82-3.29 1.9-3.34-1.03-1.51-2.63-1.72-3.01-1.76z"></path></svg>
                                  Continue with Apple
                                </button>
                                <button class="w-full h-12 rounded-xl bg-gradient-to-r from-cyan-500 to-blue-500 text-white border border-cyan-300/60 shadow-[0_10px_30px_-10px_rgba(34,211,238,0.65)] hover:shadow-[0_10px_40px_-8px_rgba(34,211,238,0.75)] text-[13px]">
                                  Continue with Email
                                </button>
                                <div class="pt-2 text-center">
                                  <button class="text-[13px] text-neutral-300 hover:text-white transition underline underline-offset-4 decoration-white/20 hover:decoration-cyan-300/60">
                                    Create an account
                                  </button>
                                </div>
                              </div>

                              <!-- Legal -->
                              <div class="mt-auto text-center">
                                <p class="text-[11px] text-neutral-500">
                                  By continuing you agree to our
                                  <a href="#" class="text-neutral-300 hover:text-white transition">Terms</a>
                                  and
                                  <a href="#" class="text-neutral-300 hover:text-white transition">Privacy Policy</a>.
                                </p>
                              </div>

                              <!-- Home indicator -->
                              <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">Onboarding · Welcome</div>
                      </div>

                      <!-- Placeholder — Home -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-white/10 to-white/5">
                          <div class="rounded-[2.9rem] bg-neutral-950/70 border border-dashed border-white/15 h-[844px] relative flex items-center justify-center">
                            <div class="text-center px-6">
                              <div class="mx-auto size-12 rounded-xl bg-neutral-900/80 border border-white/10 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-neutral-400" data-lucide="house"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>
                              </div>
                              <div class="mt-3 text-[14px] text-neutral-200 tracking-tight">Home</div>
                              <div class="text-[12px] text-neutral-500">Placeholder (next turn)</div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">Home · TODO</div>
                      </div>

                      <!-- Placeholder — Voice Picker -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-white/10 to-white/5">
                          <div class="rounded-[2.9rem] bg-neutral-950/70 border border-dashed border-white/15 h-[844px] relative flex items-center justify-center">
                            <div class="text-center px-6">
                              <div class="mx-auto size-12 rounded-xl bg-neutral-900/80 border border-white/10 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-neutral-400" data-lucide="audio-lines"><path d="M2 10v3"></path><path d="M6 6v11"></path><path d="M10 3v18"></path><path d="M14 8v7"></path><path d="M18 5v13"></path><path d="M22 10v3"></path></svg>
                              </div>
                              <div class="mt-3 text-[14px] text-neutral-200 tracking-tight">Voice Picker</div>
                              <div class="text-[12px] text-neutral-500">Placeholder</div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">Voice Picker · TODO</div>
                      </div>

                      <!-- Placeholder — Settings -->
                      <div class="w-[390px] max-w-full shrink-0">
                        <div class="relative rounded-[3rem] p-[2px] bg-gradient-to-b from-white/10 to-white/5">
                          <div class="rounded-[2.9rem] bg-neutral-950/70 border border-dashed border-white/15 h-[844px] relative flex items-center justify-center">
                            <div class="text-center px-6">
                              <div class="mx-auto size-12 rounded-xl bg-neutral-900/80 border border-white/10 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5 text-neutral-400" data-lucide="settings"><path d="M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915"></path><circle cx="12" cy="12" r="3"></circle></svg>
                              </div>
                              <div class="mt-3 text-[14px] text-neutral-200 tracking-tight">Settings</div>
                              <div class="text-[12px] text-neutral-500">Placeholder</div>
                            </div>
                          </div>
                        </div>
                        <div class="text-[12px] text-neutral-400 mt-2 text-center">Settings · TODO</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div> <!-- end canvas backdrop -->
            </div>
          </section>
        </div>
      </div>
    </main>

    <footer class="border-t border-white/10">
      <div class="max-w-[1600px] mx-auto px-4 sm:px-6 py-6 text-[12px] text-neutral-500 flex items-center justify-between">
        <span>ALIAS · Storyboard v1</span>
        <div class="flex items-center gap-3">
          <a class="hover:text-neutral-300" href="#">Docs</a>
          <a class="hover:text-neutral-300" href="#">Changelog</a>
          <a class="hover:text-neutral-300" href="#">Support</a>
        </div>
      </div>
    </footer>
  
</body></html>