<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SANCTUM Mobile App - Master Flow Document</title>
  <style>
    /* Reset and Base Styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    :root {
      /* Light mode colors */
      --bg-primary: #FFFFFF;
      --bg-secondary: #F8F8F8;
      --bg-tertiary: #F0F0F0;
      --text-primary: #0D0D0D;
      --text-secondary: #666666;
      --text-tertiary: #999999;
      --border-color: #E5E5E5;
      --shadow-color: rgba(0, 0, 0, 0.08);
      --gold: #C9AB6E;
      --gold-hover: #B8995D;
      --cream: #E5DFD3;
      --success: #2A654A;
      --error: #D84949;
      --info: #2E5C8A;
      
      /* Transitions */
      --transition-fast: 150ms ease;
      --transition-normal: 300ms ease;
      --transition-slow: 500ms ease;
    }
    
    /* Dark mode variables */
    body.dark-mode {
      --bg-primary: #0D0D0D;
      --bg-secondary: #1A1A1A;
      --bg-tertiary: #252525;
      --text-primary: #FFFFFF;
      --text-secondary: #B0B0B0;
      --text-tertiary: #808080;
      --border-color: #333333;
      --shadow-color: rgba(0, 0, 0, 0.3);
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      transition: background-color var(--transition-normal), color var(--transition-normal);
    }
    
    /* Layout Container */
    .app-container {
      display: flex;
      height: 100vh;
      overflow: hidden;
    }
    
    /* Sidebar Navigation */
    .sidebar {
      width: 280px;
      background-color: var(--bg-primary);
      border-right: 1px solid var(--border-color);
      display: flex;
      flex-direction: column;
      transition: all var(--transition-normal);
    }
    
    .sidebar-header {
      padding: 2rem;
      border-bottom: 1px solid var(--border-color);
    }
    
    .sidebar-header h1 {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--gold);
    }
    
    .theme-toggle {
      margin-top: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .theme-toggle button {
      padding: 0.5rem 1rem;
      background-color: var(--bg-tertiary);
      border: none;
      border-radius: 8px;
      color: var(--text-primary);
      cursor: pointer;
      transition: all var(--transition-fast);
      font-size: 0.875rem;
    }
    
    .theme-toggle button:hover {
      background-color: var(--gold);
      color: white;
    }
    
    /* Navigation Items */
    .nav-list {
      flex: 1;
      overflow-y: auto;
      padding: 1rem 0;
    }
    
    .nav-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 2rem;
      color: var(--text-secondary);
      text-decoration: none;
      transition: all var(--transition-fast);
      cursor: pointer;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
      font-size: 0.9375rem;
    }
    
    .nav-item:hover {
      background-color: var(--bg-tertiary);
      color: var(--text-primary);
    }
    
    .nav-item.active {
      background-color: var(--bg-tertiary);
      color: var(--gold);
      font-weight: 600;
      position: relative;
    }
    
    .nav-item.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: var(--gold);
    }
    
    /* Main Content Area */
    .main-content {
      flex: 1;
      overflow-y: auto;
      background-color: var(--bg-secondary);
      padding: 2rem;
    }
    
    /* Flow Sections */
    .flow-section {
      display: none;
      animation: fadeIn var(--transition-normal);
    }
    
    .flow-section.active {
      display: block;
    }
    
    .flow-section h2 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 2rem;
      color: var(--text-primary);
    }
    
    /* Tab Navigation */
    .tab-nav {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0;
    }
    
    .tab-button {
      padding: 0.75rem 1.5rem;
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      font-size: 0.9375rem;
      font-weight: 500;
      position: relative;
      transition: all var(--transition-fast);
    }
    
    .tab-button:hover {
      color: var(--text-primary);
    }
    
    .tab-button.active {
      color: var(--gold);
    }
    
    .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background-color: var(--gold);
    }
    
    /* Tab Content Container */
    .tab-container {
      position: relative;
    }
    
    .tab-content {
      display: none;
      opacity: 0;
      transition: opacity var(--transition-fast);
    }
    
    .tab-content.active {
      display: block;
      opacity: 1;
    }
    
    /* Mobile Screen Frame */
    .mobile-screen {
      width: 375px;
      height: 812px;
      background-color: var(--bg-primary);
      border-radius: 32px;
      overflow: hidden;
      box-shadow: 0 20px 40px var(--shadow-color);
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      border: 1px solid var(--border-color);
    }
    
    /* Status Bar */
    .status-bar {
      height: 44px;
      background-color: var(--bg-primary);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
      font-size: 14px;
      font-weight: 600;
    }
    
    .status-icons {
      display: flex;
      gap: 4px;
    }
    
    /* App Header */
    .app-header {
      height: 56px;
      background-color: var(--bg-primary);
      border-bottom: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
    }
    
    .app-header h1 {
      font-size: 18px;
      font-weight: 700;
      color: var(--text-primary);
    }
    
    .header-left, .header-right {
      width: 40px;
      display: flex;
      justify-content: center;
    }
    
    .icon-button {
      width: 40px;
      height: 40px;
      border-radius: 20px;
      background-color: transparent;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all var(--transition-fast);
      color: var(--text-primary);
      font-size: 18px;
      position: relative;
    }
    
    .icon-button:hover {
      background-color: var(--bg-tertiary);
    }
    
    .icon-button .badge {
      position: absolute;
      top: 8px;
      right: 8px;
      background-color: var(--gold);
      color: white;
      font-size: 10px;
      font-weight: 700;
      padding: 2px 5px;
      border-radius: 10px;
      min-width: 16px;
      text-align: center;
    }
    
    /* Content Area */
    .content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
    }
    
    /* Components Styles */
    .primary-button {
      background-color: var(--gold);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 15px;
      cursor: pointer;
      transition: all var(--transition-fast);
      position: relative;
      overflow: hidden;
    }
    
    .primary-button:hover {
      background-color: var(--gold-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(201, 171, 110, 0.3);
    }
    
    .primary-button.large {
      padding: 16px 32px;
      font-size: 16px;
      width: 100%;
    }
    
    .primary-button.small {
      padding: 8px 16px;
      font-size: 14px;
    }
    
    .secondary-button {
      background-color: transparent;
      color: var(--gold);
      border: 1px solid var(--gold);
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 15px;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .secondary-button:hover {
      background-color: rgba(201, 171, 110, 0.1);
    }
    
    .text-button {
      background: none;
      border: none;
      color: var(--gold);
      font-weight: 500;
      font-size: 14px;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .text-button:hover {
      opacity: 0.8;
    }
    
    /* Cards */
    .card {
      background-color: var(--bg-primary);
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px var(--shadow-color);
      transition: all var(--transition-fast);
      border: 1px solid var(--border-color);
    }
    
    /* Section Headers */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      margin-top: 24px;
    }
    
    .section-header h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    /* Event Card Styles */
    .event-card {
      background-color: var(--bg-primary);
      border-radius: 12px;
      overflow: hidden;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px var(--shadow-color);
      border: 1px solid var(--border-color);
      transition: all var(--transition-fast);
    }
    
    .event-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px var(--shadow-color);
    }
    
    .event-image {
      width: 100%;
      height: 140px;
      background-color: var(--bg-tertiary);
      object-fit: cover;
    }
    
    .event-content {
      padding: 16px;
    }
    
    .event-category {
      font-size: 12px;
      font-weight: 600;
      color: var(--gold);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
    }
    
    .event-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 8px;
      line-height: 1.3;
    }
    
    .event-details {
      font-size: 14px;
      color: var(--text-secondary);
      line-height: 1.4;
    }
    
    .event-meta {
      display: flex;
      gap: 16px;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid var(--border-color);
    }
    
    .meta-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 13px;
      color: var(--text-secondary);
    }
    
    /* Bottom Nav */
    .bottom-nav {
      height: 88px;
      background-color: var(--bg-primary);
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding-bottom: 20px;
    }
    
    .nav-tab {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 8px;
      cursor: pointer;
      transition: all var(--transition-fast);
      color: var(--text-tertiary);
    }
    
    .nav-tab.active {
      color: var(--gold);
    }
    
    .nav-tab-icon {
      font-size: 24px;
    }
    
    .nav-tab-label {
      font-size: 11px;
      font-weight: 500;
    }
    
    /* Membership Card Styles */
    .membership-card {
      background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 20px;
      position: relative;
      overflow: hidden;
      min-height: 200px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    }
    
    .membership-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--gold) 0%, #FFD700 100%);
    }
    
    .membership-logo {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 48px;
      height: 48px;
      opacity: 0.3;
    }
    
    .membership-tier {
      font-size: 12px;
      font-weight: 600;
      color: var(--gold);
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 8px;
    }
    
    .membership-name {
      font-size: 20px;
      font-weight: 700;
      color: white;
      margin-bottom: 20px;
    }
    
    .membership-details {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    
    .membership-detail {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
    }
    
    /* Quick Actions Grid */
    .quick-actions {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 12px;
      margin-bottom: 24px;
    }
    
    .quick-action {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 16px 8px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .quick-action:hover {
      background-color: var(--gold);
      color: white;
    }
    
    .quick-action-icon {
      font-size: 28px;
    }
    
    .quick-action-label {
      font-size: 12px;
      font-weight: 500;
      text-align: center;
    }
    
    /* Guest Card Styles */
    .guest-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
      gap: 12px;
    }
    
    .guest-avatar {
      width: 48px;
      height: 48px;
      border-radius: 24px;
      background-color: var(--bg-tertiary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      color: var(--text-secondary);
    }
    
    .guest-info {
      flex: 1;
    }
    
    .guest-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .guest-detail {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .guest-status {
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .guest-status.active {
      background-color: rgba(42, 101, 74, 0.1);
      color: var(--success);
    }
    
    .guest-status.pending {
      background-color: rgba(201, 171, 110, 0.1);
      color: var(--gold);
    }
    
    /* Form Styles */
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-secondary);
      margin-bottom: 8px;
    }
    
    .form-input {
      width: 100%;
      padding: 12px 16px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      font-size: 16px;
      color: var(--text-primary);
      transition: all var(--transition-fast);
    }
    
    .form-input:focus {
      outline: none;
      border-color: var(--gold);
      background-color: var(--bg-primary);
    }
    
    /* Date Time Selector */
    .datetime-selector {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .date-input, .time-select, .duration-select {
      flex: 1;
      padding: 10px 12px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      font-size: 14px;
      color: var(--text-primary);
    }
    
    /* Filter Pills */
    .filter-pills {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      overflow-x: auto;
      padding-bottom: 8px;
    }
    
    .filter-pill {
      padding: 8px 16px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-secondary);
      white-space: nowrap;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .filter-pill.active {
      background-color: var(--gold);
      color: white;
      border-color: var(--gold);
    }
    
    /* Room Card */
    .room-card {
      display: flex;
      gap: 16px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
    }
    
    .room-card img {
      width: 100px;
      height: 100px;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .room-info {
      flex: 1;
    }
    
    .room-info h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .room-info p {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 8px;
    }
    
    .room-amenities {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      margin-bottom: 8px;
    }
    
    .amenity {
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .room-price {
      font-size: 16px;
      font-weight: 600;
      color: var(--gold);
    }
    
    /* Menu Item Styles */
    .menu-item {
      display: flex;
      gap: 12px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
      align-items: center;
    }
    
    .menu-item img {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .menu-item.featured img {
      width: 120px;
      height: 120px;
    }
    
    .item-details {
      flex: 1;
    }
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      margin-bottom: 4px;
    }
    
    .item-header h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .price {
      font-size: 16px;
      font-weight: 600;
      color: var(--gold);
    }
    
    .item-details p {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 8px;
    }
    
    .item-tags {
      display: flex;
      gap: 8px;
    }
    
    .dietary-tag {
      padding: 2px 8px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .add-button {
      width: 32px;
      height: 32px;
      border-radius: 16px;
      background-color: var(--gold);
      color: white;
      border: none;
      font-size: 20px;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .add-button:hover {
      background-color: var(--gold-hover);
    }
    
    /* Cart Styles */
    .cart-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 8px;
      margin-bottom: 8px;
      border: 1px solid var(--border-color);
    }
    
    .item-quantity {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-secondary);
    }
    
    .modifications {
      font-size: 12px;
      color: var(--text-tertiary);
    }
    
    .item-price {
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .remove-button {
      width: 24px;
      height: 24px;
      border-radius: 12px;
      background-color: var(--bg-tertiary);
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      font-size: 18px;
      transition: all var(--transition-fast);
    }
    
    .remove-button:hover {
      background-color: var(--error);
      color: white;
    }
    
    /* Order Summary */
    .order-summary {
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      padding: 16px;
      margin: 20px 0;
    }
    
    .summary-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .summary-row.total {
      padding-top: 8px;
      border-top: 1px solid var(--border-color);
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    /* Wellness Score */
    .wellness-score-card {
      background-color: var(--bg-primary);
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      border: 1px solid var(--border-color);
    }
    
    .score-circle {
      width: 200px;
      height: 200px;
      margin: 0 auto 20px;
      position: relative;
    }
    
    .score-value {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
    
    .score-value .number {
      display: block;
      font-size: 48px;
      font-weight: 700;
      color: var(--gold);
    }
    
    .score-value .label {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .score-breakdown {
      display: flex;
      justify-content: space-around;
    }
    
    .metric {
      text-align: center;
    }
    
    .metric .icon {
      font-size: 24px;
    }
    
    .metric .value {
      display: block;
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .metric .label {
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    /* Member Directory */
    .member-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
    }
    
    .member-avatar {
      width: 60px;
      height: 60px;
      border-radius: 30px;
      object-fit: cover;
      background-color: var(--bg-tertiary);
    }
    
    .member-info {
      flex: 1;
    }
    
    .member-info h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .member-info p {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 8px;
    }
    
    .member-tags {
      display: flex;
      gap: 8px;
    }
    
    .tag {
      padding: 2px 8px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .connect-button {
      padding: 8px 16px;
      background-color: var(--gold);
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .message-button {
      padding: 8px 16px;
      background-color: transparent;
      color: var(--gold);
      border: 1px solid var(--gold);
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    /* Service Grid */
    .service-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 24px;
    }
    
    .service-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 20px 12px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      cursor: pointer;
      transition: all var(--transition-fast);
      border: 1px solid transparent;
    }
    
    .service-card:hover {
      border-color: var(--gold);
      background-color: var(--bg-primary);
    }
    
    .service-icon {
      font-size: 32px;
    }
    
    .service-name {
      font-size: 13px;
      font-weight: 500;
      text-align: center;
      color: var(--text-primary);
    }
    
    /* Balance Card */
    .balance-card {
      background: linear-gradient(135deg, var(--gold) 0%, #B8995D 100%);
      border-radius: 16px;
      padding: 24px;
      margin-bottom: 24px;
      color: white;
    }
    
    .balance-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .balance-header h3 {
      font-size: 16px;
      font-weight: 500;
      opacity: 0.9;
    }
    
    .period {
      font-size: 14px;
      opacity: 0.8;
    }
    
    .balance-amount {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 20px;
      display: flex;
      align-items: baseline;
    }
    
    .currency {
      font-size: 32px;
      margin-right: 4px;
    }
    
    .cents {
      font-size: 32px;
      opacity: 0.8;
    }
    
    .balance-actions {
      display: flex;
      gap: 12px;
    }
    
    .balance-actions .primary-button {
      background-color: white;
      color: var(--gold);
    }
    
    .balance-actions .secondary-button {
      border-color: white;
      color: white;
    }
    
    /* Settings */
    .settings-section {
      margin-bottom: 32px;
    }
    
    .settings-section h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
    }
    
    .settings-item {
      margin-bottom: 16px;
    }
    
    .settings-item label {
      display: block;
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 8px;
    }
    
    .settings-input, .settings-select {
      width: 100%;
      padding: 12px 16px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      font-size: 16px;
      color: var(--text-primary);
    }
    
    .toggle-setting {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid var(--border-color);
    }
    
    .toggle-info h4 {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .toggle-info p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 48px;
      height: 28px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--bg-tertiary);
      transition: .4s;
      border-radius: 28px;
    }
    
    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 20px;
      width: 20px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .toggle-slider {
      background-color: var(--gold);
    }
    
    input:checked + .toggle-slider:before {
      transform: translateX(20px);
    }
    
    /* Animation Classes */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @keyframes slideUp {
      from {
        transform: translateY(100%);
      }
      to {
        transform: translateY(0);
      }
    }
    
    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transform: scale(0);
      animation: ripple-animation 0.6s ease-out;
      pointer-events: none;
    }
    
    @keyframes ripple-animation {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
    
    /* Responsive adjustments for smaller screens */
    @media (max-width: 1200px) {
      .sidebar {
        width: 240px;
      }
    }
    
    @media (max-width: 768px) {
      .app-container {
        flex-direction: column;
      }
      
      .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
      }
      
      .nav-list {
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
      }
      
      .nav-item {
        white-space: nowrap;
      }
      
      .mobile-screen {
        width: 100%;
        max-width: 375px;
        margin: 0 auto;
      }
    }
    
    /* Additional component styles as needed */
    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 24px;
    }
    
    .quick-action-card {
      padding: 20px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      text-align: center;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .quick-action-card:hover {
      background-color: var(--gold);
      color: white;
    }
    
    .action-icon {
      font-size: 32px;
    }
    
    .booking-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
    }
    
    .booking-time {
      text-align: center;
      min-width: 60px;
    }
    
    .booking-time .time {
      display: block;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .booking-time .duration {
      display: block;
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .booking-details {
      flex: 1;
    }
    
    .booking-details h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .booking-details p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .facility-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .facility-card {
      background-color: var(--bg-primary);
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid var(--border-color);
    }
    
    .facility-card img {
      width: 100%;
      height: 100px;
      object-fit: cover;
    }
    
    .facility-info {
      padding: 12px;
    }
    
    .facility-info h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .facility-info p {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 12px;
    }
    
    .order-type-selector {
      display: flex;
      gap: 8px;
      margin-bottom: 20px;
    }
    
    .order-type {
      flex: 1;
      padding: 12px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      text-align: center;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .order-type.active {
      background-color: var(--gold);
      color: white;
      border-color: var(--gold);
    }
    
    .order-type .icon {
      display: block;
      font-size: 24px;
      margin-bottom: 4px;
    }
    
    .special-requests textarea {
      width: 100%;
      padding: 12px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      font-size: 14px;
      color: var(--text-primary);
      resize: vertical;
      min-height: 80px;
    }
    
    .payment-method {
      margin: 20px 0;
    }
    
    .payment-option {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      border: 2px solid transparent;
    }
    
    .payment-option.selected {
      border-color: var(--gold);
    }
    
    .card-icon {
      font-size: 24px;
    }
    
    .card-details {
      flex: 1;
    }
    
    .card-type {
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .activity-timeline {
      margin-bottom: 24px;
    }
    
    .activity-item {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
    }
    
    .activity-item.completed {
      opacity: 0.7;
    }
    
    .activity-item .time {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-secondary);
      min-width: 70px;
    }
    
    .activity-details {
      flex: 1;
    }
    
    .activity-details h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .activity-details p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .status {
      font-size: 20px;
      color: var(--success);
    }
    
    .wellness-actions {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin-bottom: 24px;
    }
    
    .action-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
      border: none;
      cursor: pointer;
      transition: all var(--transition-fast);
      color: var(--text-primary);
    }
    
    .action-card:hover {
      background-color: var(--gold);
      color: white;
    }
    
    .insights-card {
      background-color: var(--bg-primary);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid var(--border-color);
    }
    
    .insights-card h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 16px;
    }
    
    .insight {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
    }
    
    .insight .icon {
      font-size: 20px;
    }
    
    .insight p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .search-container {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .search-input {
      flex: 1;
      padding: 12px 16px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      font-size: 14px;
      color: var(--text-primary);
    }
    
    .filter-button {
      padding: 12px 16px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      transition: all var(--transition-fast);
      color: var(--text-primary);
    }
    
    .filter-button:hover {
      border-color: var(--gold);
    }
    
    .concierge-welcome {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px;
      background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
      border-radius: 16px;
      margin-bottom: 24px;
    }
    
    .concierge-avatar {
      width: 80px;
      height: 80px;
      border-radius: 40px;
      object-fit: cover;
    }
    
    .welcome-message h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .welcome-message p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .request-list {
      margin-bottom: 24px;
    }
    
    .request-card {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      margin-bottom: 12px;
      border: 1px solid var(--border-color);
    }
    
    .request-icon {
      font-size: 24px;
    }
    
    .request-details {
      flex: 1;
    }
    
    .request-details h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .request-details p {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 4px;
    }
    
    .request-details .status {
      font-size: 12px;
      font-weight: 500;
      color: var(--success);
    }
    
    .popular-services {
      margin-bottom: 80px;
    }
    
    .service-highlight {
      display: flex;
      gap: 16px;
      padding: 16px;
      background-color: var(--bg-primary);
      border-radius: 12px;
      border: 1px solid var(--border-color);
    }
    
    .service-highlight img {
      width: 100px;
      height: 60px;
      border-radius: 8px;
      object-fit: cover;
    }
    
    .highlight-details {
      flex: 1;
    }
    
    .highlight-details h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .highlight-details p {
      font-size: 14px;
      color: var(--text-secondary);
      margin-bottom: 12px;
    }
    
    .floating {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      width: calc(100% - 40px);
      max-width: 335px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    }
    
    .bar-chart {
      display: flex;
      align-items: flex-end;
      justify-content: space-around;
      height: 200px;
      margin: 20px 0;
    }
    
    .bar {
      width: 60px;
      background-color: var(--gold);
      border-radius: 8px 8px 0 0;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      padding-bottom: 30px;
    }
    
    .bar .value {
      position: absolute;
      top: -20px;
      font-size: 12px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .bar .label {
      position: absolute;
      bottom: -20px;
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .transaction-list {
      margin-bottom: 24px;
    }
    
    .transaction-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px 0;
      border-bottom: 1px solid var(--border-color);
    }
    
    .transaction-icon {
      font-size: 24px;
    }
    
    .transaction-details {
      flex: 1;
    }
    
    .transaction-details h4 {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .transaction-details p {
      font-size: 14px;
      color: var(--text-secondary);
    }
    
    .transaction-amount {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .transaction-amount.credit {
      color: var(--success);
    }
    
    .payment-methods-summary {
      margin-top: 24px;
    }
    
    .method-card {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: var(--bg-tertiary);
      border-radius: 12px;
    }
    
    .method-details {
      flex: 1;
      display: flex;
      gap: 8px;
      align-items: center;
    }
    
    .method-details .type {
      font-size: 12px;
      color: var(--text-secondary);
    }
    
    .profile-photo-section {
      text-align: center;
      margin-bottom: 32px;
    }
    
    .profile-photo-large {
      position: relative;
      display: inline-block;
    }
    
    .profile-photo-large img {
      width: 120px;
      height: 120px;
      border-radius: 60px;
      object-fit: cover;
    }
    
    .photo-edit-button {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 40px;
      height: 40px;
      background-color: var(--gold);
      color: white;
      border: none;
      border-radius: 20px;
      font-size: 20px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .interest-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    
    .tag-pill {
      padding: 8px 16px;
      background-color: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 20px;
      font-size: 14px;
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .tag-pill.active {
      background-color: var(--gold);
      color: white;
      border-color: var(--gold);
    }
    
    .add-tag {
      padding: 8px 16px;
      background-color: transparent;
      border: 1px dashed var(--border-color);
      border-radius: 20px;
      font-size: 14px;
      color: var(--text-secondary);
      cursor: pointer;
      transition: all var(--transition-fast);
    }
    
    .add-tag:hover {
      border-color: var(--gold);
      color: var(--gold);
    }
    
    /* Notification styles */
    .section-placeholder {
      text-align: center;
      padding: 80px 20px;
      color: var(--text-secondary);
      font-size: 18px;
    }
    
    /* ─── Global gallery styles ───────────────────── */
    .gallery-grid{
      display:grid;
      grid-template-columns:repeat(auto-fill,minmax(180px,1fr));
      gap:20px;
      padding:24px;
    }
    .thumb{
      background:var(--bg-primary);
      border:1px solid var(--border-color);
      border-radius:12px;overflow:hidden;cursor:pointer;transition:transform .15s;
    }
    .thumb:hover{transform:translateY(-4px);}
    .thumb img{width:100%;height:120px;object-fit:cover;}
      .flow-row{display:flex;gap:40px;flex-wrap:wrap;justify-content:center;margin-bottom:48px;}

    /* ─── RHS Detail Panel ───────────────────────── */
    .detail-panel{width:420px;background:var(--bg-tertiary);border-left:1px solid var(--border-color);position:relative;display:flex;flex-direction:column;padding:24px 32px;transition:transform var(--transition-normal);}  
    .detail-panel.collapsed{transform:translateX(100%);}  
    .detail-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px;}  
    .detail-header h3{font-size:20px;font-weight:600;}  
    .detail-close{all:unset;cursor:pointer;font-size:28px;line-height:1;}  
    .detail-preview{width:100%;height:225px;border-radius:12px;background:var(--bg-primary);display:flex;align-items:center;justify-content:center;margin-bottom:24px;}  
    .detail-meta{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:24px;}  
    .meta-chip{background:var(--bg-primary);border:1px solid var(--border-color);border-radius:20px;padding:4px 12px;font-size:13px;}  
    .detail-actions{margin-top:auto;display:flex;gap:12px;}  
    .detail-actions .primary-button{flex:1;text-align:center;}
    
    /* Screen List Styles */
    .screen-list{flex:1;overflow-y:auto;margin-bottom:24px;}
    .screen-list-content{display:flex;flex-direction:column;gap:8px;}
    .screen-list-empty{color:var(--text-tertiary);text-align:center;padding:40px 20px;font-style:italic;}
    .screen-item{background:var(--bg-primary);border:1px solid var(--border-color);border-radius:8px;padding:12px 16px;cursor:pointer;transition:all var(--transition-fast);}
    .screen-item:hover{background:var(--bg-secondary);border-color:var(--gold);}
    .screen-item.active{background:var(--cream);border-color:var(--gold);color:var(--text-primary);}
    .screen-item-title{font-weight:600;font-size:14px;margin-bottom:4px;}
    .screen-item-subtitle{font-size:12px;color:var(--text-secondary);}
    .screen-item-status{display:inline-block;font-size:11px;padding:2px 8px;border-radius:12px;background:var(--bg-tertiary);color:var(--text-tertiary);margin-top:4px;}
    .screen-item-status.complete{background:var(--success);color:white;}
    .screen-item-status.inprogress{background:var(--gold);color:white;}
    .screen-item-status.todo{background:var(--error);color:white;}
    
    /* Screen Detail Styles */
    .screen-detail{flex:1;display:flex;flex-direction:column;}
    .screen-detail.hidden{display:none;}
    .screen-detail-header{margin-bottom:16px;}
    .back-button{background:none;border:none;color:var(--gold);cursor:pointer;font-size:14px;padding:8px 0;margin-bottom:8px;transition:color var(--transition-fast);}
    .back-button:hover{color:var(--gold-hover);}
    .screen-detail-header h4{font-size:18px;font-weight:600;color:var(--text-primary);}
    .screen-description{margin:16px 0;}
    .screen-description h5{font-size:14px;font-weight:600;margin-bottom:8px;color:var(--text-primary);}
    .screen-description p{font-size:13px;line-height:1.5;color:var(--text-secondary);}
    
    /* Selected screen highlight */
    .mobile-screen.screen-selected{border:3px solid var(--gold);box-shadow:0 0 0 1px var(--gold);transform:scale(1.02);transition:all var(--transition-fast);}
    
    /* Remove tabs, show all mobile screens directly in one row */
    .flow-section .tab-nav{display:none !important;}
    .flow-section .tab-container{display:flex !important;flex-wrap:nowrap;gap:24px;overflow-x:auto;padding-bottom:20px;}
    .flow-section .tab-content{display:contents !important;}
    .flow-section .tab-content .flow-row{display:contents;}
    .flow-section .mobile-screen{margin:0;flex:0 0 auto;}
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: var(--bg-tertiary);
    }
    
    ::-webkit-scrollbar-thumb {
      background: var(--border-color);
      border-radius: 4px;
    }
    
        ::-webkit-scrollbar-thumb:hover {  
      background: var(--text-tertiary);  
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h1>SANCTUM Mobile</h1>
        <div class="theme-toggle">
          <button id="darkModeToggle">🌓 Toggle Dark Mode</button>
        </div>
      </div>
      
      <nav class="nav-list">
        <button class="nav-item active" data-section="overview">🗂️ Overview</button>
        <button class="nav-item" data-section="auth">🔐 Authentication</button>
        <button class="nav-item" data-section="home">🏠 Home</button>
        <button class="nav-item" data-section="membership">💳 Membership</button>
        <button class="nav-item" data-section="guest">👥 Guest Management</button>
        <button class="nav-item" data-section="events">🎟️ Events</button>
        <button class="nav-item" data-section="facilities">🏢 Facilities</button>
        <button class="nav-item" data-section="fnb">🍽️ F&B Ordering</button>
        <button class="nav-item" data-section="wellness">🧘 Wellness</button>
        <button class="nav-item" data-section="social">💬 Social</button>
        <button class="nav-item" data-section="concierge">🛎️ Concierge</button>
        <button class="nav-item" data-section="payment">💰 Payment</button>
        <button class="nav-item" data-section="wine">🍷 Wine Club</button>
        <button class="nav-item" data-section="golf">⛳ Golf</button>
        <button class="nav-item" data-section="rewards">🏆 Rewards</button>
        <button class="nav-item" data-section="digitalkey">🔑 Digital Key</button>
        <button class="nav-item" data-section="emergency">🚨 Emergency</button>
        <button class="nav-item" data-section="feedback">💭 Feedback</button>
        <button class="nav-item" data-section="settings">⚙️ Settings</button>
        <button class="nav-item" data-section="staff">👔 Staff</button>
      </nav>
    </aside>
    
    <!-- Main Content Area -->
    <main class="main-content">
      <div class="content-wrapper">
        <!-- *** AUTO-GENERATED GALLERY *** -->
        <section id="overview" class="flow-section active">
          <h2>All Screens</h2>
          <div id="autoGallery" class="gallery-grid"></div>
        </section>

        <!-- Authentication Section -->
        <section id="auth" class="flow-section">
          <h2>Authentication & Onboarding</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Login</button>
            <button class="tab-button">Forgot Password</button>
            <button class="tab-button">Reset Success</button>
            <button class="tab-button">Onboarding</button>
          </div>
          
          <div class="tab-container">
            <!-- LOGIN SCREEN -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="content" style="display: flex; flex-direction: column; height: 100%; padding: 40px 24px;">
                    <!-- Logo -->
                    <div style="text-align: center; margin-bottom: 48px;">
                      <div style="width: 80px; height: 80px; background: var(--gold); border-radius: 40px; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                        <span style="color: white; font-size: 36px; font-weight: 700;">S</span>
                      </div>
                      <h1 style="font-size: 28px; font-weight: 700; margin-bottom: 8px;">SANCTUM</h1>
                      <p style="font-size: 14px; color: var(--text-secondary);">Exclusive Membership Club</p>
                    </div>
                    
                    <!-- Login Form -->
                    <div style="flex: 1;">
                      <div class="form-group" style="margin-bottom: 20px;">
                        <label style="font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">Email</label>
                        <input type="email" placeholder="Enter your email" class="input-field" style="width: 100%; padding: 16px; border: 1px solid var(--border-color); border-radius: 12px;">
                      </div>
                      
                      <div class="form-group" style="margin-bottom: 24px;">
                        <label style="font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">Password</label>
                        <input type="password" placeholder="Enter your password" class="input-field" style="width: 100%; padding: 16px; border: 1px solid var(--border-color); border-radius: 12px;">
                      </div>
                      
                      <button class="primary-button large" style="width: 100%; margin-bottom: 16px;">Sign In</button>
                      
                      <div style="text-align: center; margin-bottom: 32px;">
                        <button class="text-button" style="color: var(--gold);">Forgot Password?</button>
                      </div>
                      
                      <!-- Biometric Login -->
                      <div style="text-align: center;">
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">Or sign in with</p>
                        <div style="display: flex; gap: 16px; justify-content: center;">
                          <button class="icon-button" style="width: 60px; height: 60px; border-radius: 30px; border: 1px solid var(--border-color);">👤</button>
                          <button class="icon-button" style="width: 60px; height: 60px; border-radius: 30px; border: 1px solid var(--border-color);">👆</button>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Footer -->
                    <div style="text-align: center; padding-top: 20px;">
                      <p style="font-size: 14px; color: var(--text-secondary);">
                        Don't have an account? <button class="text-button" style="color: var(--gold);">Contact Membership</button>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- FORGOT PASSWORD SCREEN -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Reset Password</h1>
                    <div class="header-right"></div>
                  </div>
                  
                  <div class="content" style="padding: 24px;">
                    <div style="text-align: center; margin-bottom: 32px;">
                      <div style="width: 80px; height: 80px; background: var(--bg-tertiary); border-radius: 40px; margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                        <span style="font-size: 36px;">🔐</span>
                      </div>
                      <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 12px;">Forgot Your Password?</h2>
                      <p style="font-size: 16px; color: var(--text-secondary); line-height: 1.5;">Enter your email address and we'll send you instructions to reset your password.</p>
                    </div>
                    
                    <div class="form-group" style="margin-bottom: 24px;">
                      <label style="font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">Email Address</label>
                      <input type="email" placeholder="Enter your email" class="input-field" style="width: 100%; padding: 16px; border: 1px solid var(--border-color); border-radius: 12px;">
                    </div>
                    
                    <button class="primary-button large" style="width: 100%; margin-bottom: 16px;">Send Reset Link</button>
                    
                    <div style="text-align: center;">
                      <p style="font-size: 14px; color: var(--text-secondary);">
                        Remember your password? <button class="text-button" style="color: var(--gold);">Sign In</button>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- PASSWORD RESET SUCCESS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="content" style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%; padding: 40px;">
                    <div style="text-align: center;">
                      <div style="width: 100px; height: 100px; background: var(--success); border-radius: 50px; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center;">
                        <span style="color: white; font-size: 48px;">✓</span>
                      </div>
                      <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 16px;">Check Your Email</h2>
                      <p style="font-size: 16px; color: var(--text-secondary); line-height: 1.5; margin-bottom: 32px;">We've sent password reset instructions to <a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="d1b5b0bf91a2b0bfb2a5a4bcb2bda4b3ffb2bebcffb0a4">[email&#160;protected]</a></p>
                      <button class="primary-button large" style="width: 100%; margin-bottom: 16px;">Open Email App</button>
                      <button class="secondary-button" style="width: 100%;">Back to Sign In</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- ONBOARDING 1 - WELCOME -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="content" style="display: flex; flex-direction: column; height: 100%;">
                    <!-- Skip button -->
                    <div style="text-align: right; padding: 20px;">
                      <button class="text-button">Skip</button>
                    </div>
                    
                    <!-- Content -->
                    <div style="flex: 1; display: flex; flex-direction: column; justify-content: center; padding: 40px;">
                      <div style="text-align: center;">
                        <div style="width: 120px; height: 120px; background: var(--gold); border-radius: 60px; margin: 0 auto 32px; display: flex; align-items: center; justify-content: center;">
                          <span style="color: white; font-size: 60px; font-weight: 700;">S</span>
                        </div>
                        <h1 style="font-size: 32px; font-weight: 700; margin-bottom: 16px;">Welcome to SANCTUM</h1>
                        <p style="font-size: 18px; color: var(--text-secondary); line-height: 1.5;">Your exclusive gateway to luxury experiences and connections</p>
                      </div>
                    </div>
                    
                    <!-- Navigation -->
                    <div style="padding: 40px;">
                      <div style="display: flex; justify-content: center; gap: 8px; margin-bottom: 32px;">
                        <div style="width: 32px; height: 4px; background: var(--gold); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                      </div>
                      <button class="primary-button large" style="width: 100%;">Get Started</button>
                    </div>
                  </div>
                </div>
                
                <!-- ONBOARDING 2 - FEATURES -->
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="content" style="display: flex; flex-direction: column; height: 100%;">
                    <!-- Skip button -->
                    <div style="text-align: right; padding: 20px;">
                      <button class="text-button">Skip</button>
                    </div>
                    
                    <!-- Content -->
                    <div style="flex: 1; padding: 40px;">
                      <div style="text-align: center; margin-bottom: 40px;">
                        <div style="width: 100px; height: 100px; background: var(--bg-tertiary); border-radius: 50px; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center;">
                          <span style="font-size: 48px;">🏛️</span>
                        </div>
                        <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 16px;">Premium Facilities</h2>
                        <p style="font-size: 16px; color: var(--text-secondary); line-height: 1.5;">Access world-class amenities including private dining, meeting rooms, and wellness facilities</p>
                      </div>
                      
                      <!-- Feature highlights -->
                      <div style="display: grid; gap: 16px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; background: var(--gold); border-radius: 24px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                            <span style="color: white; font-size: 20px;">🍽️</span>
                          </div>
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Fine Dining</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Michelin-starred restaurants</p>
                          </div>
                        </div>
                        
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; background: var(--gold); border-radius: 24px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                            <span style="color: white; font-size: 20px;">💼</span>
                          </div>
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Business Center</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Private meeting rooms & offices</p>
                          </div>
                        </div>
                        
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; background: var(--gold); border-radius: 24px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                            <span style="color: white; font-size: 20px;">🧘</span>
                          </div>
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Wellness</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Spa, gym & wellness programs</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Navigation -->
                    <div style="padding: 40px;">
                      <div style="display: flex; justify-content: center; gap: 8px; margin-bottom: 32px;">
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--gold); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                      </div>
                      <button class="primary-button large" style="width: 100%;">Next</button>
                    </div>
                  </div>
                </div>
                
                <!-- ONBOARDING 3 - NETWORKING -->
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="content" style="display: flex; flex-direction: column; height: 100%;">
                    <!-- Skip button -->
                    <div style="text-align: right; padding: 20px;">
                      <button class="text-button">Skip</button>
                    </div>
                    
                    <!-- Content -->
                    <div style="flex: 1; display: flex; flex-direction: column; justify-content: center; padding: 40px;">
                      <div style="text-align: center;">
                        <div style="width: 100px; height: 100px; background: var(--bg-tertiary); border-radius: 50px; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center;">
                          <span style="font-size: 48px;">🤝</span>
                        </div>
                        <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 16px;">Connect & Network</h2>
                        <p style="font-size: 16px; color: var(--text-secondary); line-height: 1.5; margin-bottom: 32px;">Join an exclusive community of leaders, innovators, and tastemakers</p>
                        
                        <!-- Member avatars -->
                        <div style="display: flex; justify-content: center; margin-bottom: 16px;">
                          <div style="width: 60px; height: 60px; border-radius: 30px; background: var(--gold); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; border: 3px solid white; margin-right: -20px; z-index: 3;">JD</div>
                          <div style="width: 60px; height: 60px; border-radius: 30px; background: var(--info); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; border: 3px solid white; margin-right: -20px; z-index: 2;">SC</div>
                          <div style="width: 60px; height: 60px; border-radius: 30px; background: var(--success); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; border: 3px solid white; margin-right: -20px; z-index: 1;">MW</div>
                          <div style="width: 60px; height: 60px; border-radius: 30px; background: var(--bg-tertiary); display: flex; align-items: center; justify-content: center; color: var(--text-secondary); font-weight: 600; border: 3px solid white;">+247</div>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary);">250+ verified members</p>
                      </div>
                    </div>
                    
                    <!-- Navigation -->
                    <div style="padding: 40px;">
                      <div style="display: flex; justify-content: center; gap: 8px; margin-bottom: 32px;">
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--gold); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                      </div>
                      <button class="primary-button large" style="width: 100%;">Next</button>
                    </div>
                  </div>
                </div>
                
                <!-- ONBOARDING 4 - PERMISSIONS -->
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="content" style="display: flex; flex-direction: column; height: 100%;">
                    <!-- Content -->
                    <div style="flex: 1; display: flex; flex-direction: column; justify-content: center; padding: 40px;">
                      <div style="text-align: center;">
                        <div style="width: 100px; height: 100px; background: var(--bg-tertiary); border-radius: 50px; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center;">
                          <span style="font-size: 48px;">🔔</span>
                        </div>
                        <h2 style="font-size: 28px; font-weight: 700; margin-bottom: 16px;">Stay Updated</h2>
                        <p style="font-size: 16px; color: var(--text-secondary); line-height: 1.5; margin-bottom: 40px;">Enable notifications to receive updates about events, bookings, and exclusive offers</p>
                        
                        <button class="primary-button large" style="width: 100%; margin-bottom: 16px;">Enable Notifications</button>
                        <button class="secondary-button" style="width: 100%;">Maybe Later</button>
                      </div>
                    </div>
                    
                    <!-- Navigation -->
                    <div style="padding: 40px;">
                      <div style="display: flex; justify-content: center; gap: 8px;">
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--border-color); border-radius: 2px;"></div>
                        <div style="width: 32px; height: 4px; background: var(--gold); border-radius: 2px;"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Home Section -->
        <section id="home" class="flow-section active">
          <h2>Home Dashboard</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Dashboard</button>
            <button class="tab-button">Notifications</button>
            <button class="tab-button">Quick Actions</button>
            <button class="tab-button">Today's Activity</button>
            <button class="tab-button">Member Directory</button>
            <button class="tab-button">Club Info</button>
            <button class="tab-button">Account</button>
            <button class="tab-button">Customize</button>
            <button class="tab-button">Widgets</button>
            <button class="tab-button">Layout</button>
            <button class="tab-button">Preferences</button>
          </div>
          
          <div class="tab-container">
            <!-- Dashboard Tab -->
            <div class="tab-content active">
              <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">☰</button>
                  </div>
                  <h1>SANCTUM</h1>
                  <div class="header-right">
                    <button class="icon-button">
                      🔔
                      <span class="badge">3</span>
                    </button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Welcome Section -->
                  <div class="welcome-section">
                    <h2 style="font-size: 24px; margin-bottom: 8px;">Good morning, Dan</h2>
                    <p style="color: var(--text-secondary); margin-bottom: 20px;">Your day at SANCTUM awaits</p>
                  </div>
                  
                  <!-- Membership Card -->
                  <div class="membership-card">
                    <div class="membership-logo">🏛️</div>
                    <div class="membership-tier">FOUNDATION PRINCIPAL</div>
                    <div class="membership-name">DAN HUMPHREYS</div>
                    <div class="membership-details">
                      <div class="membership-detail">MEMBER SINCE 2023</div>
                      <div class="membership-detail">NO. 2023-0042</div>
                    </div>
                  </div>
                  
                  <!-- Quick Actions -->
                  <div class="quick-actions">
                    <div class="quick-action">
                      <div class="quick-action-icon">🎟️</div>
                      <div class="quick-action-label">Events</div>
                    </div>
                    <div class="quick-action">
                      <div class="quick-action-icon">🍽️</div>
                      <div class="quick-action-label">Dining</div>
                    </div>
                    <div class="quick-action">
                      <div class="quick-action-icon">👥</div>
                      <div class="quick-action-label">Guests</div>
                    </div>
                    <div class="quick-action">
                      <div class="quick-action-icon">🏢</div>
                      <div class="quick-action-label">Book</div>
                    </div>
                  </div>
                  
                  <!-- Today's Schedule -->
                  <div class="section-header">
                    <h3>Today's Schedule</h3>
                    <button class="text-button">View All</button>
                  </div>
                  
                  <div class="schedule-item card">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                      <div>
                        <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Executive Boardroom</h4>
                        <p style="font-size: 14px; color: var(--text-secondary);">2:00 PM - 4:00 PM • Level 18</p>
                      </div>
                      <button class="icon-button">→</button>
                    </div>
                  </div>
                  
                  <!-- Upcoming Events -->
                  <div class="section-header">
                    <h3>Featured Events</h3>
                    <button class="text-button">See All</button>
                  </div>
                  
                  <div class="event-card">
                    <img src="https://via.placeholder.com/343x140/1a1a1a/666?text=Melbourne+Cup" alt="Melbourne Cup" class="event-image">
                    <div class="event-content">
                      <div class="event-category">EXCLUSIVE</div>
                      <h4 class="event-title">Melbourne Cup VIP Experience</h4>
                      <p class="event-details">Birdcage access, premium dining, and luxury transfers</p>
                      <div class="event-meta">
                        <div class="meta-item">📅 Nov 2</div>
                        <div class="meta-item">👥 Limited</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Bottom Navigation -->
                <div class="bottom-nav">
                  <div class="nav-tab active">
                    <span class="nav-tab-icon">🏠</span>
                    <span class="nav-tab-label">Home</span>
                  </div>
                  <div class="nav-tab">
                    <span class="nav-tab-icon">🎟️</span>
                    <span class="nav-tab-label">Events</span>
                  </div>
                  <div class="nav-tab">
                    <span class="nav-tab-icon">🏢</span>
                    <span class="nav-tab-label">Facilities</span>
                  </div>
                  <div class="nav-tab">
                    <span class="nav-tab-icon">🍽️</span>
                    <span class="nav-tab-label">Dining</span>
                  </div>
                  <div class="nav-tab">
                    <span class="nav-tab-icon">👤</span>
                    <span class="nav-tab-label">Profile</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Notifications Tab -->
            <div class="tab-content">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Notifications</h1>
                  <div class="header-right">
                    <button class="text-button">Clear All</button>
                  </div>
                </div>
                
                <div class="content">
                  <div class="notification-item card" style="margin-bottom: 12px;">
                    <div style="display: flex; gap: 12px;">
                      <div style="width: 40px; height: 40px; background-color: var(--gold); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white;">🎟️</div>
                      <div style="flex: 1;">
                        <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">New Event: Wine Masterclass</h4>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Join renowned sommelier James Halliday for an exclusive tasting</p>
                        <span style="font-size: 12px; color: var(--text-tertiary);">2 hours ago</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="notification-item card" style="margin-bottom: 12px;">
                    <div style="display: flex; gap: 12px;">
                      <div style="width: 40px; height: 40px; background-color: var(--success); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white;">✓</div>
                      <div style="flex: 1;">
                        <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Booking Confirmed</h4>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Executive Boardroom reserved for tomorrow at 2:00 PM</p>
                        <span style="font-size: 12px; color: var(--text-tertiary);">Yesterday</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="notification-item card" style="margin-bottom: 12px;">
                    <div style="display: flex; gap: 12px;">
                      <div style="width: 40px; height: 40px; background-color: var(--info); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white;">👥</div>
                      <div style="flex: 1;">
                        <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">New Member Introduction</h4>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">Sarah Chen (Partner, Chen & Associates) has joined SANCTUM</p>
                        <span style="font-size: 12px; color: var(--text-tertiary);">3 days ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Quick Actions Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">☰</button>
                    </div>
                    <h1>Quick Actions</h1>
                    <div class="header-right">
                      <button class="icon-button">🔔</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; padding: 20px;">
                      <!-- Quick Action Cards -->
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">🍽️</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Order Food</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Restaurant & Room Service</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">📅</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Book Facility</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Meeting Rooms & Spaces</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">👥</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Invite Guest</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Send Guest Invitation</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">🧘</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Wellness</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Spa & Fitness Booking</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">🎟️</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Events</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Browse & Book Events</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">🛎️</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Concierge</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Request Services</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">🚗</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Valet</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Request Your Vehicle</p>
                      </div>
                      
                      <div class="card" style="padding: 24px; text-align: center; cursor: pointer;">
                        <div style="font-size: 48px; margin-bottom: 12px;">💬</div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Member Chat</h3>
                        <p style="font-size: 13px; color: var(--text-secondary);">Connect with Members</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Today's Activity Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Today's Activity</h1>
                    <div class="header-right">
                      <button class="icon-button">📊</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Activity Summary -->
                      <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, var(--gold), #B8995D); color: white;">
                        <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">Your Day at SANCTUM</h3>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; text-align: center;">
                          <div>
                            <p style="font-size: 24px; font-weight: 700;">3</p>
                            <p style="font-size: 12px; opacity: 0.9;">Bookings</p>
                          </div>
                          <div>
                            <p style="font-size: 24px; font-weight: 700;">2</p>
                            <p style="font-size: 12px; opacity: 0.9;">Guests</p>
                          </div>
                          <div>
                            <p style="font-size: 24px; font-weight: 700;">1</p>
                            <p style="font-size: 12px; opacity: 0.9;">Events</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Schedule Timeline -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Schedule</h3>
                      
                      <div class="card" style="margin-bottom: 12px; border-left: 4px solid var(--gold);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Business Lunch</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Private Dining Room 3</p>
                          </div>
                          <span style="font-size: 12px; color: var(--gold); font-weight: 600;">12:00 PM</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary);">Meeting with Sarah Chen + 3 clients</p>
                        <button class="secondary-button" style="width: 100%; margin-top: 8px;">View Details</button>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px; border-left: 4px solid var(--info);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Executive Boardroom</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Level 18</p>
                          </div>
                          <span style="font-size: 12px; color: var(--info); font-weight: 600;">2:00 PM</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary);">Board meeting - 2 hours</p>
                        <button class="secondary-button" style="width: 100%; margin-top: 8px;">Check-in Early</button>
                      </div>
                      
                      <div class="card" style="border-left: 4px solid var(--success);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Networking Drinks</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Rooftop Terrace</p>
                          </div>
                          <span style="font-size: 12px; color: var(--success); font-weight: 600;">6:00 PM</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary);">Monthly member mixer</p>
                        <button class="primary-button" style="width: 100%; margin-top: 8px;">RSVP</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Member Directory Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Member Directory</h1>
                    <div class="header-right">
                      <button class="icon-button">🔍</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Search Bar -->
                      <div style="margin-bottom: 20px;">
                        <input type="search" placeholder="Search members..." class="input-field" style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px;">
                      </div>
                      
                      <!-- Filter Tabs -->
                      <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="secondary-button" style="flex: 1;">All</button>
                        <button class="secondary-button" style="flex: 1;">Online</button>
                        <button class="secondary-button" style="flex: 1;">At Club</button>
                      </div>
                      
                      <!-- Member List -->
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--gold); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">SC</div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Sarah Chen</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Tech Executive • Foundation Principal</p>
                          </div>
                          <div style="text-align: right;">
                            <span style="width: 8px; height: 8px; background: var(--success); border-radius: 4px; display: inline-block;"></span>
                            <p style="font-size: 11px; color: var(--success); margin-top: 2px;">At Club</p>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--info); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">MT</div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Michael Thompson</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Investment Banking • Foundation Principal</p>
                          </div>
                          <div style="text-align: right;">
                            <span style="width: 8px; height: 8px; background: var(--text-tertiary); border-radius: 4px; display: inline-block;"></span>
                            <p style="font-size: 11px; color: var(--text-tertiary); margin-top: 2px;">Offline</p>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--success); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">JW</div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">James Wilson</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Legal Partner • Distinguished Member</p>
                          </div>
                          <div style="text-align: right;">
                            <span style="width: 8px; height: 8px; background: var(--gold); border-radius: 4px; display: inline-block;"></span>
                            <p style="font-size: 11px; color: var(--gold); margin-top: 2px;">Online</p>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; align-items: center; gap: 16px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--error); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">AR</div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Anna Rodriguez</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Healthcare CEO • Foundation Principal</p>
                          </div>
                          <div style="text-align: right;">
                            <span style="width: 8px; height: 8px; background: var(--success); border-radius: 4px; display: inline-block;"></span>
                            <p style="font-size: 11px; color: var(--success); margin-top: 2px;">At Club</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Club Info Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Club Information</h1>
                    <div class="header-right">
                      <button class="icon-button">🌤️</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Weather & Time -->
                      <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, #2E5C8A, #4A7BA7); color: white;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                          <div>
                            <h3 style="font-size: 18px; font-weight: 600;">Melbourne CBD</h3>
                            <p style="font-size: 14px; opacity: 0.9;">Wednesday, March 15</p>
                          </div>
                          <div style="text-align: right;">
                            <div style="font-size: 36px; margin-bottom: 4px;">☀️</div>
                            <p style="font-size: 28px; font-weight: 700;">22°C</p>
                          </div>
                        </div>
                        <p style="font-size: 14px; opacity: 0.9;">Perfect weather for rooftop dining</p>
                      </div>
                      
                      <!-- Club Status -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Club Status</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                          <div style="text-align: center; padding: 12px;">
                            <div style="font-size: 24px; margin-bottom: 8px;">👥</div>
                            <p style="font-size: 20px; font-weight: 700; color: var(--gold);">142</p>
                            <p style="font-size: 12px; color: var(--text-secondary);">Members Present</p>
                          </div>
                          <div style="text-align: center; padding: 12px;">
                            <div style="font-size: 24px; margin-bottom: 8px;">🏢</div>
                            <p style="font-size: 20px; font-weight: 700; color: var(--success);">85%</p>
                            <p style="font-size: 12px; color: var(--text-secondary);">Facilities Available</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Today's Highlights -->
                      <div class="card" style="margin-bottom: 16px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Today's Highlights</h3>
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                          <div style="font-size: 20px;">🍽️</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Chef's Special</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Wagyu beef with truffle risotto</p>
                          </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                          <div style="font-size: 20px;">🎵</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Live Jazz</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Piano lounge, 7:00 PM</p>
                          </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 12px;">
                          <div style="font-size: 20px;">📚</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Book Club</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Library, 6:30 PM</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Quick Contact -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Contact</h3>
                        <div style="display: flex; gap: 8px;">
                          <button class="secondary-button" style="flex: 1;">📞 Call</button>
                          <button class="secondary-button" style="flex: 1;">💬 Chat</button>
                          <button class="secondary-button" style="flex: 1;">📧 Email</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Account Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Account Overview</h1>
                    <div class="header-right">
                      <button class="icon-button">💳</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Account Summary -->
                      <div class="card" style="margin-bottom: 20px; background: var(--bg-tertiary);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 16px;">
                          <div>
                            <h3 style="font-size: 18px; font-weight: 600;">Current Balance</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">Available credit</p>
                          </div>
                          <p style="font-size: 32px; font-weight: 700; color: var(--gold);">$2,847</p>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                          <div>
                            <p style="font-size: 12px; color: var(--text-secondary);">This Month</p>
                            <p style="font-size: 20px; font-weight: 600;">$1,234</p>
                          </div>
                          <div>
                            <p style="font-size: 12px; color: var(--text-secondary);">Last Month</p>
                            <p style="font-size: 20px; font-weight: 600;">$987</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Quick Actions -->
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                        <button class="primary-button">💳 Add Funds</button>
                        <button class="secondary-button">📄 Statement</button>
                      </div>
                      
                      <!-- Recent Transactions -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Transactions</h3>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 4px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Private Dining</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">March 14, 2024</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--error);">-$340</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">Business lunch for 4 guests</p>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 4px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Personal Training</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">March 13, 2024</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--error);">-$120</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">1-hour session with Michael</p>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 4px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Account Credit</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">March 12, 2024</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--success);">+$1,000</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">Monthly membership credit</p>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 4px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Spa Treatment</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">March 11, 2024</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--error);">-$180</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">Deep tissue massage</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Customize Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Customize Dashboard</h1>
                    <div class="header-right">
                      <button class="icon-button">💾</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Layout Preview -->
                      <div class="card" style="margin-bottom: 20px; background: var(--bg-tertiary);">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Dashboard Preview</h3>
                        <div style="border: 2px dashed var(--border-color); border-radius: 8px; padding: 16px; background: var(--bg-primary);">
                          <!-- Mini dashboard preview -->
                          <div style="display: grid; gap: 8px; opacity: 0.7;">
                            <div style="height: 20px; background: var(--gold); border-radius: 4px; display: flex; align-items: center; padding: 0 8px;">
                              <span style="font-size: 10px; color: white;">Welcome Section</span>
                            </div>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                              <div style="height: 40px; background: var(--info); border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                <span style="font-size: 10px; color: white;">Schedule</span>
                              </div>
                              <div style="height: 40px; background: var(--success); border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                <span style="font-size: 10px; color: white;">Events</span>
                              </div>
                            </div>
                            <div style="height: 30px; background: var(--text-tertiary); border-radius: 4px; display: flex; align-items: center; padding: 0 8px;">
                              <span style="font-size: 10px; color: white;">Quick Actions</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Widget Categories -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Available Widgets</h3>
                      
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                        <div class="card" style="padding: 16px; text-align: center; cursor: pointer; border: 2px solid transparent;" data-widget="schedule">
                          <div style="font-size: 24px; margin-bottom: 8px;">📅</div>
                          <h4 style="font-size: 14px; font-weight: 600;">Schedule</h4>
                          <p style="font-size: 12px; color: var(--text-secondary);">Today's bookings</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; text-align: center; cursor: pointer; border: 2px solid var(--gold);" data-widget="events">
                          <div style="font-size: 24px; margin-bottom: 8px;">🎟️</div>
                          <h4 style="font-size: 14px; font-weight: 600;">Events</h4>
                          <p style="font-size: 12px; color: var(--text-secondary);">Upcoming events</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; text-align: center; cursor: pointer; border: 2px solid transparent;" data-widget="weather">
                          <div style="font-size: 24px; margin-bottom: 8px;">🌤️</div>
                          <h4 style="font-size: 14px; font-weight: 600;">Weather</h4>
                          <p style="font-size: 12px; color: var(--text-secondary);">Current conditions</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; text-align: center; cursor: pointer; border: 2px solid var(--gold);" data-widget="account">
                          <div style="font-size: 24px; margin-bottom: 8px;">💳</div>
                          <h4 style="font-size: 14px; font-weight: 600;">Account</h4>
                          <p style="font-size: 12px; color: var(--text-secondary);">Balance & spending</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; text-align: center; cursor: pointer; border: 2px solid transparent;" data-widget="members">
                          <div style="font-size: 24px; margin-bottom: 8px;">👥</div>
                          <h4 style="font-size: 14px; font-weight: 600;">Members</h4>
                          <p style="font-size: 12px; color: var(--text-secondary);">Who's at club</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; text-align: center; cursor: pointer; border: 2px solid var(--gold);" data-widget="facilities">
                          <div style="font-size: 24px; margin-bottom: 8px;">🏢</div>
                          <h4 style="font-size: 14px; font-weight: 600;">Facilities</h4>
                          <p style="font-size: 12px; color: var(--text-secondary);">Availability status</p>
                        </div>
                      </div>
                      
                      <!-- Widget Settings -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Widget Settings</h3>
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Auto-refresh</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Update widgets automatically</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox" checked>
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                          
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Compact Mode</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Show more widgets on screen</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox">
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Widgets Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Widget Library</h1>
                    <div class="header-right">
                      <button class="icon-button">🔍</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Widget Categories -->
                      <div style="display: flex; gap: 8px; margin-bottom: 20px; overflow-x: auto;">
                        <button class="secondary-button" style="white-space: nowrap; border: 2px solid var(--gold);">All</button>
                        <button class="secondary-button" style="white-space: nowrap;">Productivity</button>
                        <button class="secondary-button" style="white-space: nowrap;">Social</button>
                        <button class="secondary-button" style="white-space: nowrap;">Wellness</button>
                        <button class="secondary-button" style="white-space: nowrap;">Club Info</button>
                      </div>
                      
                      <!-- Featured Widgets -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Featured Widgets</h3>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--gold); display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 20px;">📊</span>
                          </div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Usage Analytics</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Track your club usage patterns</p>
                          </div>
                          <button class="secondary-button">Add</button>
                        </div>
                        <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 12px; display: flex; align-items: center; gap: 8px;">
                          <div style="width: 32px; height: 20px; background: var(--gold); border-radius: 4px;"></div>
                          <div style="width: 24px; height: 20px; background: var(--info); border-radius: 4px;"></div>
                          <div style="width: 40px; height: 20px; background: var(--success); border-radius: 4px;"></div>
                          <span style="font-size: 12px; color: var(--text-secondary); margin-left: auto;">Preview</span>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--info); display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 20px;">🎯</span>
                          </div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Goals Tracker</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Monitor fitness and networking goals</p>
                          </div>
                          <button class="primary-button">Added</button>
                        </div>
                        <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span style="font-size: 12px;">Workout Goal</span>
                            <span style="font-size: 12px; color: var(--success);">75%</span>
                          </div>
                          <div style="background: var(--border-color); height: 4px; border-radius: 2px;">
                            <div style="background: var(--success); height: 100%; width: 75%; border-radius: 2px;"></div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--success); display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 20px;">💬</span>
                          </div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Quick Chat</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Message members instantly</p>
                          </div>
                          <button class="secondary-button">Add</button>
                        </div>
                        <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 12px; display: flex; align-items: center; gap: 8px;">
                          <div style="width: 24px; height: 24px; border-radius: 12px; background: var(--gold);"></div>
                          <div style="width: 24px; height: 24px; border-radius: 12px; background: var(--info); margin-left: -8px;"></div>
                          <div style="width: 24px; height: 24px; border-radius: 12px; background: var(--success); margin-left: -8px;"></div>
                          <span style="font-size: 12px; color: var(--text-secondary); margin-left: auto;">3 online</span>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                          <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--error); display: flex; align-items: center; justify-content: center;">
                            <span style="color: white; font-size: 20px;">🔥</span>
                          </div>
                          <div style="flex: 1;">
                            <h4 style="font-size: 16px; font-weight: 600;">Live Activity</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Real-time club activity feed</p>
                          </div>
                          <button class="secondary-button">Add</button>
                        </div>
                        <div style="background: var(--bg-tertiary); border-radius: 8px; padding: 12px;">
                          <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                            <div style="width: 6px; height: 6px; border-radius: 3px; background: var(--success);"></div>
                            <span style="font-size: 11px; color: var(--text-secondary);">Sarah checked in</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="width: 6px; height: 6px; border-radius: 3px; background: var(--gold);"></div>
                            <span style="font-size: 11px; color: var(--text-secondary);">New event posted</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Layout Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Layout Designer</h1>
                    <div class="header-right">
                      <button class="icon-button">🎨</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Layout Templates -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Layout Templates</h3>
                      
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                        <div class="card" style="padding: 16px; cursor: pointer; border: 2px solid var(--gold);">
                          <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">Executive</h4>
                          <div style="display: grid; gap: 4px; opacity: 0.7;">
                            <div style="height: 12px; background: var(--gold); border-radius: 2px;"></div>
                            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 4px;">
                              <div style="height: 20px; background: var(--info); border-radius: 2px;"></div>
                              <div style="height: 20px; background: var(--success); border-radius: 2px;"></div>
                            </div>
                            <div style="height: 8px; background: var(--text-tertiary); border-radius: 2px;"></div>
                          </div>
                          <p style="font-size: 11px; color: var(--text-secondary); margin-top: 8px;">Business-focused layout</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; cursor: pointer; border: 2px solid transparent;">
                          <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">Social</h4>
                          <div style="display: grid; gap: 4px; opacity: 0.7;">
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 4px;">
                              <div style="height: 12px; background: var(--success); border-radius: 2px;"></div>
                              <div style="height: 12px; background: var(--info); border-radius: 2px;"></div>
                              <div style="height: 12px; background: var(--gold); border-radius: 2px;"></div>
                            </div>
                            <div style="height: 16px; background: var(--error); border-radius: 2px;"></div>
                            <div style="height: 8px; background: var(--text-tertiary); border-radius: 2px;"></div>
                          </div>
                          <p style="font-size: 11px; color: var(--text-secondary); margin-top: 8px;">Member-focused layout</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; cursor: pointer; border: 2px solid transparent;">
                          <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">Wellness</h4>
                          <div style="display: grid; gap: 4px; opacity: 0.7;">
                            <div style="height: 16px; background: var(--success); border-radius: 2px;"></div>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 4px;">
                              <div style="height: 12px; background: var(--info); border-radius: 2px;"></div>
                              <div style="height: 12px; background: var(--gold); border-radius: 2px;"></div>
                            </div>
                            <div style="height: 8px; background: var(--text-tertiary); border-radius: 2px;"></div>
                          </div>
                          <p style="font-size: 11px; color: var(--text-secondary); margin-top: 8px;">Health-focused layout</p>
                        </div>
                        
                        <div class="card" style="padding: 16px; cursor: pointer; border: 2px solid transparent;">
                          <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">Minimal</h4>
                          <div style="display: grid; gap: 4px; opacity: 0.7;">
                            <div style="height: 8px; background: var(--gold); border-radius: 2px;"></div>
                            <div style="height: 20px; background: var(--info); border-radius: 2px;"></div>
                            <div style="height: 8px; background: var(--success); border-radius: 2px;"></div>
                          </div>
                          <p style="font-size: 11px; color: var(--text-secondary); margin-top: 8px;">Clean, simple layout</p>
                        </div>
                      </div>
                      
                      <!-- Custom Layout Builder -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Custom Layout</h3>
                        <div style="border: 2px dashed var(--border-color); border-radius: 8px; padding: 16px; min-height: 120px; background: var(--bg-primary);">
                          <div style="display: grid; gap: 8px;">
                            <!-- Draggable widget areas -->
                            <div style="background: var(--gold); height: 24px; border-radius: 4px; display: flex; align-items: center; padding: 0 8px; cursor: move;">
                              <span style="font-size: 12px; color: white;">⋮⋮ Welcome & Membership</span>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                              <div style="background: var(--info); height: 32px; border-radius: 4px; display: flex; align-items: center; justify-content: center; cursor: move;">
                                <span style="font-size: 10px; color: white;">⋮⋮ Schedule</span>
                              </div>
                              <div style="background: var(--success); height: 32px; border-radius: 4px; display: flex; align-items: center; justify-content: center; cursor: move;">
                                <span style="font-size: 10px; color: white;">⋮⋮ Account</span>
                              </div>
                            </div>
                            <div style="background: var(--text-tertiary); height: 20px; border-radius: 4px; display: flex; align-items: center; padding: 0 8px; cursor: move;">
                              <span style="font-size: 10px; color: white;">⋮⋮ Quick Actions</span>
                            </div>
                          </div>
                        </div>
                        <div style="display: flex; gap: 8px; margin-top: 12px;">
                          <button class="secondary-button" style="flex: 1;">Reset</button>
                          <button class="primary-button" style="flex: 1;">Save Layout</button>
                        </div>
                      </div>
                      
                      <!-- Layout Settings -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Layout Settings</h3>
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Dense Mode</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Reduce spacing between widgets</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox">
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                          
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Auto-arrange</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Automatically organize widgets</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox" checked>
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Preferences Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Dashboard Preferences</h1>
                    <div class="header-right">
                      <button class="icon-button">⚙️</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Display Settings -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Display</h3>
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Show Member Status</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Display online/offline indicators</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox" checked>
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                          
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">24-Hour Time</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Use 24-hour time format</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox">
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                          
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Weather Widget</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Show weather on dashboard</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox" checked>
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Refresh Settings -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Refresh Settings</h3>
                        <div style="margin-bottom: 16px;">
                          <label style="font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">Auto-refresh Interval</label>
                          <select class="input-field" style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px;">
                            <option value="30">30 seconds</option>
                            <option value="60" selected>1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="600">10 minutes</option>
                            <option value="disabled">Disabled</option>
                          </select>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Refresh on Focus</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Update when returning to app</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                      
                      <!-- Privacy Settings -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Privacy</h3>
                        <div style="display: flex; flex-direction: column; gap: 16px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Show Recent Activity</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Display your club activity to others</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox" checked>
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                          
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">Anonymous Mode</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Hide your presence from other members</p>
                            </div>
                            <label class="toggle-switch">
                              <input type="checkbox">
                              <span class="toggle-slider"></span>
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Action Buttons -->
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                        <button class="secondary-button">Reset to Default</button>
                        <button class="primary-button">Save Changes</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Membership Section -->
        <section id="membership" class="flow-section">
          <h2>Membership</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Overview</button>
            <button class="tab-button">Benefits</button>
            <button class="tab-button">Partners</button>
          </div>
          
          <div class="tab-container">
            <!-- Membership Overview -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Membership</h1>
                  <div class="header-right">
                    <button class="icon-button">📤</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Digital Membership Card -->
                  <div class="membership-card" style="margin-bottom: 24px;">
                    <div class="membership-logo">🏛️</div>
                    <div class="membership-tier">FOUNDATION PRINCIPAL</div>
                    <div class="membership-name">DAN HUMPHREYS</div>
                    <div class="membership-details">
                      <div class="membership-detail">MEMBER NO. 2023-0042</div>
                      <div class="membership-detail">VALID THRU 12/25</div>
                    </div>
                  </div>
                  
                  <!-- Quick Stats -->
                  <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-bottom: 24px;">
                    <div class="card" style="text-align: center;">
                      <div style="font-size: 24px; font-weight: 700; color: var(--gold);">147</div>
                      <div style="font-size: 12px; color: var(--text-secondary);">Days Active</div>
                    </div>
                    <div class="card" style="text-align: center;">
                      <div style="font-size: 24px; font-weight: 700; color: var(--gold);">24</div>
                      <div style="font-size: 12px; color: var(--text-secondary);">Events Attended</div>
                    </div>
                    <div class="card" style="text-align: center;">
                      <div style="font-size: 24px; font-weight: 700; color: var(--gold);">12</div>
                      <div style="font-size: 12px; color: var(--text-secondary);">Guest Passes</div>
                    </div>
                  </div>
                  
                  <!-- Membership Benefits Summary -->
                  <div class="section-header">
                    <h3>Your Benefits</h3>
                    <button class="text-button">View All</button>
                  </div>
                  
                  <div class="benefits-list">
                    <div class="benefit-item card" style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                      <div style="font-size: 24px;">🏋️</div>
                      <div>
                        <h4 style="font-size: 16px; font-weight: 600;">Platinum Gym Access</h4>
                        <p style="font-size: 14px; color: var(--text-secondary);">Fitness First, Goodlife, or Zap</p>
                    </div>
                    </div>
                    
                    <div class="benefit-item card" style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                      <div style="font-size: 24px;">🎟️</div>
                      <div>
                        <h4 style="font-size: 16px; font-weight: 600;">Priority Event Access</h4>
                        <p style="font-size: 14px; color: var(--text-secondary);">Exclusive invitations and VIP experiences</p>
                      </div>
                    </div>
                    
                    <div class="benefit-item card" style="display: flex; align-items: center; gap: 16px; margin-bottom: 12px;">
                      <div style="font-size: 24px;">🧘</div>
                      <div>
                        <h4 style="font-size: 16px; font-weight: 600;">SALT Recovery Access</h4>
                        <p style="font-size: 14px; color: var(--text-secondary);">Sauna, spa, and ice bath facilities</p>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Renewal Status -->
                  <div class="renewal-status card" style="background: linear-gradient(135deg, var(--gold) 0%, #B8995D 100%); color: white; padding: 20px; text-align: center;">
                    <h3 style="font-size: 18px; margin-bottom: 8px;">Membership Valid Until</h3>
                    <div style="font-size: 32px; font-weight: 700; margin-bottom: 16px;">December 2025</div>
                    <button class="secondary-button" style="background-color: white; color: var(--gold); border: none;">Renew Early</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Benefits Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Member Benefits</h1>
                    <div class="header-right">
                      <button class="icon-button">🔍</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Benefit Categories -->
                      <div class="card" style="margin-bottom: 16px;">
                        <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px; color: var(--gold);">Exclusive Access</h3>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>24/7 Club Access</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Private Dining Rooms</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Executive Boardrooms</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Rooftop Terrace</span>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px; color: var(--gold);">Wellness & Lifestyle</h3>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Spa & Sauna Access</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Personal Training Sessions</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Wellness Programs</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Nutrition Consultations</span>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card">
                        <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px; color: var(--gold);">Concierge Services</h3>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Travel Arrangements</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Event Planning</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Restaurant Reservations</span>
                          </div>
                          <div style="display: flex; align-items: center; gap: 12px;">
                            <span style="color: var(--gold);">✓</span>
                            <span>Personal Shopping</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Partners Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Partner Network</h1>
                    <div class="header-right">
                      <button class="icon-button">🗺️</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Partner Categories -->
                      <div style="margin-bottom: 24px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 12px; color: var(--text-secondary);">International Clubs</h3>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">The Arts Club London</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Mayfair, London</p>
                            </div>
                            <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Full Access</span>
                          </div>
                        </div>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Core Club New York</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Midtown Manhattan</p>
                            </div>
                            <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Full Access</span>
                          </div>
                        </div>
                        
                        <div class="card">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Capital Club Dubai</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">DIFC, Dubai</p>
                            </div>
                            <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Full Access</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 12px; color: var(--text-secondary);">Lifestyle Partners</h3>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">NetJets</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Private Aviation</p>
                            </div>
                            <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">15% Discount</span>
                          </div>
                        </div>
                        
                        <div class="card">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Aman Resorts</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Luxury Hospitality</p>
                            </div>
                            <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">VIP Status</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Guest Management Section -->
        <section id="guest" class="flow-section">
          <h2>Guest Management</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Active Guests</button>
            <button class="tab-button">Invite Guest</button>
            <button class="tab-button">History</button>
          </div>
          
          <div class="tab-container">
            <!-- Active Guests Tab -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Guest Management</h1>
                  <div class="header-right">
                    <button class="icon-button">+</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Guest Pass Balance -->
                  <div class="card" style="background-color: var(--bg-tertiary); text-align: center; padding: 20px; margin-bottom: 24px;">
                    <div style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Available Guest Passes</div>
                    <div style="font-size: 48px; font-weight: 700; color: var(--gold);">12</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">of 24 annual passes</div>
                  </div>
                  
                  <!-- Active Guests -->
                  <div class="section-header">
                    <h3>Today's Guests</h3>
                    <button class="text-button">View All</button>
                  </div>
                  
                  <div class="guest-list">
                    <div class="guest-card">
                      <div class="guest-avatar">JW</div>
                      <div class="guest-info">
                        <div class="guest-name">James Wilson</div>
                        <div class="guest-detail">Lunch Meeting • 12:30 PM</div>
                      </div>
                      <div class="guest-status active">Active</div>
                    </div>
                    
                    <div class="guest-card">
                      <div class="guest-avatar">SC</div>
                      <div class="guest-info">
                        <div class="guest-name">Sarah Chen</div>
                        <div class="guest-detail">All Day Pass • Until 10 PM</div>
                      </div>
                      <div class="guest-status active">Active</div>
                    </div>
                  </div>
                  
                  <!-- Upcoming Guests -->
                  <div class="section-header">
                    <h3>Upcoming</h3>
                  </div>
                  
                  <div class="guest-list">
                    <div class="guest-card">
                      <div class="guest-avatar">MT</div>
                      <div class="guest-info">
                        <div class="guest-name">Michael Thompson</div>
                        <div class="guest-detail">Tomorrow • Board Meeting</div>
                      </div>
                      <div class="guest-status pending">Pending</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Invite Guest Tab -->
            <div class="tab-content">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Invite Guest</h1>
                  <div class="header-right">
                    <button class="text-button">Send</button>
                  </div>
                </div>
                
                <div class="content">
                  <form>
                    <div class="form-group">
                      <label class="form-label">Guest Name</label>
                      <input type="text" class="form-input" placeholder="Full name">
                    </div>
                    
                    <div class="form-group">
                      <label class="form-label">Email Address</label>
                      <input type="email" class="form-input" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                      <label class="form-label">Mobile Number</label>
                      <input type="tel" class="form-input" placeholder="+61 400 000 000">
                    </div>
                    
                    <div class="form-group">
                      <label class="form-label">Visit Date</label>
                      <input type="date" class="form-input">
                    </div>
                    
                    <div class="form-group">
                      <label class="form-label">Access Type</label>
                      <select class="form-input">
                        <option>All Day Pass</option>
                        <option>Lunch Guest (11 AM - 3 PM)</option>
                        <option>Dinner Guest (5 PM - 10 PM)</option>
                        <option>Meeting Room Access Only</option>
                      </select>
                    </div>
                    
                    <div class="form-group">
                      <label class="form-label">Purpose of Visit</label>
                      <textarea class="form-input" rows="3" placeholder="Optional notes"></textarea>
                    </div>
                    
                    <button type="submit" class="primary-button large">Send Guest Invitation</button>
                  </form>
                </div>
              </div>
            </div>
            
            <!-- History Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Guest History</h1>
                    <div class="header-right">
                      <button class="icon-button">🔍</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Date Filter -->
                      <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="secondary-button" style="flex: 1;">This Month</button>
                        <button class="secondary-button" style="flex: 1;">Last 3 Months</button>
                        <button class="secondary-button" style="flex: 1;">All Time</button>
                      </div>
                      
                      <!-- Guest History List -->
                      <div style="margin-bottom: 24px;">
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--text-secondary); margin-bottom: 12px;">DECEMBER 2024</h3>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Michael Chen</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Dec 15, 2024 • 2:00 PM - 6:00 PM</p>
                            </div>
                            <span style="color: var(--success); font-size: 14px;">Completed</span>
                          </div>
                        </div>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Sarah Williams</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Dec 10, 2024 • 11:00 AM - 3:00 PM</p>
                            </div>
                            <span style="color: var(--success); font-size: 14px;">Completed</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--text-secondary); margin-bottom: 12px;">NOVEMBER 2024</h3>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">David Park</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Nov 28, 2024 • 7:00 PM - 10:00 PM</p>
                            </div>
                            <span style="color: var(--success); font-size: 14px;">Completed</span>
                          </div>
                        </div>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Emma Thompson</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Nov 22, 2024 • 12:00 PM - 4:00 PM</p>
                            </div>
                            <span style="color: var(--text-tertiary); font-size: 14px;">Cancelled</span>
                          </div>
                        </div>
                        
                        <div class="card">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Alex Kumar</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Nov 15, 2024 • 6:00 PM - 9:00 PM</p>
                            </div>
                            <span style="color: var(--success); font-size: 14px;">Completed</span>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Stats Summary -->
                      <div class="card" style="margin-top: 24px; background-color: var(--bg-secondary);">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">Guest Statistics</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                          <div>
                            <p style="font-size: 24px; font-weight: 700; color: var(--gold);">47</p>
                            <p style="font-size: 14px; color: var(--text-secondary);">Total Guests This Year</p>
                          </div>
                          <div>
                            <p style="font-size: 24px; font-weight: 700; color: var(--gold);">8</p>
                            <p style="font-size: 14px; color: var(--text-secondary);">Unique Guests</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Events Section -->
        <section id="events" class="flow-section">
          <h2>Events & Experiences</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Upcoming</button>
            <button class="tab-button">My Bookings</button>
            <button class="tab-button">Categories</button>
            <button class="tab-button">Past Events</button>
          </div>
          
          <div class="tab-container">
            <!-- Upcoming Events -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Events</h1>
                  <div class="header-right">
                    <button class="icon-button">🔍</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Event Categories -->
                  <div class="filter-pills scrollable" style="margin-bottom: 20px;">
                    <button class="filter-pill active">All Events</button>
                    <button class="filter-pill">Sport</button>
                    <button class="filter-pill">Culture</button>
                    <button class="filter-pill">Dining</button>
                    <button class="filter-pill">Business</button>
                    <button class="filter-pill">Wellness</button>
                  </div>
                  
                  <!-- Featured Event -->
                  <div class="event-card" style="margin-bottom: 20px;">
                    <img src="https://via.placeholder.com/343x180/1a1a1a/666?text=Melbourne+Cup" alt="Melbourne Cup" class="event-image" style="height: 180px;">
                    <div class="event-content">
                      <div class="event-category">FEATURED</div>
                      <h4 class="event-title" style="font-size: 18px;">Melbourne Cup VIP Experience</h4>
                      <p class="event-details">Join SANCTUM members in the exclusive Birdcage with premium hospitality, luxury transfers, and all-day dining.</p>
                      <div class="event-meta">
                        <div class="meta-item">📅 Nov 2, 2025</div>
                        <div class="meta-item">💰 $2,500</div>
                      </div>
                      <button class="primary-button" style="width: 100%; margin-top: 16px;">Book Now</button>
                    </div>
                  </div>
                  
                  <!-- Event List -->
                  <div class="section-header">
                    <h3>This Week</h3>
                  </div>
                  
                  <div class="event-card">
                    <img src="https://via.placeholder.com/343x140/1a1a1a/666?text=Wine+Tasting" alt="Wine Tasting" class="event-image">
                    <div class="event-content">
                      <div class="event-category">DINING</div>
                      <h4 class="event-title">Yarra Valley Wine Masterclass</h4>
                      <p class="event-details">Exclusive tasting with renowned sommelier</p>
                      <div class="event-meta">
                        <div class="meta-item">📅 Jun 10</div>
                        <div class="meta-item">⏰ 7:00 PM</div>
                        <div class="meta-item">👥 20 seats</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="event-card">
                    <img src="https://via.placeholder.com/343x140/1a1a1a/666?text=AFL+Match" alt="AFL Match" class="event-image">
                    <div class="event-content">
                      <div class="event-category">SPORT</div>
                      <h4 class="event-title">AFL Premium Box: Collingwood vs Carlton</h4>
                      <p class="event-details">MCG corporate box with full catering</p>
                      <div class="event-meta">
                        <div class="meta-item">📅 Jun 12</div>
                        <div class="meta-item">⏰ 7:40 PM</div>
                        <div class="meta-item">👥 8 seats left</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- My Bookings Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>My Bookings</h1>
                    <div class="header-right">
                      <button class="icon-button">📅</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Booking Tabs -->
                      <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="secondary-button" style="flex: 1;">Upcoming</button>
                        <button class="secondary-button" style="flex: 1;">Past</button>
                      </div>
                      
                      <!-- Upcoming Bookings -->
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">Wine Tasting Evening</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">Tomorrow • 7:00 PM</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Confirmed</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                          <span>🍷 Wine Bar</span>
                          <span>👥 2 Guests</span>
                          <span>⏱️ 2 hours</span>
                        </div>
                        <div style="display: flex; gap: 8px;">
                          <button class="secondary-button" style="flex: 1;">View Details</button>
                          <button class="secondary-button" style="flex: 1;">Add to Calendar</button>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">Executive Leadership Talk</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">Dec 22 • 6:00 PM</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Confirmed</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                          <span>🎤 Main Hall</span>
                          <span>👥 1 Guest</span>
                          <span>⏱️ 90 mins</span>
                        </div>
                        <div style="display: flex; gap: 8px;">
                          <button class="secondary-button" style="flex: 1;">View Details</button>
                          <button class="secondary-button" style="flex: 1;">Cancel Booking</button>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">New Year's Gala</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">Dec 31 • 8:00 PM</p>
                          </div>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Waitlist</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">
                          <span>🎊 Grand Ballroom</span>
                          <span>👥 2 Guests</span>
                          <span>⏱️ All Night</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">You are #3 on the waitlist</p>
                        <button class="secondary-button" style="width: 100%;">Cancel Waitlist</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Categories Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Event Categories</h1>
                    <div class="header-right">
                      <button class="icon-button">🔍</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Category Grid -->
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">🍷</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Wine & Spirits</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">8 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">🎨</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Arts & Culture</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">5 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">💼</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Business</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">12 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">🍽️</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Culinary</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">10 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">🏃</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Wellness</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">6 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">🎵</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Music & Entertainment</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">7 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">👨‍👩‍👧‍👦</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Family</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">4 Events</p>
                        </div>
                        
                        <div class="card" style="text-align: center; padding: 24px; cursor: pointer;">
                          <div style="font-size: 48px; margin-bottom: 8px;">⚽</div>
                          <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Sports</h3>
                          <p style="font-size: 13px; color: var(--text-secondary);">9 Events</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Past Events Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Past Events</h1>
                    <div class="header-right">
                      <button class="icon-button">📸</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Past Events List -->
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; gap: 12px;">
                          <img src="#" alt="Event" style="width: 80px; height: 80px; border-radius: 8px; background-color: var(--bg-tertiary);">
                          <div style="flex: 1;">
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Autumn Wine Festival</h3>
                            <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Nov 15, 2024</p>
                            <div style="display: flex; gap: 8px;">
                              <button class="secondary-button" style="font-size: 13px;">View Photos</button>
                              <button class="secondary-button" style="font-size: 13px;">Rate Event</button>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; gap: 12px;">
                          <img src="#" alt="Event" style="width: 80px; height: 80px; border-radius: 8px; background-color: var(--bg-tertiary);">
                          <div style="flex: 1;">
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">CEO Roundtable Discussion</h3>
                            <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Nov 8, 2024</p>
                            <div style="display: flex; gap: 8px;">
                              <button class="secondary-button" style="font-size: 13px;">View Summary</button>
                              <button class="secondary-button" style="font-size: 13px;">Download Notes</button>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; gap: 12px;">
                          <img src="#" alt="Event" style="width: 80px; height: 80px; border-radius: 8px; background-color: var(--bg-tertiary);">
                          <div style="flex: 1;">
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Halloween Masquerade</h3>
                            <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Oct 31, 2024</p>
                            <div style="display: flex; gap: 8px;">
                              <button class="secondary-button" style="font-size: 13px;">View Photos</button>
                              <button class="secondary-button" style="font-size: 13px;">Share Memories</button>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; gap: 12px;">
                          <img src="#" alt="Event" style="width: 80px; height: 80px; border-radius: 8px; background-color: var(--bg-tertiary);">
                          <div style="flex: 1;">
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Summer Jazz Night</h3>
                            <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Aug 20, 2024</p>
                            <div style="display: flex; gap: 8px;">
                              <button class="secondary-button" style="font-size: 13px;">View Photos</button>
                              <button class="secondary-button" style="font-size: 13px;">Watch Highlights</button>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Load More -->
                      <button class="secondary-button" style="width: 100%; margin-top: 20px;">Load More Events</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Facilities Section -->
        <section id="facilities" class="flow-section">
          <h2>Facilities & Bookings</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Overview</button>
            <button class="tab-button">Meeting Rooms</button>
            <button class="tab-button">Wellness</button>
            <button class="tab-button">Private Dining</button>
          </div>
          
          <div class="tab-container">
            <!-- Facilities Overview -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Facilities</h1>
                  <div class="header-right">
                    <button class="icon-button">📅</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Quick Actions -->
                  <div class="quick-actions-grid">
                    <div class="quick-action-card">
                      <div class="action-icon">🏢</div>
                      <span>Meeting Rooms</span>
                    </div>
                    <div class="quick-action-card">
                      <div class="action-icon">🧘</div>
                      <span>Wellness</span>
                    </div>
                    <div class="quick-action-card">
                      <div class="action-icon">🍽️</div>
                      <span>Private Dining</span>
                    </div>
                    <div class="quick-action-card">
                      <div class="action-icon">🚗</div>
                      <span>Parking</span>
                    </div>
                  </div>
                  
                  <!-- Current Bookings -->
                  <div class="section-header">
                    <h3>Your Bookings Today</h3>
                    <button class="text-button">View All</button>
                  </div>
                  
                  <div class="booking-card">
                    <div class="booking-time">
                      <span class="time">2:00 PM</span>
                      <span class="duration">2 hours</span>
                    </div>
                    <div class="booking-details">
                      <h4>Executive Boardroom</h4>
                      <p>Level 18 • 8 attendees</p>
                    </div>
                    <button class="icon-button">→</button>
                  </div>
                  
                  <div class="booking-card">
                    <div class="booking-time">
                      <span class="time">6:00 PM</span>
                      <span class="duration">1 hour</span>
                    </div>
                    <div class="booking-details">
                      <h4>SALT Recovery Session</h4>
                      <p>Sauna + Ice Bath</p>
                    </div>
                    <button class="icon-button">→</button>
                  </div>
                  
                  <!-- Available Now -->
                  <div class="section-header">
                    <h3>Available Now</h3>
                  </div>
                  
                  <div class="facility-grid">
                    <div class="facility-card available">
                      <img src="https://via.placeholder.com/150x100/1a1a1a/666?text=Meeting+Room" alt="Meeting Room">
                      <div class="facility-info">
                        <h4>Harbour View Room</h4>
                        <p>4-6 people • Level 16</p>
                        <button class="primary-button small">Book Now</button>
                      </div>
                    </div>
                    
                    <div class="facility-card available">
                      <img src="https://via.placeholder.com/150x100/1a1a1a/666?text=Hot+Desk" alt="Hot Desk">
                      <div class="facility-info">
                        <h4>Hot Desk Zone</h4>
                        <p>Multiple available</p>
                        <button class="primary-button small">Reserve</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Meeting Rooms Tab -->
            <div class="tab-content">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Meeting Rooms</h1>
                  <div class="header-right">
                    <button class="icon-button">🔍</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Date & Time Selector -->
                  <div class="datetime-selector">
                    <input type="date" class="date-input" value="2025-06-07">
                    <select class="time-select">
                      <option>9:00 AM</option>
                      <option>10:00 AM</option>
                      <option selected>2:00 PM</option>
                      <option>3:00 PM</option>
                    </select>
                    <select class="duration-select">
                      <option>30 min</option>
                      <option>1 hour</option>
                      <option selected>2 hours</option>
                      <option>3 hours</option>
                    </select>
                  </div>
                  
                  <!-- Filters -->
                  <div class="filter-pills">
                    <button class="filter-pill active">All Rooms</button>
                    <button class="filter-pill">4-6 People</button>
                    <button class="filter-pill">8+ People</button>
                    <button class="filter-pill">Video Conf</button>
                  </div>
                  
                  <!-- Room List -->
                  <div class="room-list">
                    <div class="room-card available">
                      <img src="https://via.placeholder.com/100x100/1a1a1a/666?text=Room" alt="Executive Boardroom">
                      <div class="room-info">
                        <h4>Executive Boardroom</h4>
                        <p>Level 18 • Up to 12 people</p>
                        <div class="room-amenities">
                          <span class="amenity">📺 85" Display</span>
                          <span class="amenity">📹 Video Conf</span>
                          <span class="amenity">☕ Barista</span>
                        </div>
                        <div class="room-price">$150/hour</div>
                      </div>
                      <button class="primary-button">Book</button>
                    </div>
                    
                    <div class="room-card available">
                      <img src="https://via.placeholder.com/100x100/1a1a1a/666?text=Room" alt="Harbour View">
                      <div class="room-info">
                        <h4>Harbour View Conference</h4>
                        <p>Level 16 • Up to 8 people</p>
                        <div class="room-amenities">
                          <span class="amenity">📺 65" Display</span>
                          <span class="amenity">🌊 Water Views</span>
                        </div>
                        <div class="room-price">$100/hour</div>
                      </div>
                      <button class="primary-button">Book</button>
                    </div>
                    
                    <div class="room-card occupied">
                      <img src="https://via.placeholder.com/100x100/1a1a1a/666?text=Room" alt="Creative Studio">
                      <div class="room-info">
                        <h4>Creative Studio</h4>
                        <p>Level 15 • Up to 20 people</p>
                        <div class="room-amenities">
                          <span class="amenity">📐 Whiteboard Walls</span>
                          <span class="amenity">🎨 Design Tools</span>
                        </div>
                        <div class="room-availability">Available from 4:00 PM</div>
                      </div>
                      <button class="secondary-button">Notify Me</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Wellness Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Wellness Facilities</h1>
                    <div class="header-right">
                      <button class="icon-button">🗓️</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Wellness Options -->
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h3 style="font-size: 18px; font-weight: 600;">Spa & Sauna</h3>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Available</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Relax and rejuvenate in our luxury spa facilities</p>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏰ 6:00 AM - 10:00 PM</span>
                          <span>👥 4 spots available</span>
                        </div>
                        <button class="primary-button" style="width: 100%;">Book Session</button>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h3 style="font-size: 18px; font-weight: 600;">Fitness Center</h3>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Busy</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">State-of-the-art equipment and personal training</p>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏰ 5:00 AM - 11:00 PM</span>
                          <span>👥 85% capacity</span>
                        </div>
                        <button class="secondary-button" style="width: 100%;">View Schedule</button>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h3 style="font-size: 18px; font-weight: 600;">Pool & Jacuzzi</h3>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Available</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Olympic-size pool and relaxing hot tub</p>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏰ 6:00 AM - 9:00 PM</span>
                          <span>👥 Open swim</span>
                        </div>
                        <button class="primary-button" style="width: 100%;">Reserve Lane</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Private Dining Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Private Dining</h1>
                    <div class="header-right">
                      <button class="icon-button">📅</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Date Selection -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">Select Date & Time</h3>
                        <input type="date" class="input-field" style="margin-bottom: 12px;">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                          <button class="secondary-button">6:00 PM</button>
                          <button class="secondary-button">6:30 PM</button>
                          <button class="secondary-button">7:00 PM</button>
                          <button class="secondary-button">7:30 PM</button>
                          <button class="secondary-button">8:00 PM</button>
                          <button class="secondary-button">8:30 PM</button>
                        </div>
                      </div>
                      
                      <!-- Room Options -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">Available Rooms</h3>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">The Gold Room</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Up to 12 guests</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$500</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Elegant space with city views, perfect for intimate gatherings</p>
                        <button class="primary-button" style="width: 100%;">Reserve This Room</button>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">The Wine Cellar</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Up to 8 guests</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$350</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Intimate setting surrounded by our curated wine collection</p>
                        <button class="primary-button" style="width: 100%;">Reserve This Room</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- F&B Ordering Section -->
        <section id="fnb" class="flow-section">
          <h2>Food & Beverage</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Menu</button>
            <button class="tab-button">Cart</button>
            <button class="tab-button">Order Tracking</button>
            <button class="tab-button">Table Service</button>
          </div>
          
          <div class="tab-container">
            <!-- Menu Tab -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>12:30 PM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Menu</h1>
                  <div class="header-right">
                    <button class="icon-button cart-icon">
                      🛒
                      <span class="badge">3</span>
                    </button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Location Selector -->
                  <div class="location-selector" style="display: flex; align-items: center; gap: 12px; padding: 12px; background-color: var(--bg-tertiary); border-radius: 12px; margin-bottom: 16px;">
                    <div class="location-icon">📍</div>
                    <div class="location-info" style="flex: 1;">
                      <h4 style="font-size: 16px; font-weight: 600;">Level 18 Lounge</h4>
                      <p style="font-size: 14px; color: var(--text-secondary);">Table 12</p>
                    </div>
                    <button class="text-button">Change</button>
                  </div>
                  
                  <!-- Menu Categories -->
                  <div class="menu-categories" style="display: flex; gap: 8px; overflow-x: auto; margin-bottom: 20px; padding-bottom: 8px;">
                    <button class="category-pill active">All Day</button>
                    <button class="category-pill">Breakfast</button>
                    <button class="category-pill">Lunch</button>
                    <button class="category-pill">Dinner</button>
                    <button class="category-pill">Beverages</button>
                    <button class="category-pill">Wine List</button>
                  </div>
                  
                  <!-- Featured Items -->
                  <div class="section-header">
                    <h3>Chef's Recommendations</h3>
                  </div>
                  
                  <div class="menu-item featured">
                    <img src="https://via.placeholder.com/120x120/1a1a1a/666?text=Wagyu" alt="Wagyu Burger">
                    <div class="item-details">
                      <div class="item-header">
                        <h4>Wagyu Beef Burger</h4>
                        <span class="price">$42</span>
                      </div>
                      <p>Tasmanian wagyu, aged cheddar, truffle aioli, brioche bun</p>
                      <div class="item-tags">
                        <span class="dietary-tag">GF Available</span>
                        <span class="popular-tag" style="background-color: var(--gold); color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;">Popular</span>
                      </div>
                    </div>
                    <button class="add-button">+</button>
                  </div>
                  
                  <!-- Menu Sections -->
                  <div class="menu-section">
                    <h3>Small Plates</h3>
                    
                    <div class="menu-item">
                      <img src="https://via.placeholder.com/80x80/1a1a1a/666?text=Oysters" alt="Oysters">
                      <div class="item-details">
                        <div class="item-header">
                          <h4>Sydney Rock Oysters</h4>
                          <span class="price">$6 ea</span>
                        </div>
                        <p>Mignonette, lemon</p>
                        <div class="quantity-selector" style="display: flex; align-items: center; gap: 12px;">
                          <button class="qty-button" style="width: 28px; height: 28px; border-radius: 14px; border: 1px solid var(--border-color); background: none; cursor: pointer;">-</button>
                          <span class="qty">6</span>
                          <button class="qty-button" style="width: 28px; height: 28px; border-radius: 14px; border: 1px solid var(--border-color); background: none; cursor: pointer;">+</button>
                        </div>
                      </div>
                    </div>
                    
                    <div class="menu-item">
                      <img src="https://via.placeholder.com/80x80/1a1a1a/666?text=Tuna" alt="Tuna Tartare">
                      <div class="item-details">
                        <div class="item-header">
                          <h4>Bluefin Tuna Tartare</h4>
                          <span class="price">$28</span>
                        </div>
                        <p>Avocado, ponzu, crispy rice</p>
                        <div class="item-tags">
                          <span class="dietary-tag">DF</span>
                          <span class="dietary-tag">GF</span>
                        </div>
                      </div>
                      <button class="add-button">+</button>
                    </div>
                  </div>
                  
                  <!-- Wine Pairing Suggestion -->
                  <div class="wine-suggestion" style="background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%); border-radius: 16px; padding: 20px; margin-top: 24px;">
                    <div class="suggestion-header" style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                      <span class="wine-icon" style="font-size: 32px;">🍷</span>
                      <h4 style="font-size: 18px; margin-bottom: 8px;">Perfect Pairing</h4>
                    </div>
                    <div class="wine-details">
                      <h5 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">2019 Penfolds Grange</h5>
                      <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Shiraz • South Australia</p>
                      <div class="wine-price" style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <span class="glass-price" style="font-size: 16px; font-weight: 600; color: var(--gold);">$85/glass</span>
                        <span class="bottle-price" style="font-size: 16px; font-weight: 600; color: var(--gold);">$950/bottle</span>
                      </div>
                    </div>
                    <button class="secondary-button">Add to Order</button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Cart Tab -->
            <div class="tab-content">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>12:35 PM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Your Order</h1>
                  <div class="header-right">
                    <button class="text-button">Clear</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Order Type -->
                  <div class="order-type-selector">
                    <button class="order-type active">
                      <span class="icon">🍽️</span>
                      <span>Dine In</span>
                    </button>
                    <button class="order-type">
                      <span class="icon">📦</span>
                      <span>Takeaway</span>
                    </button>
                    <button class="order-type">
                      <span class="icon">🚪</span>
                      <span>Room Service</span>
                    </button>
                  </div>
                  
                  <!-- Cart Items -->
                  <div class="cart-items">
                    <div class="cart-item">
                      <div class="item-quantity">1x</div>
                      <div class="item-details">
                        <h4>Wagyu Beef Burger</h4>
                        <p class="modifications">No onions, add bacon</p>
                      </div>
                      <div class="item-price">$42</div>
                      <button class="remove-button">×</button>
                    </div>
                    
                    <div class="cart-item">
                      <div class="item-quantity">6x</div>
                      <div class="item-details">
                        <h4>Sydney Rock Oysters</h4>
                      </div>
                      <div class="item-price">$36</div>
                      <button class="remove-button">×</button>
                    </div>
                    
                    <div class="cart-item">
                      <div class="item-quantity">1x</div>
                      <div class="item-details">
                        <h4>2019 Penfolds Grange</h4>
                        <p>Glass</p>
                      </div>
                      <div class="item-price">$85</div>
                      <button class="remove-button">×</button>
                    </div>
                  </div>
                  
                  <!-- Special Requests -->
                  <div class="special-requests">
                    <h3>Special Requests</h3>
                    <textarea placeholder="Allergies, preferences, or special instructions..."></textarea>
                  </div>
                  
                  <!-- Order Summary -->
                  <div class="order-summary">
                    <div class="summary-row">
                      <span>Subtotal</span>
                      <span>$163.00</span>
                    </div>
                    <div class="summary-row">
                      <span>Service Charge (10%)</span>
                      <span>$16.30</span>
                    </div>
                    <div class="summary-row">
                      <span>GST</span>
                      <span>$17.93</span>
                    </div>
                    <div class="summary-row total">
                      <span>Total</span>
                      <span>$197.23</span>
                    </div>
                  </div>
                  
                  <!-- Payment Method -->
                  <div class="payment-method">
                    <h3>Payment Method</h3>
                    <div class="payment-option selected">
                      <span class="card-icon">💳</span>
                      <div class="card-details">
                        <span>•••• 4242</span>
                        <span class="card-type">Member Account</span>
                      </div>
                      <button class="text-button">Change</button>
                    </div>
                  </div>
                  
                  <button class="primary-button large">Place Order - $197.23</button>
                </div>
              </div>
            </div>
            
            <!-- Order Tracking Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Order Tracking</h1>
                    <div class="header-right">
                      <button class="icon-button">🔔</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Active Order -->
                      <div class="card" style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                          <h3 style="font-size: 18px; font-weight: 600;">Order #2847</h3>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Preparing</span>
                        </div>
                        
                        <!-- Progress Steps -->
                        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                          <div style="text-align: center; flex: 1;">
                            <div style="width: 40px; height: 40px; background: var(--success); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; margin: 0 auto 8px;">✓</div>
                            <p style="font-size: 12px;">Confirmed</p>
                          </div>
                          <div style="flex: 1; display: flex; align-items: center;">
                            <div style="height: 2px; background: var(--success); width: 100%;"></div>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="width: 40px; height: 40px; background: var(--gold); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; margin: 0 auto 8px;">🍳</div>
                            <p style="font-size: 12px;">Preparing</p>
                          </div>
                          <div style="flex: 1; display: flex; align-items: center;">
                            <div style="height: 2px; background: var(--border-color); width: 100%;"></div>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="width: 40px; height: 40px; background: var(--bg-tertiary); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">🚶</div>
                            <p style="font-size: 12px;">On the Way</p>
                          </div>
                        </div>
                        
                        <p style="font-size: 14px; text-align: center; color: var(--text-secondary);">Estimated delivery: 15-20 minutes</p>
                      </div>
                      
                      <!-- Order Items -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Order Details</h3>
                        <div style="border-bottom: 1px solid var(--border-color); padding-bottom: 12px; margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-weight: 500;">2x Wagyu Beef Burger</span>
                            <span style="font-weight: 500;">$78</span>
                          </div>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Medium, Extra cheese, No onions</p>
                        </div>
                        <div style="border-bottom: 1px solid var(--border-color); padding-bottom: 12px; margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-weight: 500;">1x Caesar Salad</span>
                            <span style="font-weight: 500;">$24</span>
                          </div>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Extra dressing</p>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-weight: 600; font-size: 16px;">
                          <span>Total</span>
                          <span>$102</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Table Service Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Table Service</h1>
                    <div class="header-right">
                      <button class="icon-button">🔔</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Table Info -->
                      <div class="card" style="margin-bottom: 20px; background: var(--gold); color: white;">
                        <div style="text-align: center;">
                          <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 8px;">Table 12</h2>
                          <p style="font-size: 16px;">Main Dining Room</p>
                        </div>
                      </div>
                      
                      <!-- Quick Actions -->
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">🍽️</div>
                          <p style="font-size: 14px; font-weight: 600;">View Menu</p>
                        </button>
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">🛎️</div>
                          <p style="font-size: 14px; font-weight: 600;">Call Server</p>
                        </button>
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">🧾</div>
                          <p style="font-size: 14px; font-weight: 600;">View Bill</p>
                        </button>
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">💳</div>
                          <p style="font-size: 14px; font-weight: 600;">Pay Now</p>
                        </button>
                      </div>
                      
                      <!-- Current Order -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Current Order</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                          <div>
                            <p style="font-weight: 500;">Appetizers</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Oysters, Bruschetta</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Served</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                          <div>
                            <p style="font-weight: 500;">Main Course</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Ribeye Steak, Salmon</p>
                          </div>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Preparing</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- F&B ORDER TRACKING -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Order Tracking</h1>
                    <div class="header-right">
                      <button class="icon-button">🔔</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Active Order -->
                      <div class="card" style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                          <h3 style="font-size: 18px; font-weight: 600;">Order #2847</h3>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Preparing</span>
                        </div>
                        
                        <!-- Progress Steps -->
                        <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                          <div style="text-align: center; flex: 1;">
                            <div style="width: 40px; height: 40px; background: var(--success); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; margin: 0 auto 8px;">✓</div>
                            <p style="font-size: 12px;">Confirmed</p>
                          </div>
                          <div style="flex: 1; display: flex; align-items: center;">
                            <div style="height: 2px; background: var(--success); width: 100%;"></div>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="width: 40px; height: 40px; background: var(--gold); border-radius: 20px; display: flex; align-items: center; justify-content: center; color: white; margin: 0 auto 8px;">🍳</div>
                            <p style="font-size: 12px;">Preparing</p>
                          </div>
                          <div style="flex: 1; display: flex; align-items: center;">
                            <div style="height: 2px; background: var(--border-color); width: 100%;"></div>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="width: 40px; height: 40px; background: var(--bg-tertiary); border-radius: 20px; display: flex; align-items: center; justify-content: center; margin: 0 auto 8px;">🚶</div>
                            <p style="font-size: 12px;">On the Way</p>
                          </div>
                        </div>
                        
                        <p style="font-size: 14px; text-align: center; color: var(--text-secondary);">Estimated delivery: 15-20 minutes</p>
                      </div>
                      
                      <!-- Order Items -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Order Details</h3>
                        <div style="border-bottom: 1px solid var(--border-color); padding-bottom: 12px; margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-weight: 500;">2x Wagyu Beef Burger</span>
                            <span style="font-weight: 500;">$78</span>
                          </div>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Medium, Extra cheese, No onions</p>
                        </div>
                        <div style="border-bottom: 1px solid var(--border-color); padding-bottom: 12px; margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <span style="font-weight: 500;">1x Caesar Salad</span>
                            <span style="font-weight: 500;">$24</span>
                          </div>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Extra dressing</p>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-weight: 600; font-size: 16px;">
                          <span>Total</span>
                          <span>$102</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- F&B TABLE SERVICE -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Table Service</h1>
                    <div class="header-right">
                      <button class="icon-button">🔔</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Table Info -->
                      <div class="card" style="margin-bottom: 20px; background: var(--gold); color: white;">
                        <div style="text-align: center;">
                          <h2 style="font-size: 24px; font-weight: 700; margin-bottom: 8px;">Table 12</h2>
                          <p style="font-size: 16px;">Main Dining Room</p>
                        </div>
                      </div>
                      
                      <!-- Quick Actions -->
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">🍽️</div>
                          <p style="font-size: 14px; font-weight: 600;">View Menu</p>
                        </button>
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">🛎️</div>
                          <p style="font-size: 14px; font-weight: 600;">Call Server</p>
                        </button>
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">🧾</div>
                          <p style="font-size: 14px; font-weight: 600;">View Bill</p>
                        </button>
                        <button class="card" style="padding: 20px; text-align: center; cursor: pointer; border: none;">
                          <div style="font-size: 32px; margin-bottom: 8px;">💳</div>
                          <p style="font-size: 14px; font-weight: 600;">Pay Now</p>
                        </button>
                      </div>
                      
                      <!-- Current Order -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Current Order</h3>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                          <div>
                            <p style="font-weight: 500;">Appetizers</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Oysters, Bruschetta</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Served</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px 0;">
                          <div>
                            <p style="font-weight: 500;">Main Course</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Ribeye Steak, Salmon</p>
                          </div>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Preparing</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Wellness Section -->
        <section id="wellness" class="flow-section">
          <h2>Wellness & Fitness</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Dashboard</button>
            <button class="tab-button">Book Session</button>
            <button class="tab-button">My Progress</button>
            <button class="tab-button">Challenges</button>
          </div>
          
          <div class="tab-container">
            <!-- Wellness Dashboard -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>6:00 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Wellness</h1>
                  <div class="header-right">
                    <button class="icon-button">📊</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Wellness Score -->
                  <div class="wellness-score-card">
                    <div class="score-circle">
                      <svg viewBox="0 0 200 200">
                        <circle cx="100" cy="100" r="90" fill="none" stroke="#e5e5e5" stroke-width="20"/>
                        <circle cx="100" cy="100" r="90" fill="none" stroke="#C9AB6E" stroke-width="20" 
                                stroke-dasharray="565.48" stroke-dashoffset="141.37" 
                                transform="rotate(-90 100 100)"/>
                      </svg>
                      <div class="score-value">
                        <span class="number">87</span>
                        <span class="label">Wellness Score</span>
                      </div>
                    </div>
                    <div class="score-breakdown">
                      <div class="metric">
                        <span class="icon">🏃</span>
                        <span class="value">4.2k</span>
                        <span class="label">Steps Today</span>
                      </div>
                      <div class="metric">
                        <span class="icon">🧘</span>
                        <span class="value">45m</span>
                        <span class="label">Mindfulness</span>
                      </div>
                      <div class="metric">
                        <span class="icon">💤</span>
                        <span class="value">7.5h</span>
                        <span class="label">Sleep</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Today's Schedule -->
                  <div class="section-header">
                    <h3>Today's Activities</h3>
                    <button class="text-button">View Week</button>
                  </div>
                  
                  <div class="activity-timeline">
                    <div class="activity-item completed">
                      <div class="time">6:30 AM</div>
                      <div class="activity-details">
                        <h4>Morning Yoga</h4>
                        <p>With Sarah Chen • Studio A</p>
                      </div>
                      <span class="status">✓</span>
                    </div>
                    
                    <div class="activity-item upcoming">
                      <div class="time">12:00 PM</div>
                      <div class="activity-details">
                        <h4>HIIT Training</h4>
                        <p>With Mike Johnson • Gym Floor</p>
                      </div>
                      <button class="text-button">Join</button>
                    </div>
                    
                    <div class="activity-item upcoming">
                      <div class="time">6:00 PM</div>
                      <div class="activity-details">
                        <h4>Recovery Session</h4>
                        <p>Sauna + Ice Bath • SALT Studio</p>
                      </div>
                      <button class="text-button">Check In</button>
                    </div>
                  </div>
                  
                  <!-- Quick Actions -->
                  <div class="wellness-actions">
                    <button class="action-card">
                      <span class="icon">📱</span>
                      <span>Virtual PT</span>
                    </button>
                    <button class="action-card">
                      <span class="icon">🥗</span>
                      <span>Meal Plan</span>
                    </button>
                    <button class="action-card">
                      <span class="icon">📈</span>
                      <span>Body Scan</span>
                    </button>
                    <button class="action-card">
                      <span class="icon">🏆</span>
                      <span>Challenges</span>
                    </button>
                  </div>
                  
                  <!-- Wellness Insights -->
                  <div class="insights-card">
                    <h3>Weekly Insights</h3>
                    <div class="insight">
                      <span class="icon">⬆️</span>
                      <p>Your activity level increased by 23% this week</p>
                    </div>
                    <div class="insight">
                      <span class="icon">💪</span>
                      <p>You're 2 sessions away from your monthly goal</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Book Session Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Book Session</h1>
                    <div class="header-right">
                      <button class="icon-button">📅</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Session Types -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Choose Session Type</h3>
                      <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="secondary-button" style="flex: 1;">Personal Training</button>
                        <button class="secondary-button" style="flex: 1;">Group Class</button>
                        <button class="secondary-button" style="flex: 1;">Spa Treatment</button>
                      </div>
                      
                      <!-- Available Sessions -->
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">HIIT Training</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">with Coach Michael</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$120</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏱️ 60 mins</span>
                          <span>🔥 High intensity</span>
                          <span>👥 1-on-1</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                          <button class="secondary-button">7:00 AM</button>
                          <button class="secondary-button">9:00 AM</button>
                          <button class="secondary-button">4:00 PM</button>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Yoga Flow</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">with Sarah Chen</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$80</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏱️ 75 mins</span>
                          <span>🧘 All levels</span>
                          <span>👥 Group class</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                          <button class="secondary-button">6:30 AM</button>
                          <button class="secondary-button">12:00 PM</button>
                          <button class="secondary-button">6:00 PM</button>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Deep Tissue Massage</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Spa Treatment</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$180</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏱️ 90 mins</span>
                          <span>💆 Therapeutic</span>
                          <span>👤 Individual</span>
                        </div>
                        <button class="primary-button" style="width: 100%;">View Available Times</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- My Progress Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>My Progress</h1>
                    <div class="header-right">
                      <button class="icon-button">📊</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Stats Overview -->
                      <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, var(--gold), #B8995D);">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; text-align: center; color: white;">
                          <div>
                            <p style="font-size: 32px; font-weight: 700;">42</p>
                            <p style="font-size: 14px;">Sessions</p>
                          </div>
                          <div>
                            <p style="font-size: 32px; font-weight: 700;">156</p>
                            <p style="font-size: 14px;">Hours</p>
                          </div>
                          <div>
                            <p style="font-size: 32px; font-weight: 700;">8</p>
                            <p style="font-size: 14px;">Streak</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Weekly Activity -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">This Week</h3>
                        <div style="display: flex; justify-content: space-between; align-items: end; height: 120px;">
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 60%;" title="Mon"></div>
                            <p style="font-size: 12px; margin-top: 8px;">M</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 80%;" title="Tue"></div>
                            <p style="font-size: 12px; margin-top: 8px;">T</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--bg-tertiary); width: 100%; height: 0%;" title="Wed"></div>
                            <p style="font-size: 12px; margin-top: 8px;">W</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 100%;" title="Thu"></div>
                            <p style="font-size: 12px; margin-top: 8px;">T</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 40%;" title="Fri"></div>
                            <p style="font-size: 12px; margin-top: 8px;">F</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--bg-tertiary); width: 100%; height: 0%;" title="Sat"></div>
                            <p style="font-size: 12px; margin-top: 8px;">S</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--bg-tertiary); width: 100%; height: 0%;" title="Sun"></div>
                            <p style="font-size: 12px; margin-top: 8px;">S</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Achievements -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Achievements</h3>
                        <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                          <div style="font-size: 32px;">🏃</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Marathon Ready</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Completed 10k run training</p>
                          </div>
                        </div>
                        <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                          <div style="font-size: 32px;">💪</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Strength Master</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Lifted 200% body weight</p>
                          </div>
                        </div>
                        <div style="display: flex; gap: 12px;">
                          <div style="font-size: 32px;">🧘</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Zen Mode</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">30 days of meditation</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Challenges Tab -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Challenges</h1>
                    <div class="header-right">
                      <button class="icon-button">🏆</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Active Challenges -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Active Challenges</h3>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h4 style="font-size: 16px; font-weight: 600;">December Fitness Challenge</h4>
                          <span style="font-size: 24px;">🎄</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Complete 20 workouts this month</p>
                        <div style="margin-bottom: 8px;">
                          <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 4px;">
                            <span>Progress</span>
                            <span>15/20 workouts</span>
                          </div>
                          <div style="background: var(--bg-tertiary); height: 8px; border-radius: 4px;">
                            <div style="background: var(--gold); height: 100%; width: 75%; border-radius: 4px;"></div>
                          </div>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">6 days remaining</p>
                      </div>
                      
                      <div class="card" style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h4 style="font-size: 16px; font-weight: 600;">Step Challenge</h4>
                          <span style="font-size: 24px;">👟</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Walk 10,000 steps daily for 30 days</p>
                        <div style="margin-bottom: 8px;">
                          <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 4px;">
                            <span>Current Streak</span>
                            <span>8 days</span>
                          </div>
                          <div style="background: var(--bg-tertiary); height: 8px; border-radius: 4px;">
                            <div style="background: var(--success); height: 100%; width: 27%; border-radius: 4px;"></div>
                          </div>
                        </div>
                        <button class="secondary-button" style="width: 100%;">View Details</button>
                      </div>
                      
                      <!-- Upcoming Challenges -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Join New Challenge</h3>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                          <h4 style="font-size: 16px; font-weight: 600;">New Year Transformation</h4>
                          <span style="font-size: 24px;">🎊</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">6-week body transformation program</p>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Starts January 1st • 42 members joined</p>
                        <button class="primary-button" style="width: 100%;">Join Challenge</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- WELLNESS BOOK SESSION -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Book Session</h1>
                    <div class="header-right">
                      <button class="icon-button">📅</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Session Types -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Choose Session Type</h3>
                      <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="secondary-button" style="flex: 1;">Personal Training</button>
                        <button class="secondary-button" style="flex: 1;">Group Class</button>
                        <button class="secondary-button" style="flex: 1;">Spa Treatment</button>
                      </div>
                      
                      <!-- Available Sessions -->
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">HIIT Training</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">with Coach Michael</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$120</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏱️ 60 mins</span>
                          <span>🔥 High intensity</span>
                          <span>👥 1-on-1</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                          <button class="secondary-button">7:00 AM</button>
                          <button class="secondary-button">9:00 AM</button>
                          <button class="secondary-button">4:00 PM</button>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Yoga Flow</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">with Sarah Chen</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$80</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏱️ 75 mins</span>
                          <span>🧘 All levels</span>
                          <span>👥 Group class</span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px;">
                          <button class="secondary-button">6:30 AM</button>
                          <button class="secondary-button">12:00 PM</button>
                          <button class="secondary-button">6:00 PM</button>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Deep Tissue Massage</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Spa Treatment</p>
                          </div>
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$180</span>
                        </div>
                        <div style="display: flex; gap: 16px; font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">
                          <span>⏱️ 90 mins</span>
                          <span>💆 Therapeutic</span>
                          <span>👤 Individual</span>
                        </div>
                        <button class="primary-button" style="width: 100%;">View Available Times</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- WELLNESS MY PROGRESS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>My Progress</h1>
                    <div class="header-right">
                      <button class="icon-button">📊</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Stats Overview -->
                      <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, var(--gold), #B8995D);">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; text-align: center; color: white;">
                          <div>
                            <p style="font-size: 32px; font-weight: 700;">42</p>
                            <p style="font-size: 14px;">Sessions</p>
                          </div>
                          <div>
                            <p style="font-size: 32px; font-weight: 700;">156</p>
                            <p style="font-size: 14px;">Hours</p>
                          </div>
                          <div>
                            <p style="font-size: 32px; font-weight: 700;">8</p>
                            <p style="font-size: 14px;">Streak</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Weekly Activity -->
                      <div class="card" style="margin-bottom: 20px;">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">This Week</h3>
                        <div style="display: flex; justify-content: space-between; align-items: end; height: 120px;">
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 60%;" title="Mon"></div>
                            <p style="font-size: 12px; margin-top: 8px;">M</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 80%;" title="Tue"></div>
                            <p style="font-size: 12px; margin-top: 8px;">T</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--bg-tertiary); width: 100%; height: 0%;" title="Wed"></div>
                            <p style="font-size: 12px; margin-top: 8px;">W</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 100%;" title="Thu"></div>
                            <p style="font-size: 12px; margin-top: 8px;">T</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--gold); width: 100%; height: 40%;" title="Fri"></div>
                            <p style="font-size: 12px; margin-top: 8px;">F</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--bg-tertiary); width: 100%; height: 0%;" title="Sat"></div>
                            <p style="font-size: 12px; margin-top: 8px;">S</p>
                          </div>
                          <div style="text-align: center; flex: 1;">
                            <div style="background: var(--bg-tertiary); width: 100%; height: 0%;" title="Sun"></div>
                            <p style="font-size: 12px; margin-top: 8px;">S</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Achievements -->
                      <div class="card">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Achievements</h3>
                        <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                          <div style="font-size: 32px;">🏃</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Marathon Ready</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Completed 10k run training</p>
                          </div>
                        </div>
                        <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                          <div style="font-size: 32px;">💪</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Strength Master</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">Lifted 200% body weight</p>
                          </div>
                        </div>
                        <div style="display: flex; gap: 12px;">
                          <div style="font-size: 32px;">🧘</div>
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Zen Mode</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">30 days of meditation</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- WELLNESS CHALLENGES -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Challenges</h1>
                    <div class="header-right">
                      <button class="icon-button">🏆</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Active Challenges -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Active Challenges</h3>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h4 style="font-size: 16px; font-weight: 600;">December Fitness Challenge</h4>
                          <span style="font-size: 24px;">🎄</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Complete 20 workouts this month</p>
                        <div style="margin-bottom: 8px;">
                          <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 4px;">
                            <span>Progress</span>
                            <span>15/20 workouts</span>
                          </div>
                          <div style="background: var(--bg-tertiary); height: 8px; border-radius: 4px;">
                            <div style="background: var(--gold); height: 100%; width: 75%; border-radius: 4px;"></div>
                          </div>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">6 days remaining</p>
                      </div>
                      
                      <div class="card" style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h4 style="font-size: 16px; font-weight: 600;">Step Challenge</h4>
                          <span style="font-size: 24px;">👟</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Walk 10,000 steps daily for 30 days</p>
                        <div style="margin-bottom: 8px;">
                          <div style="display: flex; justify-content: space-between; font-size: 13px; margin-bottom: 4px;">
                            <span>Current Streak</span>
                            <span>8 days</span>
                          </div>
                          <div style="background: var(--bg-tertiary); height: 8px; border-radius: 4px;">
                            <div style="background: var(--success); height: 100%; width: 27%; border-radius: 4px;"></div>
                          </div>
                        </div>
                        <button class="secondary-button" style="width: 100%;">View Details</button>
                      </div>
                      
                      <!-- Upcoming Challenges -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Join New Challenge</h3>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                          <h4 style="font-size: 16px; font-weight: 600;">New Year Transformation</h4>
                          <span style="font-size: 24px;">🎊</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">6-week body transformation program</p>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Starts January 1st • 42 members joined</p>
                        <button class="primary-button" style="width: 100%;">Join Challenge</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Social & Directory Section -->
        <section id="social" class="flow-section">
          <h2>Member Directory & Social</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Directory</button>
            <button class="tab-button">Messages</button>
            <button class="tab-button">Groups</button>
            <button class="tab-button">Feed</button>
          </div>
          
          <div class="tab-container">
            <!-- Member Directory -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>3:45 PM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Members</h1>
                  <div class="header-right">
                    <button class="icon-button">🔍</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Search Bar -->
                  <div class="search-container">
                    <input type="text" placeholder="Search members by name, company, or interests" class="search-input">
                    <button class="filter-button">
                      <span>Filters</span>
                      <span class="badge">2</span>
                    </button>
                  </div>
                  
                  <!-- Industry Filters -->
                  <div class="filter-pills scrollable">
                    <button class="filter-pill">All Members</button>
                    <button class="filter-pill active">Finance</button>
                    <button class="filter-pill active">Technology</button>
                    <button class="filter-pill">Real Estate</button>
                    <button class="filter-pill">Legal</button>
                    <button class="filter-pill">Media</button>
                  </div>
                  
                  <!-- Member List -->
                  <div class="member-list">
                    <div class="member-card">
                      <img src="https://via.placeholder.com/60x60/1a1a1a/666?text=JD" alt="James Davidson" class="member-avatar">
                      <div class="member-info">
                        <h4>James Davidson</h4>
                        <p>CEO • Davidson Capital</p>
                        <div class="member-tags">
                          <span class="tag">Private Equity</span>
                          <span class="tag">Golf</span>
                        </div>
                      </div>
                      <button class="connect-button">Connect</button>
                    </div>
                    
                    <div class="member-card connected">
                      <img src="https://via.placeholder.com/60x60/1a1a1a/666?text=SC" alt="Sarah Chen" class="member-avatar">
                      <div class="member-info">
                        <h4>Sarah Chen</h4>
                        <p>Partner • Chen & Associates</p>
                        <div class="member-tags">
                          <span class="tag">M&A Law</span>
                          <span class="tag">Wine</span>
                        </div>
                      </div>
                      <button class="message-button">Message</button>
                    </div>
                    
                    <div class="member-card">
                      <img src="https://via.placeholder.com/60x60/1a1a1a/666?text=MT" alt="Michael Thompson" class="member-avatar">
                      <div class="member-info">
                        <h4>Michael Thompson</h4>
                        <p>Founder • Thompson Tech</p>
                        <div class="member-tags">
                          <span class="tag">AI/ML</span>
                          <span class="tag">Sailing</span>
                        </div>
                      </div>
                      <button class="connect-button">Connect</button>
                    </div>
                  </div>
                  
                  <!-- Suggested Connections -->
                  <div class="section-header">
                    <h3>Suggested Connections</h3>
                  </div>
                  
                  <div class="suggestion-card" style="background-color: var(--bg-tertiary); border-radius: 12px; padding: 16px;">
                    <p class="suggestion-reason" style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Based on your interest in sustainable investing</p>
                    <div class="member-card" style="margin: 0; border: none; padding: 0;">
                      <img src="https://via.placeholder.com/60x60/1a1a1a/666?text=RW" alt="Rebecca Williams" class="member-avatar">
                      <div class="member-info">
                        <h4>Rebecca Williams</h4>
                        <p>Managing Director • Green Ventures</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- SOCIAL MESSAGES -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Messages</h1>
                  <div class="header-right">
                    <button class="icon-button">✏️</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Search Bar -->
                    <input type="search" placeholder="Search messages..." class="input-field" style="margin-bottom: 20px;">
                    
                    <!-- Message List -->
                    <div class="card" style="margin-bottom: 12px; cursor: pointer;">
                      <div style="display: flex; gap: 12px;">
                        <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--gold); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">JD</div>
                        <div style="flex: 1;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <h4 style="font-size: 16px; font-weight: 600;">John Davidson</h4>
                            <span style="font-size: 13px; color: var(--text-tertiary);">2:30 PM</span>
                          </div>
                          <p style="font-size: 14px; color: var(--text-secondary);">Looking forward to the wine tasting tomorrow!</p>
                        </div>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px; cursor: pointer;">
                      <div style="display: flex; gap: 12px;">
                        <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--info); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">SC</div>
                        <div style="flex: 1;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <h4 style="font-size: 16px; font-weight: 600;">Sarah Chen</h4>
                            <span style="font-size: 13px; color: var(--text-tertiary);">Yesterday</span>
                          </div>
                          <p style="font-size: 14px; color: var(--text-secondary);">Thanks for the recommendation on the spa...</p>
                        </div>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px; cursor: pointer;">
                      <div style="display: flex; gap: 12px;">
                        <div style="width: 48px; height: 48px; border-radius: 24px; background: var(--success); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">MW</div>
                        <div style="flex: 1;">
                          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                            <h4 style="font-size: 16px; font-weight: 600;">Michael Wong</h4>
                            <span style="font-size: 13px; color: var(--text-tertiary);">Dec 15</span>
                          </div>
                          <p style="font-size: 14px; color: var(--text-secondary);">Great meeting you at the networking event!</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- SOCIAL GROUPS -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Groups</h1>
                  <div class="header-right">
                    <button class="icon-button">➕</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- My Groups -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">My Groups</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Wine Enthusiasts</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">24 members • 3 new posts</p>
                        </div>
                        <span style="font-size: 24px;">🍷</span>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Golf Society</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">18 members • 1 new event</p>
                        </div>
                        <span style="font-size: 24px;">⛳</span>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 20px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Business Networking</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">42 members • 5 new posts</p>
                        </div>
                        <span style="font-size: 24px;">💼</span>
                      </div>
                    </div>
                    
                    <!-- Suggested Groups -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Suggested Groups</h3>
                    
                    <div class="card">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Art Collectors</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">15 members</p>
                        </div>
                        <span style="font-size: 24px;">🎨</span>
                      </div>
                      <button class="primary-button" style="width: 100%;">Join Group</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- SOCIAL FEED -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Social Feed</h1>
                  <div class="header-right">
                    <button class="icon-button">📷</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Feed Posts -->
                    <div class="card" style="margin-bottom: 16px;">
                      <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; border-radius: 20px; background: var(--gold); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; font-weight: 600;">RP</div>
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Robert Peterson</h4>
                          <p style="font-size: 12px; color: var(--text-tertiary);">2 hours ago</p>
                        </div>
                      </div>
                      <p style="margin-bottom: 12px;">Amazing wine tasting event last night! The 2018 Bordeaux selection was exceptional. 🍷</p>
                      <div style="height: 200px; background: var(--bg-tertiary); border-radius: 8px; margin-bottom: 12px;"></div>
                      <div style="display: flex; gap: 20px;">
                        <button style="all: unset; cursor: pointer; font-size: 14px;">❤️ 24</button>
                        <button style="all: unset; cursor: pointer; font-size: 14px;">💬 8</button>
                        <button style="all: unset; cursor: pointer; font-size: 14px;">↗️ Share</button>
                      </div>
                    </div>
                    
                    <div class="card">
                      <div style="display: flex; gap: 12px; margin-bottom: 12px;">
                        <div style="width: 40px; height: 40px; border-radius: 20px; background: var(--info); display: flex; align-items: center; justify-content: center; color: white; font-size: 14px; font-weight: 600;">EL</div>
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Emma Liu</h4>
                          <p style="font-size: 12px; color: var(--text-tertiary);">Yesterday</p>
                        </div>
                      </div>
                      <p style="margin-bottom: 12px;">Just completed the December Fitness Challenge! 💪 Thanks to everyone who joined me on this journey.</p>
                      <div style="display: flex; gap: 20px;">
                        <button style="all: unset; cursor: pointer; font-size: 14px;">❤️ 42</button>
                        <button style="all: unset; cursor: pointer; font-size: 14px;">💬 15</button>
                        <button style="all: unset; cursor: pointer; font-size: 14px;">↗️ Share</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Concierge Section -->
        <section id="concierge" class="flow-section">
          <h2>Concierge Services</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Services</button>
            <button class="tab-button">Active Requests</button>
            <button class="tab-button">Chat</button>
          </div>
          
          <div class="tab-container">
            <!-- Concierge Services -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>5:20 PM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Concierge</h1>
                  <div class="header-right">
                    <button class="icon-button">💬</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Concierge Welcome -->
                  <div class="concierge-welcome">
                    <img src="https://via.placeholder.com/80x80/1a1a1a/666?text=Concierge" alt="Concierge" class="concierge-avatar">
                    <div class="welcome-message">
                      <h3>Good evening, Mr. Humphreys</h3>
                      <p>How may I assist you today?</p>
                    </div>
                  </div>
                  
                  <!-- Quick Services -->
                  <div class="service-grid">
                    <button class="service-card">
                      <span class="service-icon">🎟️</span>
                      <span class="service-name">Event Tickets</span>
                    </button>
                    <button class="service-card">
                      <span class="service-icon">🍽️</span>
                      <span class="service-name">Restaurant Reservations</span>
                    </button>
                    <button class="service-card">
                      <span class="service-icon">✈️</span>
                      <span class="service-name">Travel Planning</span>
                    </button>
                    <button class="service-card">
                      <span class="service-icon">🚗</span>
                      <span class="service-name">Transportation</span>
                    </button>
                    <button class="service-card">
                      <span class="service-icon">🎁</span>
                      <span class="service-name">Gift Services</span>
                    </button>
                    <button class="service-card">
                      <span class="service-icon">🏠</span>
                      <span class="service-name">Home Services</span>
                    </button>
                  </div>
                  
                  <!-- Recent Requests -->
                  <div class="section-header">
                    <h3>Recent Requests</h3>
                    <button class="text-button">View All</button>
                  </div>
                  
                  <div class="request-list">
                    <div class="request-card completed">
                      <div class="request-icon">✅</div>
                      <div class="request-details">
                        <h4>Restaurant Booking - Vue de Monde</h4>
                        <p>Tomorrow, 7:30 PM • 4 guests</p>
                        <span class="status">Confirmed</span>
                      </div>
                    </div>
                    
                    <div class="request-card in-progress">
                      <div class="request-icon">⏳</div>
                      <div class="request-details">
                        <h4>AFL Grand Final Tickets</h4>
                        <p>Premium seats • 2 tickets</p>
                        <span class="status">Sourcing options</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Popular This Month -->
                  <div class="popular-services">
                    <h3>Popular This Month</h3>
                    <div class="service-highlight">
                      <img src="https://via.placeholder.com/100x60/1a1a1a/666?text=Melbourne+Cup" alt="Melbourne Cup">
                      <div class="highlight-details">
                        <h4>Melbourne Cup Packages</h4>
                        <p>Birdcage access, luxury transfers</p>
                        <button class="primary-button small">Inquire</button>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Start New Request -->
                  <button class="primary-button large floating">
                    <span>Start New Request</span>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- CONCIERGE ACTIVE REQUESTS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Active Requests</h1>
                    <div class="header-right">
                      <button class="icon-button">➕</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Active Requests -->
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Restaurant Reservation</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">Nobu - Tomorrow, 8:00 PM</p>
                          </div>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">In Progress</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Requested 2 hours ago</p>
                        <button class="secondary-button" style="width: 100%;">View Details</button>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Airport Transfer</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">LAX - Dec 24, 6:00 AM</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Confirmed</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Mercedes S-Class arranged</p>
                        <button class="secondary-button" style="width: 100%;">View Details</button>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Theater Tickets</h3>
                            <p style="font-size: 14px; color: var(--text-secondary);">Hamilton - Jan 15, 7:30 PM</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Completed</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Orchestra seats, Row C</p>
                        <button class="primary-button" style="width: 100%;">Download Tickets</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- CONCIERGE CHAT -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Concierge Chat</h1>
                    <div class="header-right">
                      <button class="icon-button">📞</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="display: flex; flex-direction: column; height: 100%;">
                      <!-- Chat Messages -->
                      <div style="flex: 1; padding: 20px; overflow-y: auto;">
                        <div style="margin-bottom: 16px;">
                          <div style="background: var(--bg-tertiary); padding: 12px 16px; border-radius: 16px 16px 16px 4px; max-width: 80%; margin-bottom: 4px;">
                            <p style="font-size: 14px;">Good morning! How may I assist you today?</p>
                          </div>
                          <p style="font-size: 12px; color: var(--text-tertiary);">Sarah - Concierge • 9:30 AM</p>
                        </div>
                        
                        <div style="margin-bottom: 16px; text-align: right;">
                          <div style="background: var(--gold); color: white; padding: 12px 16px; border-radius: 16px 16px 4px 16px; max-width: 80%; margin-left: auto; margin-bottom: 4px; display: inline-block; text-align: left;">
                            <p style="font-size: 14px;">Hi Sarah! I need help booking a restaurant for tomorrow night. Something special for an anniversary.</p>
                          </div>
                          <p style="font-size: 12px; color: var(--text-tertiary);">9:32 AM</p>
                        </div>
                        
                        <div style="margin-bottom: 16px;">
                          <div style="background: var(--bg-tertiary); padding: 12px 16px; border-radius: 16px 16px 16px 4px; max-width: 80%; margin-bottom: 4px;">
                            <p style="font-size: 14px;">I'd be happy to help! For anniversaries, I recommend either Le Bernardin or The French Laundry. Both have availability tomorrow. Would you prefer modern French or seafood?</p>
                          </div>
                          <p style="font-size: 12px; color: var(--text-tertiary);">Sarah - Concierge • 9:33 AM</p>
                        </div>
                      </div>
                      
                      <!-- Message Input -->
                      <div style="padding: 16px; border-top: 1px solid var(--border-color);">
                        <div style="display: flex; gap: 12px;">
                          <input type="text" placeholder="Type a message..." class="input-field" style="flex: 1; margin: 0;">
                          <button class="primary-button" style="padding: 0 20px;">Send</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Payment Section -->
        <section id="payment" class="flow-section">
          <h2>Payment & Billing</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Overview</button>
            <button class="tab-button">Transactions</button>
            <button class="tab-button">Payment Methods</button>
            <button class="tab-button">Statements</button>
          </div>
          
          <div class="tab-container">
            <!-- Payment Overview -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>4:15 PM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Billing</h1>
                  <div class="header-right">
                    <button class="icon-button">📥</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Account Balance -->
                  <div class="balance-card">
                    <div class="balance-header">
                      <h3>Current Balance</h3>
                      <span class="period">June 2025</span>
                    </div>
                    <div class="balance-amount">
                      <span class="currency">$</span>
                      <span class="amount">2,847</span>
                      <span class="cents">.50</span>
                    </div>
                    <div class="balance-actions">
                      <button class="primary-button">Pay Now</button>
                      <button class="secondary-button">View Details</button>
                    </div>
                  </div>
                  
                  <!-- Spending Breakdown -->
                  <div class="spending-chart">
                    <h3>This Month's Activity</h3>
                    <div class="chart-container">
                      <div class="bar-chart">
                        <div class="bar" style="height: 60%">
                          <span class="value">$1,250</span>
                          <span class="label">F&B</span>
                        </div>
                        <div class="bar" style="height: 40%">
                          <span class="value">$850</span>
                          <span class="label">Events</span>
                        </div>
                        <div class="bar" style="height: 25%">
                          <span class="value">$450</span>
                          <span class="label">Facilities</span>
                        </div>
                        <div class="bar" style="height: 15%">
                          <span class="value">$297.50</span>
                          <span class="label">Other</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Recent Transactions -->
                  <div class="section-header">
                    <h3>Recent Transactions</h3>
                    <button class="text-button">See All</button>
                  </div>
                  
                  <div class="transaction-list">
                    <div class="transaction-item">
                      <div class="transaction-icon">🍽️</div>
                      <div class="transaction-details">
                        <h4>Level 18 Lounge</h4>
                        <p>Today, 12:35 PM</p>
                      </div>
                      <div class="transaction-amount">-$197.23</div>
                    </div>
                    
                    <div class="transaction-item">
                      <div class="transaction-icon">🎟️</div>
                      <div class="transaction-details">
                        <h4>Melbourne Symphony Orchestra</h4>
                        <p>Jun 5, 2025</p>
                      </div>
                      <div class="transaction-amount">-$450.00</div>
                    </div>
                    
                    <div class="transaction-item">
                      <div class="transaction-icon">💳</div>
                      <div class="transaction-details">
                        <h4>Payment Received</h4>
                        <p>Jun 1, 2025</p>
                      </div>
                      <div class="transaction-amount credit">+$3,000.00</div>
                    </div>
                  </div>
                  
                  <!-- Payment Methods -->
                  <div class="payment-methods-summary">
                    <h3>Payment Methods</h3>
                    <div class="method-card">
                      <span class="card-icon">💳</span>
                      <div class="method-details">
                        <span>•••• 4242</span>
                        <span class="type">Default</span>
                      </div>
                      <button class="text-button">Manage</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- PAYMENT TRANSACTIONS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Transactions</h1>
                    <div class="header-right">
                      <button class="icon-button">🔍</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Filter Options -->
                      <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button class="secondary-button" style="flex: 1;">All</button>
                        <button class="secondary-button" style="flex: 1;">F&B</button>
                        <button class="secondary-button" style="flex: 1;">Events</button>
                        <button class="secondary-button" style="flex: 1;">Wellness</button>
                      </div>
                      
                      <!-- Transaction List -->
                      <div style="margin-bottom: 24px;">
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--text-secondary); margin-bottom: 12px;">TODAY</h3>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600;">Restaurant - Lunch</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">12:45 PM</p>
                            </div>
                            <span style="font-size: 16px; font-weight: 600;">$127.50</span>
                          </div>
                        </div>
                        
                        <div class="card">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600;">Personal Training</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">7:00 AM</p>
                            </div>
                            <span style="font-size: 16px; font-weight: 600;">$120.00</span>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--text-secondary); margin-bottom: 12px;">YESTERDAY</h3>
                        
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600;">Wine Tasting Event</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">7:00 PM</p>
                            </div>
                            <span style="font-size: 16px; font-weight: 600;">$85.00</span>
                          </div>
                        </div>
                        
                        <div class="card">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600;">Spa Treatment</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">2:00 PM</p>
                            </div>
                            <span style="font-size: 16px; font-weight: 600;">$180.00</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- PAYMENT METHODS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Payment Methods</h1>
                    <div class="header-right">
                      <button class="icon-button">➕</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Primary Card -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Primary Method</h3>
                      
                      <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, #1a1a1a, #333); color: white;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 20px;">
                          <span style="font-size: 24px;">💳</span>
                          <span style="font-size: 20px; font-weight: 600;">VISA</span>
                        </div>
                        <div style="margin-bottom: 16px;">
                          <p style="font-size: 18px; letter-spacing: 2px;">•••• •••• •••• 4242</p>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                          <div>
                            <p style="font-size: 12px; opacity: 0.7;">Card Holder</p>
                            <p style="font-size: 14px;">DAN HUMPHREYS</p>
                          </div>
                          <div style="text-align: right;">
                            <p style="font-size: 12px; opacity: 0.7;">Expires</p>
                            <p style="font-size: 14px;">12/27</p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Other Methods -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Other Methods</h3>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; gap: 12px; align-items: center;">
                            <span style="font-size: 24px;">💳</span>
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600;">Mastercard ••• 8765</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Expires 08/26</p>
                            </div>
                          </div>
                          <button class="secondary-button">Set Primary</button>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; gap: 12px; align-items: center;">
                            <span style="font-size: 24px;">💳</span>
                            <div>
                              <h4 style="font-size: 16px; font-weight: 600;">Amex ••• 1234</h4>
                              <p style="font-size: 14px; color: var(--text-secondary);">Expires 03/25</p>
                            </div>
                          </div>
                          <button class="secondary-button">Set Primary</button>
                        </div>
                      </div>
                      
                      <button class="primary-button large" style="margin-top: 20px;">Add New Card</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- PAYMENT STATEMENTS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Statements</h1>
                    <div class="header-right">
                      <button class="icon-button">📥</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Statement List -->
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">December 2024</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Statement Period: Dec 1-31</p>
                            <p style="font-size: 16px; font-weight: 600; color: var(--gold); margin-top: 8px;">$3,847.50</p>
                          </div>
                          <button class="icon-button">📄</button>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">November 2024</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Statement Period: Nov 1-30</p>
                            <p style="font-size: 16px; font-weight: 600; color: var(--gold); margin-top: 8px;">$4,256.00</p>
                          </div>
                          <button class="icon-button">📄</button>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">October 2024</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Statement Period: Oct 1-31</p>
                            <p style="font-size: 16px; font-weight: 600; color: var(--gold); margin-top: 8px;">$3,192.75</p>
                          </div>
                          <button class="icon-button">📄</button>
                        </div>
                      </div>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">September 2024</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Statement Period: Sep 1-30</p>
                            <p style="font-size: 16px; font-weight: 600; color: var(--gold); margin-top: 8px;">$2,984.25</p>
                          </div>
                          <button class="icon-button">📄</button>
                        </div>
                      </div>
                      
                      <!-- Year Summary -->
                      <div class="card" style="margin-top: 20px; background: var(--bg-secondary);">
                        <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 12px;">2024 Year to Date</h3>
                        <p style="font-size: 24px; font-weight: 700; color: var(--gold);">$42,847.50</p>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-top: 4px;">Average Monthly: $3,570.63</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="flow-section">
          <h2>Settings & Preferences</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Profile</button>
            <button class="tab-button">Notifications</button>
            <button class="tab-button">Privacy</button>
            <button class="tab-button">Security</button>
          </div>
          
          <div class="tab-container">
            <!-- Profile Settings -->
            <div class="tab-content active">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>7:30 PM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Settings</h1>
                  <div class="header-right">
                    <button class="text-button">Save</button>
                  </div>
                </div>
                
                <div class="content">
                  <!-- Profile Photo -->
                  <div class="profile-photo-section">
                    <div class="profile-photo-large">
                      <img src="https://via.placeholder.com/120x120/1a1a1a/666?text=DH" alt="Profile">
                      <button class="photo-edit-button">📷</button>
                    </div>
                  </div>
                  
                  <!-- Personal Information -->
                  <div class="settings-section">
                    <h3>Personal Information</h3>
                    <div class="settings-item">
                      <label>Full Name</label>
                      <input type="text" value="Dan Humphreys" class="settings-input">
                    </div>
                    <div class="settings-item">
                      <label>Email</label>
                      <input type="email" value="<EMAIL>" class="settings-input">
                    </div>
                    <div class="settings-item">
                      <label>Mobile</label>
                      <input type="tel" value="+61 ***********" class="settings-input">
                    </div>
                  </div>
                  
                  <!-- Professional Information -->
                  <div class="settings-section">
                    <h3>Professional Information</h3>
                    <div class="settings-item">
                      <label>Company</label>
                      <input type="text" value="ALIAS" class="settings-input">
                    </div>
                    <div class="settings-item">
                      <label>Position</label>
                      <input type="text" value="Founder & CEO" class="settings-input">
                    </div>
                    <div class="settings-item">
                      <label>Industry</label>
                      <select class="settings-select">
                        <option selected>Technology</option>
                        <option>Finance</option>
                        <option>Real Estate</option>
                      </select>
                    </div>
                  </div>
                  
                  <!-- Interests -->
                  <div class="settings-section">
                    <h3>Interests & Preferences</h3>
                    <div class="interest-tags">
                      <button class="tag-pill active">Golf</button>
                      <button class="tag-pill active">Wine</button>
                      <button class="tag-pill active">Technology</button>
                      <button class="tag-pill">Tennis</button>
                      <button class="tag-pill">Art</button>
                      <button class="tag-pill">Sailing</button>
                      <button class="add-tag">+ Add</button>
                    </div>
                  </div>
                  
                  <!-- Communication Preferences -->
                  <div class="settings-section">
                    <h3>Communication Preferences</h3>
                    <div class="toggle-setting">
                      <div class="toggle-info">
                        <h4>Event Invitations</h4>
                        <p>Receive exclusive event invites</p>
                      </div>
                      <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                      </label>
                    </div>
                    <div class="toggle-setting">
                      <div class="toggle-info">
                        <h4>Member Updates</h4>
                        <p>New member introductions</p>
                      </div>
                      <label class="toggle-switch">
                        <input type="checkbox" checked>
                        <span class="toggle-slider"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- SETTINGS NOTIFICATIONS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Notifications</h1>
                    <div class="header-right">
                      <button class="text-button">Save</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Push Notifications -->
                      <div class="settings-section">
                        <h3>Push Notifications</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Enable Notifications</h4>
                            <p>Receive push notifications</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                      
                      <!-- Notification Types -->
                      <div class="settings-section">
                        <h3>Notification Types</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Event Reminders</h4>
                            <p>Upcoming events and bookings</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Messages</h4>
                            <p>New messages from members</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Concierge Updates</h4>
                            <p>Request status and updates</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Special Offers</h4>
                            <p>Exclusive member offers</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                      
                      <!-- Email Preferences -->
                      <div class="settings-section">
                        <h3>Email Preferences</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Weekly Newsletter</h4>
                            <p>Club updates and highlights</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Monthly Statement</h4>
                            <p>Account summary and billing</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- SETTINGS PRIVACY -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Privacy</h1>
                    <div class="header-right">
                      <button class="text-button">Save</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Profile Visibility -->
                      <div class="settings-section">
                        <h3>Profile Visibility</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Show in Member Directory</h4>
                            <p>Other members can find you</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Show Professional Info</h4>
                            <p>Company and position visible</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Show Contact Details</h4>
                            <p>Email and phone visible to members</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                      
                      <!-- Activity Privacy -->
                      <div class="settings-section">
                        <h3>Activity Privacy</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Show Event Attendance</h4>
                            <p>Others can see events you attend</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Show in Social Feed</h4>
                            <p>Your posts visible to members</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                      
                      <!-- Data Management -->
                      <div class="settings-section">
                        <h3>Data Management</h3>
                        <button class="secondary-button" style="width: 100%; margin-bottom: 12px;">Download My Data</button>
                        <button class="secondary-button" style="width: 100%; color: var(--error);">Delete Account</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- SETTINGS SECURITY -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Security</h1>
                    <div class="header-right">
                      <button class="icon-button">🔒</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Authentication -->
                      <div class="settings-section">
                        <h3>Authentication</h3>
                        <div class="settings-item">
                          <label>Current Password</label>
                          <input type="password" placeholder="Enter current password" class="settings-input">
                        </div>
                        <div class="settings-item">
                          <label>New Password</label>
                          <input type="password" placeholder="Enter new password" class="settings-input">
                        </div>
                        <div class="settings-item">
                          <label>Confirm Password</label>
                          <input type="password" placeholder="Confirm new password" class="settings-input">
                        </div>
                        <button class="primary-button" style="width: 100%; margin-top: 16px;">Update Password</button>
                      </div>
                      
                      <!-- Two-Factor Authentication -->
                      <div class="settings-section">
                        <h3>Two-Factor Authentication</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Enable 2FA</h4>
                            <p>Add extra security to your account</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <button class="secondary-button" style="width: 100%; margin-top: 12px;">Setup 2FA</button>
                      </div>
                      
                      <!-- Biometric Authentication -->
                      <div class="settings-section">
                        <h3>Biometric Authentication</h3>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Face ID</h4>
                            <p>Use Face ID to login</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                        <div class="toggle-setting">
                          <div class="toggle-info">
                            <h4>Touch ID</h4>
                            <p>Use fingerprint to login</p>
                          </div>
                          <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                          </label>
                        </div>
                      </div>
                      
                      <!-- Active Sessions -->
                      <div class="settings-section">
                        <h3>Active Sessions</h3>
                        <div class="card" style="margin-bottom: 12px;">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">iPhone 15 Pro</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Current device</p>
                            </div>
                            <span style="font-size: 12px; color: var(--success);">Active now</span>
                          </div>
                        </div>
                        <div class="card">
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                              <h4 style="font-size: 14px; font-weight: 600;">iPad Pro</h4>
                              <p style="font-size: 13px; color: var(--text-secondary);">Last active 2 days ago</p>
                            </div>
                            <button class="text-button" style="color: var(--error);">Sign Out</button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Staff Section -->
        <section id="staff" class="flow-section">
          <h2>Staff-Facing Screens</h2>
          
          <!-- STAFF CHECK-IN -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">☰</button>
                  </div>
                  <h1>Staff Portal</h1>
                  <div class="header-right">
                    <button class="icon-button">🔔</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Quick Stats -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 24px;">
                      <div class="card" style="text-align: center; padding: 20px;">
                        <p style="font-size: 32px; font-weight: 700; color: var(--gold);">47</p>
                        <p style="font-size: 14px; color: var(--text-secondary);">Members Present</p>
                      </div>
                      <div class="card" style="text-align: center; padding: 20px;">
                        <p style="font-size: 32px; font-weight: 700; color: var(--info);">12</p>
                        <p style="font-size: 14px; color: var(--text-secondary);">Guests Today</p>
                      </div>
                    </div>
                    
                    <!-- Member Check-in -->
                    <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">Member Check-in</h3>
                    <div class="card" style="margin-bottom: 16px;">
                      <input type="search" placeholder="Search member name or ID..." class="input-field" style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px; margin-bottom: 12px;">
                      
                      <button class="primary-button" style="width: 100%; margin-bottom: 8px;">
                        <span style="font-size: 20px; margin-right: 8px;">📷</span>
                        Scan Member QR
                      </button>
                      <button class="secondary-button" style="width: 100%;">Manual Check-in</button>
                    </div>
                    
                    <!-- Recent Activity -->
                    <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">Recent Activity</h3>
                    <div class="card">
                      <div style="padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <p style="font-weight: 600;">Dan Humphreys</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Member #0142</p>
                          </div>
                          <span style="font-size: 12px; color: var(--success);">✓ 9:35 AM</span>
                        </div>
                      </div>
                      <div style="padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <p style="font-weight: 600;">Sarah Chen + 2 guests</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Member #0087</p>
                          </div>
                          <span style="font-size: 12px; color: var(--success);">✓ 9:28 AM</span>
                        </div>
                      </div>
                      <div style="padding: 12px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <p style="font-weight: 600;">James Davidson</p>
                            <p style="font-size: 13px; color: var(--text-secondary);">Member #0023</p>
                          </div>
                          <span style="font-size: 12px; color: var(--error);">✗ 9:15 AM</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- STAFF GUEST MANAGEMENT -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Guest Management</h1>
                  <div class="header-right">
                    <button class="icon-button">➕</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Pending Approvals -->
                    <div class="card" style="margin-bottom: 20px; background: var(--gold); color: white;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h3 style="font-size: 18px; font-weight: 600;">Pending Approvals</h3>
                          <p style="font-size: 14px; opacity: 0.9;">3 guests awaiting check-in</p>
                        </div>
                        <span style="font-size: 32px;">⏳</span>
                      </div>
                    </div>
                    
                    <!-- Guest List -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Today's Guests</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">John Smith</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Guest of Dan Humphreys</p>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Expected: 10:00 AM</p>
                        </div>
                        <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Pending</span>
                      </div>
                      <div style="display: flex; gap: 8px;">
                        <button class="primary-button" style="flex: 1;">Check In</button>
                        <button class="secondary-button" style="flex: 1;">Contact Host</button>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Emma Wilson</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Guest of Sarah Chen</p>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Checked in: 9:28 AM</p>
                        </div>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Checked In</span>
                      </div>
                      <button class="secondary-button" style="width: 100%;">View Details</button>
                    </div>
                    
                    <div class="card">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Robert Lee</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Guest of Michael Thompson</p>
                          <p style="font-size: 13px; color: var(--text-tertiary);">Expected: 2:00 PM</p>
                        </div>
                        <span style="background: var(--info); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Pre-approved</span>
                      </div>
                      <button class="secondary-button" style="width: 100%;">View Details</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- STAFF FACILITY STATUS -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Facility Status</h1>
                  <div class="header-right">
                    <button class="icon-button">🔄</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Facility Overview -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 24px;">
                      <div class="card" style="padding: 16px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🍽️</div>
                        <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 4px;">Main Restaurant</h4>
                        <p style="font-size: 20px; font-weight: 700; color: var(--gold);">75%</p>
                        <p style="font-size: 12px; color: var(--text-secondary);">18/24 tables</p>
                      </div>
                      
                      <div class="card" style="padding: 16px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🏋️</div>
                        <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 4px;">Fitness Center</h4>
                        <p style="font-size: 20px; font-weight: 700; color: var(--success);">45%</p>
                        <p style="font-size: 12px; color: var(--text-secondary);">Normal capacity</p>
                      </div>
                      
                      <div class="card" style="padding: 16px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🏊</div>
                        <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 4px;">Pool</h4>
                        <p style="font-size: 20px; font-weight: 700; color: var(--info);">60%</p>
                        <p style="font-size: 12px; color: var(--text-secondary);">12/20 capacity</p>
                      </div>
                      
                      <div class="card" style="padding: 16px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">💼</div>
                        <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 4px;">Meeting Rooms</h4>
                        <p style="font-size: 20px; font-weight: 700; color: var(--error);">90%</p>
                        <p style="font-size: 12px; color: var(--text-secondary);">9/10 occupied</p>
                      </div>
                    </div>
                    
                    <!-- Status Updates -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Updates</h3>
                    
                    <div class="card">
                      <div style="padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                          <span style="font-weight: 600;">Boardroom A</span>
                          <span style="font-size: 12px; color: var(--text-tertiary);">10:45 AM</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary);">Now available - cleaned and ready</p>
                      </div>
                      
                      <div style="padding: 12px 0; border-bottom: 1px solid var(--border-color);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                          <span style="font-weight: 600;">Spa Treatment Room 3</span>
                          <span style="font-size: 12px; color: var(--text-tertiary);">10:30 AM</span>
                        </div>
                        <p style="font-size: 14px; color: var(--error);">Maintenance required - temporarily closed</p>
                      </div>
                      
                      <div style="padding: 12px 0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                          <span style="font-weight: 600;">Private Dining Room</span>
                          <span style="font-size: 12px; color: var(--text-tertiary);">10:15 AM</span>
                        </div>
                        <p style="font-size: 14px; color: var(--text-secondary);">Reserved for lunch event (12:00 PM)</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- STAFF ORDER MANAGEMENT -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Order Management</h1>
                  <div class="header-right">
                    <button class="icon-button">🔔</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Active Orders Summary -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; margin-bottom: 20px;">
                      <div class="card" style="padding: 12px; text-align: center; background: var(--gold); color: white;">
                        <p style="font-size: 24px; font-weight: 700;">5</p>
                        <p style="font-size: 12px;">New</p>
                      </div>
                      <div class="card" style="padding: 12px; text-align: center;">
                        <p style="font-size: 24px; font-weight: 700;">8</p>
                        <p style="font-size: 12px;">In Progress</p>
                      </div>
                      <div class="card" style="padding: 12px; text-align: center;">
                        <p style="font-size: 24px; font-weight: 700;">3</p>
                        <p style="font-size: 12px;">Ready</p>
                      </div>
                    </div>
                    
                    <!-- Filter Tabs -->
                    <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                      <button class="secondary-button" style="flex: 1;">All</button>
                      <button class="secondary-button" style="flex: 1;">Restaurant</button>
                      <button class="secondary-button" style="flex: 1;">Room Service</button>
                    </div>
                    
                    <!-- Order List -->
                    <div class="card" style="margin-bottom: 12px; border-left: 4px solid var(--gold);">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Order #2851</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Table 12 • Dan Humphreys</p>
                        </div>
                        <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">New</span>
                      </div>
                      <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 8px;">2x Wagyu Burger, 1x Caesar Salad</p>
                      <div style="display: flex; gap: 8px;">
                        <button class="primary-button" style="flex: 1;">Accept</button>
                        <button class="secondary-button" style="flex: 1;">View Details</button>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px; border-left: 4px solid var(--info);">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Order #2850</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Room 1802 • Sarah Chen</p>
                        </div>
                        <span style="background: var(--info); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Preparing</span>
                      </div>
                      <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 8px;">Continental Breakfast, Fresh Juice</p>
                      <p style="font-size: 12px; color: var(--text-tertiary);">Est. ready: 10 mins</p>
                    </div>
                    
                    <div class="card" style="border-left: 4px solid var(--success);">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Order #2849</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Lounge • Michael Thompson</p>
                        </div>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Ready</span>
                      </div>
                      <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 8px;">Coffee, Pastries</p>
                      <button class="primary-button" style="width: 100%;">Mark as Delivered</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Wine Club Section -->
        <section id="wine" class="flow-section">
          <h2>Wine Club</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Collection</button>
            <button class="tab-button">My Cellar</button>
            <button class="tab-button">Tastings</button>
            <button class="tab-button">Sommelier</button>
            <button class="tab-button">Orders</button>
          </div>
          
          <div class="tab-container">
          
            <!-- WINE COLLECTION -->
            <div class="tab-content active">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Wine Collection</h1>
                  <div class="header-right">
                    <button class="icon-button">🔍</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Member's Wine Locker -->
                    <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, #722F37, #8B4345); color: white;">
                      <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 12px;">Your Wine Locker</h3>
                      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px; text-align: center;">
                        <div>
                          <p style="font-size: 24px; font-weight: 700;">24</p>
                          <p style="font-size: 12px; opacity: 0.9;">Bottles</p>
                        </div>
                        <div>
                          <p style="font-size: 24px; font-weight: 700;">$12,400</p>
                          <p style="font-size: 12px; opacity: 0.9;">Value</p>
                        </div>
                        <div>
                          <p style="font-size: 24px; font-weight: 700;">8</p>
                          <p style="font-size: 12px; opacity: 0.9;">Regions</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Featured Wines -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Featured Selections</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; gap: 16px;">
                        <div style="width: 60px; height: 80px; background: var(--bg-tertiary); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                          <span style="font-size: 24px;">🍷</span>
                        </div>
                        <div style="flex: 1;">
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Dom Pérignon 2012</h4>
                          <p style="font-size: 13px; color: var(--text-secondary); margin-bottom: 8px;">Champagne • France</p>
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$320</span>
                            <button class="secondary-button">Reserve</button>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; gap: 16px;">
                        <div style="width: 60px; height: 80px; background: var(--bg-tertiary); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                          <span style="font-size: 24px;">🍷</span>
                        </div>
                        <div style="flex: 1;">
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Opus One 2018</h4>
                          <p style="font-size: 13px; color: var(--text-secondary); margin-bottom: 8px;">Cabernet Blend • Napa Valley</p>
                          <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$450</span>
                            <button class="primary-button">Order</button>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Wine Events -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Upcoming Tastings</h3>
                    
                    <div class="card">
                      <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">Burgundy Masterclass</h4>
                      <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Friday, March 22 • 7:00 PM</p>
                      <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Join our sommelier for an exclusive tasting</p>
                      <button class="primary-button" style="width: 100%;">Reserve Spot - $85</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
            <!-- WINE CELLAR -->
            <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>My Wine Cellar</h1>
                  <div class="header-right">
                    <button class="icon-button">📋</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Storage Overview -->
                    <div class="card" style="margin-bottom: 20px;">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="font-size: 16px; font-weight: 600;">Locker #247</h3>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Optimal</span>
                      </div>
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px;">
                        <div>
                          <p style="font-size: 12px; color: var(--text-secondary);">Temperature</p>
                          <p style="font-size: 18px; font-weight: 600;">14°C</p>
                        </div>
                        <div>
                          <p style="font-size: 12px; color: var(--text-secondary);">Humidity</p>
                          <p style="font-size: 18px; font-weight: 600;">65%</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Current Inventory -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Current Inventory</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Barolo Brunate 2016</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">Giuseppe Rinaldi • Piedmont</p>
                        </div>
                        <div style="text-align: right;">
                          <p style="font-size: 14px; font-weight: 600;">2 bottles</p>
                          <p style="font-size: 12px; color: var(--text-tertiary);">Slot A3-A4</p>
                        </div>
                      </div>
                      <div style="display: flex; gap: 8px;">
                        <button class="secondary-button" style="flex: 1;">View Details</button>
                        <button class="primary-button" style="flex: 1;">Request Bottle</button>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Cristal 2008</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">Louis Roederer • Champagne</p>
                        </div>
                        <div style="text-align: right;">
                          <p style="font-size: 14px; font-weight: 600;">1 bottle</p>
                          <p style="font-size: 12px; color: var(--text-tertiary);">Slot C1</p>
                        </div>
                      </div>
                      <div style="display: flex; gap: 8px;">
                        <button class="secondary-button" style="flex: 1;">View Details</button>
                        <button class="primary-button" style="flex: 1;">Request Bottle</button>
                      </div>
                    </div>
                    
                    <!-- Add Wine -->
                    <button class="secondary-button" style="width: 100%; margin-top: 16px; border: 2px dashed var(--border-color);">
                      + Add New Wine to Collection
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- WINE TASTINGS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Wine Tastings</h1>
                    <div class="header-right">
                      <button class="icon-button">📅</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Upcoming Tastings -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Upcoming Events</h3>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Burgundy Masterclass</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Friday, March 22 • 7:00 PM</p>
                          </div>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Available</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Explore the terroir of Côte d'Or with our head sommelier</p>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$85 per person</span>
                          <span style="font-size: 12px; color: var(--text-secondary);">6 spots left</span>
                        </div>
                        <button class="primary-button" style="width: 100%;">Reserve Spot</button>
                      </div>
                      
                      <div class="card" style="margin-bottom: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Champagne & Oysters</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">Saturday, March 30 • 6:30 PM</p>
                          </div>
                          <span style="background: var(--info); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Reserved</span>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Premium champagne paired with fresh Coffin Bay oysters</p>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$125 per person</span>
                          <span style="font-size: 12px; color: var(--success);">Confirmed</span>
                        </div>
                        <button class="secondary-button" style="width: 100%;">Manage Booking</button>
                      </div>
                      
                      <!-- Private Tastings -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Private Tastings</h3>
                      
                      <div class="card" style="margin-bottom: 16px; background: var(--bg-tertiary);">
                        <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 8px;">Book Private Session</h4>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 12px;">Personalized wine tasting with our sommelier team</p>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                          <div>
                            <p style="font-size: 12px; color: var(--text-secondary);">Duration</p>
                            <p style="font-size: 14px; font-weight: 600;">90 minutes</p>
                          </div>
                          <div>
                            <p style="font-size: 12px; color: var(--text-secondary);">Group Size</p>
                            <p style="font-size: 14px; font-weight: 600;">2-8 people</p>
                          </div>
                        </div>
                        <button class="primary-button" style="width: 100%; margin-top: 12px;">Book Private Tasting</button>
                      </div>
                      
                      <!-- Tasting History -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Your Tasting History</h3>
                      
                      <div class="card">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Barolo Vertical Tasting</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">February 18, 2024</p>
                          </div>
                          <span style="font-size: 16px;">⭐⭐⭐⭐⭐</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary);">Exceptional evening exploring 2016-2019 vintages</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- SOMMELIER CHAT -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Sommelier Chat</h1>
                    <div class="header-right">
                      <button class="icon-button">📞</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Sommelier Profile -->
                      <div class="card" style="margin-bottom: 20px; text-align: center;">
                        <div style="width: 80px; height: 80px; border-radius: 40px; background: var(--gold); margin: 0 auto 16px; display: flex; align-items: center; justify-content: center;">
                          <span style="color: white; font-size: 32px; font-weight: 600;">JM</span>
                        </div>
                        <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 4px;">Jean-Marc Dubois</h3>
                        <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Head Sommelier</p>
                        <div style="background: var(--success); color: white; padding: 4px 12px; border-radius: 12px; display: inline-block; font-size: 12px;">
                          <span>• Online</span>
                        </div>
                      </div>
                      
                      <!-- Quick Recommendations -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Quick Recommendations</h3>
                      
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                        <button class="secondary-button" style="padding: 16px; text-align: center; height: auto;">
                          <div style="font-size: 24px; margin-bottom: 8px;">🍽️</div>
                          <span style="font-size: 12px;">Dinner Pairing</span>
                        </button>
                        
                        <button class="secondary-button" style="padding: 16px; text-align: center; height: auto;">
                          <div style="font-size: 24px; margin-bottom: 8px;">🎁</div>
                          <span style="font-size: 12px;">Gift Selection</span>
                        </button>
                        
                        <button class="secondary-button" style="padding: 16px; text-align: center; height: auto;">
                          <div style="font-size: 24px; margin-bottom: 8px;">🎆</div>
                          <span style="font-size: 12px;">Special Occasion</span>
                        </button>
                        
                        <button class="secondary-button" style="padding: 16px; text-align: center; height: auto;">
                          <div style="font-size: 24px; margin-bottom: 8px;">💰</div>
                          <span style="font-size: 12px;">Budget Options</span>
                        </button>
                      </div>
                      
                      <!-- Chat Messages -->
                      <div style="background: var(--bg-tertiary); border-radius: 12px; padding: 16px; margin-bottom: 20px; height: 200px; overflow-y: auto;">
                        <div style="margin-bottom: 16px;">
                          <div style="background: var(--bg-primary); border-radius: 12px; padding: 12px; margin-bottom: 8px; margin-left: 20px;">
                            <p style="font-size: 14px;">Good afternoon, Dan! How can I assist with your wine selection today?</p>
                          </div>
                          <p style="font-size: 11px; color: var(--text-tertiary); text-align: right;">Jean-Marc • 2:15 PM</p>
                        </div>
                        
                        <div style="margin-bottom: 16px;">
                          <div style="background: var(--gold); color: white; border-radius: 12px; padding: 12px; margin-bottom: 8px; margin-right: 20px;">
                            <p style="font-size: 14px;">I'm hosting a dinner party next week for 6 people. Looking for something special under $200.</p>
                          </div>
                          <p style="font-size: 11px; color: var(--text-tertiary);">You • 2:16 PM</p>
                        </div>
                        
                        <div style="margin-bottom: 16px;">
                          <div style="background: var(--bg-primary); border-radius: 12px; padding: 12px; margin-bottom: 8px; margin-left: 20px;">
                            <p style="font-size: 14px;">Excellent! What's the main course? I have some wonderful recommendations from our new Australian collection.</p>
                          </div>
                          <p style="font-size: 11px; color: var(--text-tertiary); text-align: right;">Jean-Marc • 2:18 PM</p>
                        </div>
                      </div>
                      
                      <!-- Message Input -->
                      <div style="display: flex; gap: 8px;">
                        <input type="text" placeholder="Type your message..." class="input-field" style="flex: 1; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px;">
                        <button class="primary-button">💬</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- WINE ORDERS -->
            <div class="tab-content">
              <div class="flow-row">
                <div class="mobile-screen">
                  <div class="status-bar">
                    <span>9:41 AM</span>
                    <div class="status-icons">
                      <span>📶</span>
                      <span>📶</span>
                      <span>🔋</span>
                    </div>
                  </div>
                  
                  <div class="app-header">
                    <div class="header-left">
                      <button class="icon-button">←</button>
                    </div>
                    <h1>Wine Orders</h1>
                    <div class="header-right">
                      <button class="icon-button">🛍️</button>
                    </div>
                  </div>
                  
                  <div class="content">
                    <div style="padding: 20px;">
                      <!-- Order Status -->
                      <div class="card" style="margin-bottom: 20px; background: var(--success); color: white;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                          <h3 style="font-size: 16px; font-weight: 600;">Current Order</h3>
                          <span style="background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 4px; font-size: 12px;">Processing</span>
                        </div>
                        <p style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">Order #WO-2024-0087</p>
                        <p style="font-size: 12px; opacity: 0.8;">Expected delivery: March 25, 2024</p>
                      </div>
                      
                      <!-- Recent Orders -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Orders</h3>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Order #WO-2024-0087</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">March 20, 2024 • 3 bottles</p>
                          </div>
                          <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Processing</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 8px;">Dom Pérignon 2012, Opus One 2018, Barolo 2016</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <span style="font-size: 16px; font-weight: 600; color: var(--gold);">$1,245</span>
                          <button class="secondary-button">Track Order</button>
                        </div>
                      </div>
                      
                      <div class="card" style="margin-bottom: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Order #WO-2024-0073</h4>
                            <p style="font-size: 13px; color: var(--text-secondary);">March 15, 2024 • 6 bottles</p>
                          </div>
                          <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Delivered</span>
                        </div>
                        <p style="font-size: 12px; color: var(--text-tertiary); margin-bottom: 8px;">Burgundy selection for dinner party</p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <span style="font-size: 16px; font-weight: 600;">$890</span>
                          <button class="secondary-button">Reorder</button>
                        </div>
                      </div>
                      
                      <!-- Wine Club Subscription -->
                      <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Monthly Subscription</h3>
                      
                      <div class="card" style="background: var(--bg-tertiary);">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 12px;">
                          <div>
                            <h4 style="font-size: 16px; font-weight: 600;">Curator's Selection</h4>
                            <p style="font-size: 14px; color: var(--text-secondary);">3 bottles monthly</p>
                          </div>
                          <div style="text-align: right;">
                            <p style="font-size: 16px; font-weight: 600; color: var(--gold);">$285/month</p>
                            <p style="font-size: 12px; color: var(--success);">Active</p>
                          </div>
                        </div>
                        <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Hand-picked wines from our sommelier team</p>
                        <div style="display: flex; gap: 8px;">
                          <button class="secondary-button" style="flex: 1;">Manage</button>
                          <button class="secondary-button" style="flex: 1;">Skip Next</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Golf Section -->
        <section id="golf" class="flow-section">
          <h2>Golf</h2>
          
          <div class="tab-nav">
            <button class="tab-button active">Bookings</button>
            <button class="tab-button">Scorecard</button>
            <button class="tab-button">Handicap</button>
            <button class="tab-button">Tournaments</button>
            <button class="tab-button">Lessons</button>
          </div>
          
          <div class="tab-container">
          
          <!-- GOLF BOOKINGS -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Golf Bookings</h1>
                  <div class="header-right">
                    <button class="icon-button">📅</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Quick Book -->
                    <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, #2E5C2E, #4A7C4A); color: white;">
                      <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">Quick Tee Time</h3>
                      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 16px;">
                        <div>
                          <p style="font-size: 12px; opacity: 0.9;">Next Available</p>
                          <p style="font-size: 16px; font-weight: 600;">2:30 PM Today</p>
                        </div>
                        <div>
                          <p style="font-size: 12px; opacity: 0.9;">Course</p>
                          <p style="font-size: 16px; font-weight: 600;">Championship</p>
                        </div>
                      </div>
                      <button class="primary-button" style="background: white; color: var(--text-primary); width: 100%;">Book Now</button>
                    </div>
                    
                    <!-- Partner Courses -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Partner Courses</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; gap: 16px; margin-bottom: 12px;">
                        <div style="width: 80px; height: 60px; background: var(--success); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                          <span style="color: white; font-size: 20px;">⛳</span>
                        </div>
                        <div style="flex: 1;">
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Royal Melbourne</h4>
                          <p style="font-size: 13px; color: var(--text-secondary); margin-bottom: 4px;">Championship Course • 18 holes</p>
                          <p style="font-size: 12px; color: var(--gold);">Member Rate: $180</p>
                        </div>
                      </div>
                      <div style="display: flex; gap: 8px;">
                        <button class="secondary-button" style="flex: 1;">View Times</button>
                        <button class="primary-button" style="flex: 1;">Book</button>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; gap: 16px; margin-bottom: 12px;">
                        <div style="width: 80px; height: 60px; background: var(--info); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                          <span style="color: white; font-size: 20px;">⛳</span>
                        </div>
                        <div style="flex: 1;">
                          <h4 style="font-size: 16px; font-weight: 600; margin-bottom: 4px;">Peninsula Kingswood</h4>
                          <p style="font-size: 13px; color: var(--text-secondary); margin-bottom: 4px;">South Course • 18 holes</p>
                          <p style="font-size: 12px; color: var(--gold);">Member Rate: $140</p>
                        </div>
                      </div>
                      <div style="display: flex; gap: 8px;">
                        <button class="secondary-button" style="flex: 1;">View Times</button>
                        <button class="primary-button" style="flex: 1;">Book</button>
                      </div>
                    </div>
                    
                    <!-- Upcoming Rounds -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Upcoming Rounds</h3>
                    
                    <div class="card">
                      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Saturday Round</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">March 23 • 8:00 AM</p>
                        </div>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Confirmed</span>
                      </div>
                      <p style="font-size: 13px; color: var(--text-tertiary); margin-bottom: 12px;">Royal Melbourne Championship • 4 players</p>
                      <button class="secondary-button" style="width: 100%;">Manage Booking</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- GOLF SCORECARD -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Digital Scorecard</h1>
                  <div class="header-right">
                    <button class="icon-button">📄</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Round Info -->
                    <div class="card" style="margin-bottom: 20px; background: var(--bg-tertiary);">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <h3 style="font-size: 16px; font-weight: 600;">Current Round</h3>
                        <span style="background: var(--gold); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Hole 7</span>
                      </div>
                      <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 8px;">Royal Melbourne Championship</p>
                      <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px;">
                        <div>
                          <p style="font-size: 12px; color: var(--text-secondary);">Current Score</p>
                          <p style="font-size: 20px; font-weight: 600; color: var(--success);">-2</p>
                        </div>
                        <div>
                          <p style="font-size: 12px; color: var(--text-secondary);">Total Strokes</p>
                          <p style="font-size: 20px; font-weight: 600;">28</p>
                        </div>
                        <div>
                          <p style="font-size: 12px; color: var(--text-secondary);">Handicap</p>
                          <p style="font-size: 20px; font-weight: 600;">12</p>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Recent Holes -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Holes</h3>
                    
                    <div style="display: grid; gap: 8px; margin-bottom: 20px;">
                      <div class="card" style="padding: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Hole 6</h4>
                            <p style="font-size: 12px; color: var(--text-secondary);">Par 4 • 425m</p>
                          </div>
                          <div style="text-align: right;">
                            <p style="font-size: 18px; font-weight: 700; color: var(--success);">3</p>
                            <p style="font-size: 11px; color: var(--success);">Birdie</p>
                          </div>
                        </div>
                      </div>
                      
                      <div class="card" style="padding: 12px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div>
                            <h4 style="font-size: 14px; font-weight: 600;">Hole 5</h4>
                            <p style="font-size: 12px; color: var(--text-secondary);">Par 3 • 180m</p>
                          </div>
                          <div style="text-align: right;">
                            <p style="font-size: 18px; font-weight: 700;">3</p>
                            <p style="font-size: 11px; color: var(--text-tertiary);">Par</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                      <button class="primary-button">Record Score</button>
                      <button class="secondary-button">GPS Distances</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Rewards Section -->
        <section id="rewards" class="flow-section">
          <h2>Rewards Program</h2>
          
          <!-- REWARDS DASHBOARD -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Rewards</h1>
                  <div class="header-right">
                    <button class="icon-button">🎁</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Points Balance -->
                    <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, var(--gold), #B8995D); color: white;">
                      <h3 style="font-size: 18px; font-weight: 600; margin-bottom: 16px;">SANCTUM Points</h3>
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <div>
                          <p style="font-size: 32px; font-weight: 700;">8,420</p>
                          <p style="font-size: 14px; opacity: 0.9;">Available Points</p>
                        </div>
                        <div style="text-align: right;">
                          <p style="font-size: 18px; font-weight: 600;">Elite</p>
                          <p style="font-size: 12px; opacity: 0.9;">Member Tier</p>
                        </div>
                      </div>
                      <div style="background: rgba(255,255,255,0.2); height: 6px; border-radius: 3px;">
                        <div style="background: white; height: 100%; width: 68%; border-radius: 3px;"></div>
                      </div>
                      <p style="font-size: 12px; opacity: 0.9; margin-top: 8px;">1,580 points to VIP tier</p>
                    </div>
                    
                    <!-- Quick Redeem -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Quick Redeem</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                      <div class="card" style="padding: 16px; text-align: center; cursor: pointer;">
                        <div style="font-size: 32px; margin-bottom: 8px;">🍽️</div>
                        <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 4px;">Dining Credit</h4>
                        <p style="font-size: 12px; color: var(--text-secondary); margin-bottom: 8px;">$50 = 1,000 pts</p>
                        <button class="secondary-button" style="width: 100%;">Redeem</button>
                      </div>
                      
                      <div class="card" style="padding: 16px; text-align: center; cursor: pointer;">
                        <div style="font-size: 32px; margin-bottom: 8px;">🎆</div>
                        <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 4px;">Event Access</h4>
                        <p style="font-size: 12px; color: var(--text-secondary); margin-bottom: 8px;">VIP Events</p>
                        <button class="secondary-button" style="width: 100%;">Browse</button>
                      </div>
                    </div>
                    
                    <!-- Recent Activity -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Activity</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Dining Purchase</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">March 14, 2024</p>
                        </div>
                        <span style="font-size: 16px; font-weight: 600; color: var(--success);">+340 pts</span>
                      </div>
                      <p style="font-size: 12px; color: var(--text-tertiary);">Private dining room booking</p>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Spa Treatment</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">March 11, 2024</p>
                        </div>
                        <span style="font-size: 16px; font-weight: 600; color: var(--success);">+180 pts</span>
                      </div>
                      <p style="font-size: 12px; color: var(--text-tertiary);">Deep tissue massage</p>
                    </div>
                    
                    <div class="card">
                      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Dining Credit</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">March 10, 2024</p>
                        </div>
                        <span style="font-size: 16px; font-weight: 600; color: var(--error);">-1,000 pts</span>
                      </div>
                      <p style="font-size: 12px; color: var(--text-tertiary);">Redeemed $50 dining credit</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Digital Key Section -->
        <section id="digitalkey" class="flow-section">
          <h2>Digital Key</h2>
          
          <!-- DIGITAL KEY MAIN -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Digital Key</h1>
                  <div class="header-right">
                    <button class="icon-button">🔒</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Main Key Card -->
                    <div class="card" style="margin-bottom: 20px; background: linear-gradient(135deg, #1e3c72, #2a5298); color: white; text-align: center; padding: 40px 20px;">
                      <div style="font-size: 80px; margin-bottom: 16px;">🔑</div>
                      <h3 style="font-size: 24px; font-weight: 700; margin-bottom: 8px;">SANCTUM KEY</h3>
                      <p style="font-size: 16px; opacity: 0.9; margin-bottom: 20px;">Dan Humphreys</p>
                      <div style="background: rgba(255,255,255,0.2); border-radius: 20px; padding: 12px 24px; display: inline-block;">
                        <p style="font-size: 14px; font-weight: 600;">Member #2023-0042</p>
                      </div>
                    </div>
                    
                    <!-- Quick Access -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Quick Access</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                      <button class="primary-button" style="padding: 20px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🚪</div>
                        <span style="font-size: 14px;">Club Entry</span>
                      </button>
                      
                      <button class="secondary-button" style="padding: 20px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🏢</div>
                        <span style="font-size: 14px;">Facilities</span>
                      </button>
                      
                      <button class="secondary-button" style="padding: 20px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🏭</div>
                        <span style="font-size: 14px;">Wine Locker</span>
                      </button>
                      
                      <button class="secondary-button" style="padding: 20px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🚗</div>
                        <span style="font-size: 14px;">Valet</span>
                      </button>
                    </div>
                    
                    <!-- Access Log -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Recent Access</h3>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Main Entrance</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">Today, 9:35 AM</p>
                        </div>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Granted</span>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Executive Floor</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">Today, 2:15 PM</p>
                        </div>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Granted</span>
                      </div>
                    </div>
                    
                    <div class="card">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Wine Cellar</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">March 20, 6:45 PM</p>
                        </div>
                        <span style="background: var(--success); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">Granted</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Emergency Section -->
        <section id="emergency" class="flow-section">
          <h2>Emergency</h2>
          
          <!-- EMERGENCY MAIN -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Emergency</h1>
                  <div class="header-right">
                    <button class="icon-button">🚨</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Emergency Alert -->
                    <div class="card" style="margin-bottom: 20px; background: var(--error); color: white; text-align: center; padding: 30px 20px;">
                      <div style="font-size: 60px; margin-bottom: 16px;">🚨</div>
                      <h3 style="font-size: 20px; font-weight: 700; margin-bottom: 12px;">Emergency Services</h3>
                      <p style="font-size: 14px; opacity: 0.9;">Immediate assistance available 24/7</p>
                    </div>
                    
                    <!-- Emergency Contacts -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Emergency Contacts</h3>
                    
                    <div class="card" style="margin-bottom: 12px; border-left: 4px solid var(--error);">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600; color: var(--error);">Emergency Services</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">Police, Fire, Ambulance</p>
                        </div>
                        <button class="primary-button" style="background: var(--error); padding: 12px 20px;">Call 000</button>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Club Security</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">SANCTUM Security Desk</p>
                        </div>
                        <button class="secondary-button">Call</button>
                      </div>
                    </div>
                    
                    <div class="card" style="margin-bottom: 12px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 16px; font-weight: 600;">Club Medical</h4>
                          <p style="font-size: 14px; color: var(--text-secondary);">On-site medical assistance</p>
                        </div>
                        <button class="secondary-button">Call</button>
                      </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 16px;">Quick Actions</h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px; margin-bottom: 20px;">
                      <button class="secondary-button" style="padding: 16px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">📍</div>
                        <span style="font-size: 12px;">Share Location</span>
                      </button>
                      
                      <button class="secondary-button" style="padding: 16px; text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 8px;">📨</div>
                        <span style="font-size: 12px;">Emergency Contact</span>
                      </button>
                    </div>
                    
                    <!-- Emergency Info -->
                    <div class="card" style="background: var(--bg-tertiary);">
                      <h4 style="font-size: 14px; font-weight: 600; margin-bottom: 8px;">Your Location</h4>
                      <p style="font-size: 13px; color: var(--text-secondary); margin-bottom: 8px;">SANCTUM Melbourne</p>
                      <p style="font-size: 13px; color: var(--text-secondary);">Level 18, 120 Collins Street, Melbourne VIC 3000</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Feedback Section -->
        <section id="feedback" class="flow-section">
          <h2>Feedback</h2>
          
          <!-- FEEDBACK FORM -->
          <div class="tab-content">
            <div class="flow-row">
              <div class="mobile-screen">
                <div class="status-bar">
                  <span>9:41 AM</span>
                  <div class="status-icons">
                    <span>📶</span>
                    <span>📶</span>
                    <span>🔋</span>
                  </div>
                </div>
                
                <div class="app-header">
                  <div class="header-left">
                    <button class="icon-button">←</button>
                  </div>
                  <h1>Share Feedback</h1>
                  <div class="header-right">
                    <button class="icon-button">📝</button>
                  </div>
                </div>
                
                <div class="content">
                  <div style="padding: 20px;">
                    <!-- Feedback Header -->
                    <div style="text-align: center; margin-bottom: 32px;">
                      <div style="font-size: 48px; margin-bottom: 16px;">💭</div>
                      <h3 style="font-size: 20px; font-weight: 600; margin-bottom: 8px;">We Value Your Feedback</h3>
                      <p style="font-size: 14px; color: var(--text-secondary);">Help us improve your SANCTUM experience</p>
                    </div>
                    
                    <!-- Feedback Categories -->
                    <div style="margin-bottom: 24px;">
                      <label style="font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">Category</label>
                      <select class="input-field" style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px;">
                        <option value="">Select a category</option>
                        <option value="dining">Dining Experience</option>
                        <option value="facilities">Facilities</option>
                        <option value="events">Events</option>
                        <option value="staff">Staff Service</option>
                        <option value="app">Mobile App</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    
                    <!-- Rating -->
                    <div style="margin-bottom: 24px;">
                      <label style="font-size: 14px; font-weight: 600; margin-bottom: 12px; display: block;">Overall Rating</label>
                      <div style="display: flex; gap: 8px; justify-content: center;">
                        <button style="font-size: 32px; background: none; border: none; cursor: pointer;">⭐</button>
                        <button style="font-size: 32px; background: none; border: none; cursor: pointer;">⭐</button>
                        <button style="font-size: 32px; background: none; border: none; cursor: pointer;">⭐</button>
                        <button style="font-size: 32px; background: none; border: none; cursor: pointer;">⭐</button>
                        <button style="font-size: 32px; background: none; border: none; cursor: pointer; opacity: 0.3;">⭐</button>
                      </div>
                    </div>
                    
                    <!-- Feedback Text -->
                    <div style="margin-bottom: 24px;">
                      <label style="font-size: 14px; font-weight: 600; margin-bottom: 8px; display: block;">Your Feedback</label>
                      <textarea placeholder="Tell us about your experience..." class="input-field" style="width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 8px; min-height: 120px; resize: vertical;"></textarea>
                    </div>
                    
                    <!-- Contact Preference -->
                    <div style="margin-bottom: 24px;">
                      <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                          <h4 style="font-size: 14px; font-weight: 600;">Follow-up Contact</h4>
                          <p style="font-size: 13px; color: var(--text-secondary);">May we contact you about this feedback?</p>
                        </div>
                        <label class="toggle-switch">
                          <input type="checkbox" checked>
                          <span class="toggle-slider"></span>
                        </label>
                      </div>
                    </div>
                    
                    <!-- Submit -->
                    <button class="primary-button" style="width: 100%; margin-bottom: 16px;">Submit Feedback</button>
                    
                    <!-- Alternative Options -->
                    <div style="text-align: center;">
                      <p style="font-size: 13px; color: var(--text-secondary); margin-bottom: 12px;">Prefer to speak directly?</p>
                      <div style="display: flex; gap: 8px;">
                        <button class="secondary-button" style="flex: 1;">Call Manager</button>
                        <button class="secondary-button" style="flex: 1;">Book Meeting</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
    
    <!-- Right-hand Detail Panel -->
    <aside id="detailPanel" class="detail-panel">
      <header class="detail-header">
        <h3 id="detailTitle">Screens</h3>
        <button class="detail-close" id="detailClose">&times;</button>
      </header>

      <!-- Screen List View -->
      <div id="screenList" class="screen-list">
        <div class="screen-list-content">
          <p class="screen-list-empty">Select a section to view screens</p>
        </div>
      </div>

      <!-- Screen Detail View -->
      <div id="screenDetail" class="screen-detail hidden">
        <div class="screen-detail-header">
          <button class="back-button" id="backToList">← Back to List</button>
          <h4 id="screenTitle">Screen Name</h4>
        </div>
        
        <div id="detailPreview" class="detail-preview">
          <img id="screenPreview" src="" alt="Screen preview" style="display: none;">
          <div id="previewPlaceholder">Screen preview will appear here</div>
        </div>

        <div id="detailMeta" class="detail-meta">
          <span class="meta-chip" id="screenStage">Stage: —</span>
          <span class="meta-chip" id="screenUpdated">Updated: —</span>
          <span class="meta-chip" id="screenType">Type: —</span>
        </div>

        <div class="screen-description">
          <h5>Description</h5>
          <p id="screenDescription">Screen description will appear here.</p>
        </div>

        <div class="detail-actions">
          <button class="primary-button" id="viewScreenBtn">View Full Screen</button>
          <button class="secondary-button" id="editScreenBtn">Edit</button>
        </div>
      </div>
    </aside>

  </div><!-- .app-container -->

  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
    // Dark mode toggle with smooth transitions
    const darkModeToggle = document.getElementById('darkModeToggle');
    const body = document.body;
    
    // Check for saved preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    if (currentTheme === 'dark') {
      body.classList.add('dark-mode');
    }
    
    darkModeToggle.addEventListener('click', () => {
      body.classList.toggle('dark-mode');
      
      // Save preference
      const theme = body.classList.contains('dark-mode') ? 'dark' : 'light';
      localStorage.setItem('theme', theme);
      
      // Add animation class
      body.style.transition = 'all 0.3s ease';
    });

    // Navigation system
    const navItems = document.querySelectorAll('.nav-item');
    const sections = document.querySelectorAll('.flow-section');
    
    navItems.forEach(item => {
      item.addEventListener('click', () => {
        const targetSection = item.dataset.section;
        
        // Update active nav with animation
        navItems.forEach(nav => nav.classList.remove('active'));
        item.classList.add('active');
        
        // Show target section with fade animation
        sections.forEach(section => {
          if (section.id === targetSection) {
            section.classList.add('active');
            section.style.animation = 'fadeIn 0.3s ease-in-out';
          } else {
            section.classList.remove('active');
          }
        });
        
        // Scroll to top of content area
        document.querySelector('.main-content').scrollTop = 0;
      });
    });

    // Tab navigation within sections
    document.querySelectorAll('.tab-nav').forEach(tabNav => {
      const buttons = tabNav.querySelectorAll('.tab-button');
      const contents = tabNav.nextElementSibling.querySelectorAll('.tab-content');
      
      buttons.forEach((button, index) => {
        button.addEventListener('click', () => {
          // Update active tab with transition
          buttons.forEach(btn => btn.classList.remove('active'));
          contents.forEach(content => {
            content.classList.remove('active');
            content.style.opacity = '0';
          });
          
          button.classList.add('active');
          
          // Fade in new content
          setTimeout(() => {
            contents[index].classList.add('active');
            contents[index].style.opacity = '1';
          }, 150);
        });
      });
    });

    // Add interactive animations
    document.querySelectorAll('.primary-button, .secondary-button').forEach(button => {
      button.addEventListener('click', function(e) {
        // Ripple effect
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        this.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
      });
    });

    // Haptic feedback simulation (visual feedback)
    document.querySelectorAll('button, .clickable').forEach(element => {
      element.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.98)';
      });
      
      element.addEventListener('touchend', function() {
        this.style.transform = 'scale(1)';
      });
    });

    // Screen List Management System
    const detailPanel = document.getElementById('detailPanel');
    const detailClose = document.getElementById('detailClose');
    const detailTitle = document.getElementById('detailTitle');
    const screenList = document.getElementById('screenList');
    const screenDetail = document.getElementById('screenDetail');
    const backToList = document.getElementById('backToList');
    
    // Screen data for each section
    const screenData = {
      home: [
        { id: 'home-dashboard', title: 'Dashboard', subtitle: 'Main home screen with quick actions', status: 'complete', type: 'Primary Screen', description: 'The main dashboard shows membership status, quick actions, schedule preview, and featured events.' },
        { id: 'home-notifications', title: 'Notifications', subtitle: 'Notification center', status: 'complete', type: 'Secondary Screen', description: 'Displays all member notifications including booking confirmations, event updates, and system messages.' },
        { id: 'home-quickactions', title: 'Quick Actions', subtitle: 'Fast access to common features', status: 'todo', type: 'Secondary Screen', description: 'Provides quick access to frequently used features like booking, ordering, and guest management.' }
      ],
      membership: [
        { id: 'membership-overview', title: 'Membership Overview', subtitle: 'Tier info and benefits', status: 'complete', type: 'Primary Screen', description: 'Shows current membership tier, benefits, and upgrade options.' },
        { id: 'membership-benefits', title: 'Benefits', subtitle: 'Detailed benefits list', status: 'complete', type: 'Secondary Screen', description: 'Complete list of membership benefits and privileges.' },
        { id: 'membership-partners', title: 'Partner Network', subtitle: 'Partner establishments', status: 'complete', type: 'Secondary Screen', description: 'Directory of partner clubs and establishments with reciprocal benefits.' }
      ],
      guest: [
        { id: 'guest-active', title: 'Active Guests', subtitle: 'Currently visiting guests', status: 'complete', type: 'Primary Screen', description: 'Shows all currently signed-in guests with check-in times and status.' },
        { id: 'guest-invite', title: 'Invite Guest', subtitle: 'New guest invitation', status: 'complete', type: 'Primary Screen', description: 'Form to invite new guests with date selection and approval workflow.' },
        { id: 'guest-history', title: 'Guest History', subtitle: 'Past guest visits', status: 'complete', type: 'Secondary Screen', description: 'Historical record of all guest visits and interactions.' }
      ],
      events: [
        { id: 'events-upcoming', title: 'Upcoming Events', subtitle: 'Featured and upcoming events', status: 'complete', type: 'Primary Screen', description: 'Displays featured events and upcoming calendar with booking options.' },
        { id: 'events-bookings', title: 'My Bookings', subtitle: 'User event bookings', status: 'complete', type: 'Primary Screen', description: 'Personal event bookings with management options.' },
        { id: 'events-categories', title: 'Event Categories', subtitle: 'Browse by category', status: 'complete', type: 'Secondary Screen', description: 'Categorized view of all available events and experiences.' },
        { id: 'events-past', title: 'Past Events', subtitle: 'Historical events', status: 'complete', type: 'Secondary Screen', description: 'Archive of past events with photos and memories.' }
      ],
      facilities: [
        { id: 'facilities-overview', title: 'Facilities Overview', subtitle: 'Availability dashboard', status: 'complete', type: 'Primary Screen', description: 'Real-time availability of all club facilities.' },
        { id: 'facilities-meeting', title: 'Meeting Rooms', subtitle: 'Boardroom booking', status: 'complete', type: 'Primary Screen', description: 'Meeting room booking system with availability and amenities.' },
        { id: 'facilities-wellness', title: 'Wellness Facilities', subtitle: 'Spa and fitness booking', status: 'complete', type: 'Primary Screen', description: 'Spa, sauna, and fitness facility reservations.' },
        { id: 'facilities-dining', title: 'Private Dining', subtitle: 'Dining room reservations', status: 'complete', type: 'Primary Screen', description: 'Private dining room booking for special occasions.' }
      ],
      fnb: [
        { id: 'fnb-menu', title: 'Restaurant Menu', subtitle: 'Browse and order', status: 'complete', type: 'Primary Screen', description: 'Digital menu with ordering capabilities and dietary preferences.' },
        { id: 'fnb-cart', title: 'Order Cart', subtitle: 'Review order', status: 'complete', type: 'Primary Screen', description: 'Order review and customization before checkout.' },
        { id: 'fnb-tracking', title: 'Order Tracking', subtitle: 'Live order status', status: 'complete', type: 'Secondary Screen', description: 'Real-time order tracking and delivery updates.' },
        { id: 'fnb-service', title: 'Table Service', subtitle: 'In-venue service', status: 'complete', type: 'Secondary Screen', description: 'Table-side ordering and service requests.' }
      ],
      wellness: [
        { id: 'wellness-dashboard', title: 'Wellness Dashboard', subtitle: 'Fitness overview', status: 'complete', type: 'Primary Screen', description: 'Personal wellness dashboard with stats and goals.' },
        { id: 'wellness-book', title: 'Book Session', subtitle: 'PT and class booking', status: 'complete', type: 'Primary Screen', description: 'Book personal training and fitness classes.' },
        { id: 'wellness-progress', title: 'My Progress', subtitle: 'Fitness tracking', status: 'complete', type: 'Secondary Screen', description: 'Track fitness progress and achievements.' },
        { id: 'wellness-challenges', title: 'Challenges', subtitle: 'Wellness challenges', status: 'complete', type: 'Secondary Screen', description: 'Member wellness challenges and competitions.' }
      ],
      social: [
        { id: 'social-directory', title: 'Member Directory', subtitle: 'Find members', status: 'complete', type: 'Primary Screen', description: 'Searchable member directory with profiles.' },
        { id: 'social-messages', title: 'Messages', subtitle: 'Direct messaging', status: 'complete', type: 'Primary Screen', description: 'Private messaging system between members.' },
        { id: 'social-groups', title: 'Groups', subtitle: 'Member groups', status: 'complete', type: 'Secondary Screen', description: 'Join and participate in member interest groups.' },
        { id: 'social-feed', title: 'Social Feed', subtitle: 'Club activity feed', status: 'complete', type: 'Secondary Screen', description: 'Social feed with member updates and club news.' }
      ],
      concierge: [
        { id: 'concierge-services', title: 'Available Services', subtitle: 'Service catalog', status: 'complete', type: 'Primary Screen', description: 'Comprehensive catalog of concierge services.' },
        { id: 'concierge-requests', title: 'My Requests', subtitle: 'Active requests', status: 'complete', type: 'Primary Screen', description: 'Track active concierge service requests.' },
        { id: 'concierge-chat', title: 'Chat Support', subtitle: 'Live chat', status: 'complete', type: 'Secondary Screen', description: 'Direct chat with concierge team.' }
      ],
      payment: [
        { id: 'payment-overview', title: 'Payment Overview', subtitle: 'Account summary', status: 'complete', type: 'Primary Screen', description: 'Payment account overview and balance.' },
        { id: 'payment-transactions', title: 'Transactions', subtitle: 'Payment history', status: 'complete', type: 'Primary Screen', description: 'Detailed transaction history and receipts.' },
        { id: 'payment-methods', title: 'Payment Methods', subtitle: 'Saved cards', status: 'complete', type: 'Primary Screen', description: 'Manage saved payment methods and cards.' },
        { id: 'payment-statements', title: 'Billing Statements', subtitle: 'Monthly statements', status: 'complete', type: 'Secondary Screen', description: 'Download monthly billing statements.' }
      ],
      settings: [
        { id: 'settings-profile', title: 'Profile Settings', subtitle: 'Personal information', status: 'complete', type: 'Primary Screen', description: 'Manage personal profile and contact information.' },
        { id: 'settings-notifications', title: 'Notifications', subtitle: 'Notification preferences', status: 'complete', type: 'Primary Screen', description: 'Configure notification preferences and alerts.' },
        { id: 'settings-privacy', title: 'Privacy Settings', subtitle: 'Privacy controls', status: 'complete', type: 'Secondary Screen', description: 'Manage privacy settings and data preferences.' },
        { id: 'settings-security', title: 'Security', subtitle: 'Account security', status: 'complete', type: 'Secondary Screen', description: 'Security settings including password and 2FA.' }
      ],
      staff: [
        { id: 'staff-checkin', title: 'Member Check-in', subtitle: 'Verify member access', status: 'todo', type: 'Staff Screen', description: 'Staff interface for member check-in and verification.' },
        { id: 'staff-guests', title: 'Guest Management', subtitle: 'Guest approval system', status: 'todo', type: 'Staff Screen', description: 'Staff tools for guest approval and management.' },
        { id: 'staff-facilities', title: 'Facility Status', subtitle: 'Real-time facility management', status: 'todo', type: 'Staff Screen', description: 'Monitor and manage facility availability and maintenance.' },
        { id: 'staff-orders', title: 'Order Management', subtitle: 'F&B order processing', status: 'todo', type: 'Staff Screen', description: 'Kitchen and service staff order management system.' }
      ]
    };

    // Update navigation to show screen lists in right sidebar
    navItems.forEach(item => {
      item.addEventListener('click', () => {
        const targetSection = item.dataset.section;
        
        // Update active nav
        navItems.forEach(nav => nav.classList.remove('active'));
        item.classList.add('active');
        
        // Show target section
        sections.forEach(section => {
          if (section.id === targetSection) {
            section.classList.add('active');
          } else {
            section.classList.remove('active');
          }
        });
        
        // Update right sidebar with screen list
        showScreenList(targetSection);
        
        // Show detail panel
        detailPanel.classList.remove('collapsed');
      });
    });

    function showScreenList(sectionName) {
      detailTitle.textContent = 'Screen Details';
      
      // Show empty state initially
      screenDetail.classList.add('hidden');
      screenList.classList.remove('hidden');
      
      screenList.querySelector('.screen-list-content').innerHTML = 
        '<p class="screen-list-empty">Click on any screen in the main area to view details</p>';
      
      // Add click handlers to all mobile screens in the main content
      const currentSection = document.getElementById(sectionName);
      if (currentSection) {
        const mobileScreens = currentSection.querySelectorAll('.mobile-screen');
        mobileScreens.forEach((screen, index) => {
          screen.style.cursor = 'pointer';
          screen.addEventListener('click', () => {
            // Remove active state from all screens
            mobileScreens.forEach(s => s.classList.remove('screen-selected'));
            // Add active state to clicked screen
            screen.classList.add('screen-selected');
            
            // Get screen data for this section and index
            const screens = screenData[sectionName] || [];
            const screenData_item = screens[index] || {
              id: `${sectionName}-screen-${index}`,
              title: `Screen ${index + 1}`,
              subtitle: 'Mobile interface screen',
              status: 'complete',
              type: 'Primary Screen',
              description: `Screen ${index + 1} of the ${sectionName} section.`
            };
            
            showScreenDetail(screenData_item);
          });
        });
      }
    }

    function showScreenDetail(screen) {
      // Hide screen list, show detail
      screenList.classList.add('hidden');
      screenDetail.classList.remove('hidden');
      
      // Update detail content
      document.getElementById('screenTitle').textContent = screen.title;
      document.getElementById('screenStage').textContent = `Stage: ${screen.status}`;
      document.getElementById('screenUpdated').textContent = `Updated: ${new Date().toLocaleDateString()}`;
      document.getElementById('screenType').textContent = `Type: ${screen.type}`;
      document.getElementById('screenDescription').textContent = screen.description;
      
      // Update preview
      document.getElementById('previewPlaceholder').textContent = `Preview for ${screen.title}`;
      
      // Update title to show we're viewing details
      detailTitle.textContent = screen.title;
    }

    // Back to empty state functionality
    backToList.addEventListener('click', () => {
      screenDetail.classList.add('hidden');
      screenList.classList.remove('hidden');
      
      // Remove selection from all screens
      document.querySelectorAll('.mobile-screen.screen-selected').forEach(screen => {
        screen.classList.remove('screen-selected');
      });
      
      // Reset title
      detailTitle.textContent = 'Screen Details';
    });

    // Close detail panel
    detailClose.addEventListener('click', () => {
      detailPanel.classList.add('collapsed');
      // Clear all screen selections
      document.querySelectorAll('.mobile-screen.screen-selected').forEach(screen => {
        screen.classList.remove('screen-selected');
      });
    });

    // ESC key to close
    window.addEventListener('keydown', e => {
      if (e.key === 'Escape') {
        if (!screenDetail.classList.contains('hidden')) {
          // If in detail view, go back to empty state
          backToList.click();
        } else {
          // If in empty state, close panel
          detailPanel.classList.add('collapsed');
          // Clear all screen selections
          document.querySelectorAll('.mobile-screen.screen-selected').forEach(screen => {
            screen.classList.remove('screen-selected');
          });
        }
      }
    });

/* --- Auto-build gallery -------------------------------- */
const gallery = document.getElementById('autoGallery');
const allScreens = document.querySelectorAll('.mobile-screen');
allScreens.forEach((scr,idx)=>{
  const title = scr.querySelector('.app-header h1')?.textContent.trim() || `Screen ${idx+1}`;
  const img = scr.querySelector('img')?.src || 'https://placehold.co/340x720?text=Preview';
  const anchorId = scr.dataset.anchor || ('screen-'+idx);
  scr.dataset.anchor = anchorId; scr.id = anchorId;
  const fig = document.createElement('figure');
  fig.className='thumb';
  fig.innerHTML = `<img src="${img}"><caption>${title}</caption>`;
  fig.addEventListener('click',()=>{openDetail({title,img});scr.scrollIntoView({behavior:'smooth',block:'start'});} );
  gallery.appendChild(fig);
});

/* Activate Overview by default */
document.querySelector('[data-section="overview"]').addEventListener('click', ()=>{});

    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      @keyframes slideUp {
        from { transform: translateY(100%); }
        to { transform: translateY(0); }
      }
      
      .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s ease-out;
        pointer-events: none;
      }
      
      @keyframes ripple-animation {
        to {
          transform: scale(4);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'97ec98cee9087c19',t:'MTc1NzgxODQzNy4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
