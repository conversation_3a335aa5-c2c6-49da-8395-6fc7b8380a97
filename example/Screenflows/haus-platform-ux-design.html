<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="color-scheme" content="light dark" />
    <title>HAUS Platform — User Experience Design Document</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/geist@1.3.0/dist/geist.min.css" />
    <script defer src="https://cdn.tailwindcss.com?plugins=typography"></script>
  </head>
  <body class="bg-neutral-50 text-neutral-900 dark:bg-neutral-950 dark:text-white antialiased" style="font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', Segoe UI, Roboto, Helvetica, Arial, system-ui, sans-serif;">
    <div class="fixed inset-0 -z-10">
      <div class="absolute inset-0 bg-gradient-to-b from-white to-neutral-100 dark:hidden"></div>
      <div class="absolute inset-0 opacity-40 dark:hidden" style="background: radial-gradient(800px 500px at 15% 10%, rgba(14,165,233,.10), transparent), radial-gradient(700px 500px at 85% 90%, rgba(14,165,233,.08), transparent);"></div>
      <div class="absolute inset-0 hidden dark:block bg-gradient-to-br from-neutral-950 via-neutral-900 to-neutral-950"></div>
      <div class="absolute inset-0 hidden dark:block opacity-30" style="background: radial-gradient(1000px 600px at 20% 10%, rgba(99,102,241,.20), transparent), radial-gradient(900px 500px at 80% 90%, rgba(14,165,233,.18), transparent);"></div>
    </div>
    <main class="mx-auto max-w-4xl px-4 py-8 sm:py-10">
      <header class="mb-6 flex items-center justify-between">
        <h1 class="text-2xl sm:text-3xl font-semibold tracking-tight">HAUS Platform — User Experience Design Document</h1>
        <a href="ref.html" class="text-[12px] px-2 py-1 rounded-lg bg-black/5 dark:bg-white/10 border border-black/10 dark:border-white/10 hover:bg-black/10 dark:hover:bg-white/15 transition">View Prototype</a>
      </header>
      <article class="prose prose-neutral dark:prose-invert max-w-none">
        <pre class="whitespace-pre-wrap text-sm leading-6">
# HAUS Platform - User Experience Design Document

## 1. USER PERSONAS

### Primary Personas

#### 1.1 First-Time Home Buyer - "Sarah Chen"
- **Age**: 28, Marketing Manager
- **Tech Savvy**: High
- **Goals**: Find affordable first home in good school district
- **Pain Points**: Overwhelmed by options, unsure about fair pricing
- **HAUS Value**: AI guidance, market analysis, simplified discovery

#### 1.2 Property Investor - "Marcus Williams"
- **Age**: 45, Business Owner
- **Tech Savvy**: Medium
- **Goals**: Identify high-yield investment properties
- **Pain Points**: Time-consuming analysis, scattered data
- **HAUS Value**: Investment analytics, portfolio comparison tools

#### 1.3 Upgrading Family - "The Nguyens"
- **Age**: 35-40, Dual Income
- **Tech Savvy**: Medium-High
- **Goals**: Larger home for growing family
- **Pain Points**: Coordinating viewings, comparing neighborhoods
- **HAUS Value**: Virtual tours, neighborhood insights, family-friendly filters

---

## 2. CORE USER JOURNEYS

### Journey 1: First-Time Buyer Discovery Flow

#### Stage 1: Awareness & Onboarding
**Entry Points**:
- Google search: "AI property search Australia"
- Social media ad: "Find your perfect home 10x faster"
- Friend referral with invitation code

**Touchpoints**:
1. Landing page visit
2. Value prop video (30 seconds)
3. Sign-up/onboarding
4. Initial preference capture
5. AI introduction

#### Stage 2: Search & Discovery
1. Natural language search input
2. AI clarification questions
3. Results presentation
4. Filter refinement
5. Saved searches creation

#### Stage 3: Deep Evaluation
1. Property detail exploration
2. DEEPHAUS analytics review
3. Virtual tour experience
4. AI Copilot consultation
5. Comparison shortlisting

#### Stage 4: Decision & Action
1. Final comparison
2. Finance pre-qualification
3. Inspection scheduling
4. Offer preparation assistance
5. Transaction support

---

## 3. DETAILED SCREEN FLOWS

### 3.1 ONBOARDING FLOW

#### Screen 1: Welcome Landing
```
┌─────────────────────────────────┐
│        HAUS Logo                │
│                                 │
│   "Find Your Perfect Property   │
│       10x Faster with AI"       │
│                                 │
│   [Get Started] [Sign In]       │
│                                 │
│   ▼ How It Works               │
└─────────────────────────────────┘
```

**Elements**:
- Hero video background (subtle property transitions)
- Primary CTA: "Get Started" (green, prominent)
- Secondary: "Sign In" (outline button)
- Trust indicators: "50,000+ Happy Users"

#### Screen 2: Quick Registration
```
┌─────────────────────────────────┐
│  Let's Get You Started          │
│                                 │
│  ┌─────────────────────┐       │
│  │ Email/Phone         │       │
│  └─────────────────────┘       │
│                                 │
│  OR                             │
│                                 │
│  [Continue with Google]         │
│  [Continue with Apple]          │
│                                 │
│  □ I agree to terms            │
│                                 │
│  [Next →]                       │
└─────────────────────────────────┘
```

**Elements**:
- Minimal fields (just email/phone)
- Social login options
- Progress indicator (Step 1 of 3)
- Skip option for browsing

#### Screen 3: AI Preference Capture
```
┌─────────────────────────────────┐
│  Hi! I'm your AI assistant.     │
│  What are you looking for?      │
│                                 │
│  [🏠 Buying]  [💰 Investing]    │
│  [🔄 Renting] [📊 Research]     │
│                                 │
│  Budget Range:                  │
│  [$400k ─────●───── $1.2M]     │
│                                 │
│  Preferred Locations:           │
│  [+ Add suburbs/cities]         │
│                                 │
│  [Skip for now] [Continue →]    │
└─────────────────────────────────┘
```

**Elements**:
- Conversational UI tone
- Visual selection cards
- Smart defaults based on market
- Optional detailed preferences

### 3.2 MAIN SEARCH & DISCOVERY

#### Screen 4: Natural Language Search Interface
```
┌─────────────────────────────────┐
│ HAUS  [Profile] [Saved] [Menu]  │
├─────────────────────────────────┤
│                                 │
│  🎤 "Find me a modern 3-bedroom │
│     apartment near good schools │
│     under $900k in Melbourne"   │
│                                 │
│  [Search] or press Enter        │
│                                 │
│  Suggested searches:            │
│  • Investment properties CBD    │
│  • Family homes near parks      │
│  • New developments Eastern     │
│                                 │
│  Recent Searches:               │
│  • 2BR apartments Southbank     │
└─────────────────────────────────┘
```

**Features**:
- Voice input button (prominent)
- Natural language text field
- AI-powered suggestions
- Search history with one-tap repeat
- Real-time query validation

#### Screen 5: AI Clarification Dialog
```
┌─────────────────────────────────┐
│  🤖 Let me clarify a few things:│
│                                 │
│  "Good schools" - Important?    │
│  [Primary] [Secondary] [Both]   │
│                                 │
│  Preferred school rating:        │
│  [★★★★☆ and above]             │
│                                 │
│  Must have parking?             │
│  [Yes, 1 spot] [Yes, 2+] [No]  │
│                                 │
│  [Show Results] [More Options]  │
└─────────────────────────────────┘
```

**Features**:
- Contextual clarifications only
- Quick-tap options
- Smart defaults
- "Show Results" to skip

#### Screen 6: Search Results Grid
```
┌─────────────────────────────────┐
│ 🎯 Found 47 matches (98% fit)   │
│ [Map View] [Refine] [Sort ▼]   │
├─────────────────────────────────┤
│ ┌──────┐ 423 Collins Street    │
│ │ IMG  │ 3BR | 2BA | 1 Car     │
│ │      │ $875,000              │
│ └──────┘ ★4.8 School | 95% Fit │
│          [♥] [Tour] [Analyze]  │
├─────────────────────────────────┤
│ ┌──────┐ 56 Bourke Road        │
│ │ IMG  │ 3BR | 2BA | 2 Car     │
│ │      │ $920,000              │
│ └──────┘ ★4.5 School | 92% Fit │
│          [♥] [Tour] [Analyze]  │
└─────────────────────────────────┘
```

**Features**:
- AI match percentage for each property
- Quick action buttons per listing
- Smart badges (School rating, Investment score)
- Infinite scroll with lazy loading
- Map/List toggle

### 3.3 PROPERTY DETAIL & ANALYTICS

#### Screen 7: Property Detail Hero
```
┌─────────────────────────────────┐
│ [← Back] [Share] [♥ Save]      │
├─────────────────────────────────┤
│                                 │
│     [Immersive Photo Gallery]   │
│     [360° Tour Available]       │
│                                 │
├─────────────────────────────────┤
│ 423 Collins Street, Melbourne   │
│ 3 BR | 2 BA | 1 Car | 120m²    │
│                                 │
│ $875,000 (Fair Value: $880k)   │
│ ▲ 2.5% below market            │
│                                 │
│ [💬 AI Copilot] [🎬 3D Tour]   │
│ [📊 Full Analysis] [Schedule]   │
└─────────────────────────────────┘
```

**Features**:
- Full-screen photo gallery with AI highlights
- Instant fair value comparison
- Prominent CTAs for key features
- Quick stats summary
- Sharing and saving options

#### Screen 8: DEEPHAUS Analytics Dashboard
```
┌─────────────────────────────────┐
│    DEEPHAUS AI Analysis         │
│    ━━━━━━━━━━━━━━━━━━         │
├─────────────────────────────────┤
│ Investment Score: 8.7/10 🟢     │
│                                 │
│ 📈 Value Projection (5 years)   │
│ ┌────────────────────┐         │
│ │     📊 Graph       │         │
│ │ +18% ($1.03M)     │         │
│ └────────────────────┘         │
│                                 │
│ 🏘️ Neighborhood Score: 9.2     │
│ • Schools: ★★★★★               │
│ • Transport: 450m to train      │
│ • Amenities: 15+ within 1km     │
│                                 │
│ 💰 Rental Yield: 4.8%          │
│ Est. $750-800/week             │
│                                 │
│ [Download Report] [Compare]     │
└─────────────────────────────────┘
```

**Features**:
- Visual data presentation
- Color-coded scoring
- Expandable sections
- PDF report generation
- Add to comparison tool

### 3.4 IMMERSIVE 3D TOUR EXPERIENCE

#### Screen 9: Virtual Tour Interface
```
┌─────────────────────────────────┐
│ [Exit Tour] [Settings] [Help]   │
├─────────────────────────────────┤
│                                 │
│                                 │
│      [3D Rendered Space]        │
│                                 │
│   ● Living Room                 │
│                                 │
│                                 │
├─────────────────────────────────┤
│ [◀] [▶] [Floor Plan] [Notes]   │
│                                 │
│ 🎤 "Show me the kitchen"        │
│ 💡 AI Insight: "North-facing    │
│    windows provide natural..."   │
└─────────────────────────────────┘
```

**Features**:
- Full-screen immersive view
- Voice navigation commands
- Contextual AI insights overlay
- Room-by-room navigation dots
- Measurement tools
- Note-taking capability
- VR mode option

#### Screen 10: AI Copilot Conversation
```
┌─────────────────────────────────┐
│        AI Property Copilot      │
├─────────────────────────────────┤
│ 🤖 Hi! I'm here to help you    │
│ understand this property.       │
│                                 │
│ Common questions:               │
│ • Renovation potential?         │
│ • Hidden costs to consider?     │
│ • Compare to similar props?     │
│                                 │
│ You: "Is this good for kids?"   │
│                                 │
│ 🤖 Great for families! Here's   │
│ why: 3 parks within 500m,      │
│ top-rated primary school 300m,  │
│ quiet street, fenced yard...    │
│                                 │
│ [🎤 Voice] [Type message...]    │
└─────────────────────────────────┘
```

**Features**:
- Natural conversation flow
- Suggested questions
- Voice or text input
- Contextual property insights
- Links to relevant data
- Save conversation history

### 3.5 COMPARISON & DECISION TOOLS

#### Screen 11: Property Comparison Table
```
┌─────────────────────────────────┐
│     Compare Properties          │
│  [+ Add Property] [Clear All]   │
├─────────────────────────────────┤
│         Prop A  |  Prop B       │
│ Price   $875k  |  $920k        │
│ Size    120m²  |  135m²        │
│ School  ★★★★★  |  ★★★★         │
│ Invest  8.7    |  7.9           │
│ Yield   4.8%   |  4.2%          │
│                                 │
│ 🏆 Winner: Property A           │
│ Better value & schools          │
│                                 │
│ [Download PDF] [Share]          │
└─────────────────────────────────┘
```

**Features**:
- Side-by-side comparison
- AI recommendation
- Weighted scoring
- Shareable reports
- Custom criteria weights

### 3.6 TRANSACTION SUPPORT

#### Screen 12: Offer Preparation Assistant
```
┌─────────────────────────────────┐
│    Prepare Your Offer           │
├─────────────────────────────────┤
│ AI Market Analysis:             │
│ • Recent sales: $850-890k       │
│ • Days on market: 28            │
│ • Suggested offer: $865k        │
│                                 │
│ Your Offer Amount:              │
│ [$_865,000___________]          │
│                                 │
│ Conditions:                     │
│ ☑ Subject to finance            │
│ ☑ Building inspection           │
│ ☐ Sale of existing property     │
│                                 │
│ [Get Pre-approval] [Submit →]   │
└─────────────────────────────────┘
```

**Features**:
- AI-powered offer suggestions
- Market context
- Standard conditions
- Finance integration
- Document generation

---

## 4. MOBILE-FIRST RESPONSIVE DESIGN

### 4.1 Mobile Navigation Pattern
```
┌──────────────┐
│   HAUS       │
│ ┌──────────┐ │
│ │          │ │
│ │  Search  │ │
│ │  Card    │ │
│ └──────────┘ │
│              │
│ ┌──────────┐ │
│ │ Property │ │
│ └──────────┘ │
│              │
│ [●][○][○][○] │
└──────────────┘
```

**Mobile Features**:
- Thumb-friendly navigation
- Swipe gestures between properties
- Voice search prominent
- Collapsed filters
- Bottom navigation bar

### 4.2 Tablet Optimization
- Split-screen: List + Map view
- Enhanced comparison tools
- Landscape 3D tours
- Multi-column layouts

---

## 5. KEY INTERACTION PATTERNS

### 5.1 Voice Interactions
- **Trigger**: Tap microphone or "Hey HAUS"
- **Visual Feedback**: Pulsing animation during listening
- **Processing**: Real-time transcription display
- **Confirmation**: Read-back before action

### 5.2 Gesture Controls
- **Swipe Right**: Save property
- **Swipe Left**: Dismiss
- **Pinch**: Zoom in 3D tours
- **Long Press**: Quick actions menu
- **Pull Down**: Refresh results

### 5.3 Progressive Disclosure
- **Level 1**: Essential info (price, beds, location)
- **Level 2**: Key metrics (scores, transport)
- **Level 3**: Deep analytics (full DEEPHAUS report)
- **Level 4**: Professional insights (market trends)

---

## 6. NOTIFICATION STRATEGY

### 6.1 Push Notifications
- **New Matches**: "3 new properties match your criteria"
- **Price Drops**: "Price reduced on your saved property"
- **Market Insights**: "Weekend auction results in your area"
- **Tour Reminders**: "Virtual tour starting in 10 minutes"

### 6.2 In-App Notifications
- AI insights updates
- Comparison suggestions
- Document ready
- Response from agents

---

## 7. ACCESSIBILITY FEATURES

### 7.1 Core Accessibility
- **Screen Reader**: Full ARIA support
- **Keyboard Navigation**: Tab-through interface
- **High Contrast Mode**: WCAG AAA compliance
- **Text Scaling**: 50-200% without breaking layout
- **Voice Control**: Complete voice navigation

### 7.2 Inclusive Design
- Color-blind friendly palettes
- Closed captions for tour videos
- Multiple input methods
- Cognitive load reduction
- Clear error messages

---

## 8. PERFORMANCE METRICS

### 8.1 User Flow Metrics
- **Onboarding Completion**: Target 85%
- **Search-to-Save**: <3 clicks
- **First Virtual Tour**: Within 5 minutes
- **Time to Shortlist**: <20 minutes average

### 8.2 Engagement Metrics
- **Daily Active Users**: 40% of MAU
- **Properties Viewed**: 15+ per session
- **Virtual Tours Started**: 60% of users
- **AI Copilot Interactions**: 5+ questions per property

---

## 9. ERROR STATES & EDGE CASES

### 9.1 Common Error Handling
```
┌─────────────────────────────────┐
│    No Results Found             │
│                                 │
│ Let's adjust your search:       │
│ • Increase budget by 10%?       │
│ • Expand location radius?       │
│ • Remove must-haves?            │
│                                 │
│ [Adjust Filters] [Save Alert]   │
└─────────────────────────────────┘
```

### 9.2 Offline Mode
- Cached property views
- Saved searches accessible
- Queue actions for sync
- Clear offline indicators

---

## 10. A/B TESTING PRIORITIES

### 10.1 Onboarding Tests
- **Test A**: Immediate search vs. preference capture
- **Test B**: Social proof placement
- **Test C**: Voice-first vs. text-first

### 10.2 Conversion Tests
- CTA button colors and copy
- Virtual tour prompts timing
- AI insights presentation
- Comparison tool triggers

---

This comprehensive UX design document provides the blueprint for building the HAUS platform's user interface, ensuring a seamless, intuitive, and powerful property search experience that delivers on the promise of finding properties 10x faster through AI.
        </pre>
      </article>
    </main>
  </body>
  </html>

