<!doctype html>
<html lang="en"><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>HAUS — Storyboard</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Manrope:wght@400;500;600&display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<script defer src="https://unpkg.com/lucide@0.468.0/dist/umd/lucide.js"></script>
<style>
@keyframes neonPulse {0%,100%{box-shadow:inset 0 0 0 1px rgba(255,255,255,0.1),inset 0 2px 4px rgba(0,0,0,0.3),inset 0 0 15px rgba(6,182,212,0.3),inset 0 0 25px rgba(59,130,246,0.2);}50%{box-shadow:inset 0 0 0 1px rgba(255,255,255,0.15),inset 0 2px 4px rgba(0,0,0,0.3),inset 0 0 25px rgba(6,182,212,0.5),inset 0 0 40px rgba(59,130,246,0.3),inset 0 0 60px rgba(6,182,212,0.2);}}
.neon-border { animation: none !important; box-shadow: none !important; }
@keyframes entranceSlideUp {0%{opacity:0;transform:translateY(80px) scale(0.95);}100%{opacity:1;transform:translateY(0) scale(1);}}
.entrance-animation-1 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.2s; opacity: 0; }
.entrance-animation-2 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.4s; opacity: 0; }
.entrance-animation-3 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.6s; opacity: 0; }
/* iframe preview scaling */
.frame-tile { position: relative; height: 224px; background-color: rgba(10,10,10,0.6); border-radius: 0.75rem; overflow: hidden; }
.frame-tile iframe { width: 390px; height: 844px; transform: scale(0.26); transform-origin: top left; border: 0; position: absolute; top: 10px; left: 10px; }
.frame-live iframe { width: 390px; height: 844px; border: 0; }
</style></head>
<body class="min-h-screen bg-neutral-950 text-neutral-100 antialiased" style="font-family: Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, Apple Color Emoji, Segoe UI Emoji;">
  <!-- Backdrop -->
  <div class="pointer-events-none fixed inset-0 -z-10">
    <div class="absolute -top-32 left-1/2 -translate-x-1/2 w-[1200px] h-[1200px] bg-gradient-to-br from-cyan-400/20 via-blue-500/10 to-cyan-500/20 blur-3xl rounded-full"></div>
  </div>

  <!-- Top Nav -->
  <header class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-neutral-950/60 bg-neutral-950/80 border-b border-white/10">
    <div class="max-w-[1600px] mx-auto px-4 sm:px-6">
      <div class="h-14 flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div class="size-8 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 shadow-[0_0_24px_rgba(34,211,238,0.35)] flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="house" class="lucide lucide-house w-4.5 h-4.5"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"/><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/></svg>
          </div>
          <div class="text-[15px] tracking-tight">HAUS</div>
          <div class="text-neutral-600">/</div>
          <div class="text-[14px] text-neutral-300">Storyboard</div>
        </div>
      </div>
    </div>
  </header>

  <main class="w-full">
    <div class="max-w-[1800px] mx-auto">
      <div class="flex">
        <!-- Left Rail -->
        <aside class="hidden lg:block w-64 shrink-0 border-r border-white/10 min-h-[calc(100vh-56px)]">
          <div class="p-4">
            <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Flows</div>
            <nav class="space-y-1" id="flowNav">
              <button class="w-full text-left flex items-center gap-2 px-2.5 py-2 rounded-lg bg-neutral-900/60 border border-white/10 text-[13px] flow-link" data-section="overview">
                <span class="w-2 h-2 rounded-full bg-cyan-400"></span>
                Overview
              </button>
              <button class="w-full text-left flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px] flow-link" data-section="journeys">
                <span class="w-2 h-2 rounded-full bg-blue-400"></span>
                Journeys (Lanes)
              </button>
              <button class="w-full text-left flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px] flow-link" data-section="tree">
                <span class="w-2 h-2 rounded-full bg-fuchsia-400"></span>
                Tree
              </button>
            </nav>
          </div>
        </aside>

        <!-- Main -->
        <section class="flex-1 min-h-[calc(100vh-56px)]">
          <div class="sm:px-8 pt-6 pr-4 pb-6 pl-4">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent">HAUS Mobile Storyboard</h1>
                <p class="text-[13px] text-neutral-400">Exact screens embedded from ref.html</p>
              </div>
            </div>

            <div class="mt-5 space-y-6">
              <!-- Overview Grid -->
              <section id="overview" class="">
                <div class="rounded-2xl border border-white/10 bg-neutral-900/30 p-4">
                  <div class="flex items-center gap-2 mb-3">
                    <div class="size-2 rounded-full bg-white/30"></div>
                    <div class="text-[13px] text-neutral-300 tracking-tight">Overview</div>
                    <div class="text-[12px] text-neutral-500">All frames</div>
                  </div>
                  <div id="overviewGrid" class="grid grid-cols-2 sm:grid-cols-3 xl:grid-cols-4 gap-4"></div>
                </div>
              </section>

              <!-- Journeys (Lanes) -->
              <section id="journeys" class="hidden">
                <div id="lanes" class="space-y-6"></div>
              </section>

              <!-- Tree -->
              <section id="tree" class="hidden">
                <div class="grid grid-cols-1 lg:grid-cols-[1fr_420px] gap-4">
                  <div class="rounded-2xl border border-white/10 bg-neutral-900/30 p-3 overflow-auto min-h-[520px]">
                    <svg id="treeSvg" class="w-full h-full" viewBox="0 0 1200 1200" preserveAspectRatio="xMinYMin meet"></svg>
                  </div>
                  <div class="rounded-2xl border border-white/10 bg-neutral-900/40 p-3">
                    <div class="text-[13px] text-neutral-300 mb-2">Preview</div>
                    <div class="rounded-[2.5rem] overflow-hidden border border-white/10 w-[390px] h-[844px] mx-auto frame-live">
                      <iframe id="treePreview" src="ref.html#frame1" loading="lazy"></iframe>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </section>
      </div>
    </div>
  </main>

  <!-- Hidden source for cloning exact screens -->
  <iframe id="refSource" src="ref.html" class="hidden"></iframe>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const sections = {
        overview: document.getElementById('overview'),
        journeys: document.getElementById('journeys'),
        tree: document.getElementById('tree')
      };
      const navLinks = document.querySelectorAll('.flow-link');
      const setActive = (key) => {
        Object.entries(sections).forEach(([k, el]) => el.classList.toggle('hidden', k !== key));
        navLinks.forEach(btn => {
          const active = btn.dataset.section === key;
          btn.classList.toggle('bg-neutral-900/60', active);
          btn.classList.toggle('border', active);
          btn.classList.toggle('border-white/10', active);
          if (!active) btn.classList.remove('border','border-white/10');
        });
      };
      navLinks.forEach(btn => btn.addEventListener('click', () => setActive(btn.dataset.section)));
      setActive('overview');

      const lanes = [
        { key:'education', title:'Education', frames:['frame1','education-library'] },
        { key:'discovery', title:'Discovery', frames:['frame2','frame7','frame8','discovery-map'] },
        { key:'property', title:'Property', frames:['property-detail','property-analytics'] },
        { key:'docs', title:'Documentation', frames:['frame4','frame9','docs-scanner'] },
        { key:'finance', title:'Finance', frames:['frame3','frame5','frame13','frame12'] },
        { key:'application', title:'Application', frames:['frame10','application-offer'] },
        { key:'communication', title:'Communication', frames:['frame6','frame11','comm-live-chat'] },
        { key:'analytics', title:'Analytics', frames:['analytics-dashboard'] },
        { key:'community', title:'Community', frames:['community-forum'] },
        { key:'postpurchase', title:'Post‑Purchase', frames:['postpurchase-maintenance'] },
        { key:'onboarding', title:'Onboarding', frames:['onboarding-welcome','onboarding-register','onboarding-preferences'] },
        { key:'settings', title:'Settings', frames:['settings-notifications'] }
      ];

      // Titles map for labels
      const allFrames = Array.from(new Set(lanes.flatMap(l => l.frames)));
      const titles = new Map([
        ['frame1','Today on HAUS'],['frame2','Open to Offers'],['frame3','Affordability'],['frame4','Document Vault'],['frame5','Readiness'],['frame6','Messages'],['frame7','Filters'],['frame8','Request Intro'],['frame9','Upload Document'],['frame10','Review & Submit'],['frame11','Agent Pro'],['frame12','Finance Timeline'],['frame13','Lender Offers'],
        ['onboarding-welcome','Welcome'],['onboarding-register','Register'],['onboarding-preferences','AI Preferences'],
        ['discovery-map','Map'],['property-detail','Property Detail'],['property-analytics','DEEPHAUS Analytics'],['finance-borrowing','Borrowing Power'],['education-library','Academy Library'],['docs-scanner','Scanner'],['comm-live-chat','Live Chat'],['application-offer','Offer'],['analytics-dashboard','Analytics'],['community-forum','Buyer Forum'],['postpurchase-maintenance','Maintenance'],['settings-notifications','Notifications']
      ]);
      // Helper: clone screen node from hidden ref iframe
      const refFrame = document.getElementById('refSource');
      const grid = document.getElementById('overviewGrid');
      const lanesWrap = document.getElementById('lanes');
      const svg = document.getElementById('treeSvg');
      const NS = 'http://www.w3.org/2000/svg';
      const previewContainer = document.getElementById('treePreview');
      const colX = [40, 320]; // parent, child x positions
      const rowH = 90;
      const nodeW = 200, nodeH = 36, rx = 10;
      const labelStyle = 'font-size:12px;fill:#d4d4d8;font-family:Inter, system-ui, -apple-system;';
      const nodeStroke = '#3f3f46', nodeFill = '#0a0a0a';
      function cloneScreen(id) {
        const doc = refFrame.contentDocument;
        if (!doc) return null;
        const src = doc.getElementById(id);
        if (!src) return null;
        const clone = src.cloneNode(true);
        // Initialize lucide icons inside the clone
        try { if (window.lucide && typeof window.lucide.createIcons==='function') window.lucide.createIcons({ attrs: { 'stroke-width': 1.5 } }); } catch {}
        // Copy canvas drawings
        const srcCanvases = src.querySelectorAll('canvas');
        const dstCanvases = clone.querySelectorAll('canvas');
        srcCanvases.forEach((sc, i) => {
          const dc = dstCanvases[i];
          if (!dc) return;
          try {
            dc.width = sc.width; dc.height = sc.height;
            const ctx = dc.getContext('2d');
            ctx.drawImage(sc, 0, 0);
          } catch {}
        });
        return clone;
      }

      function buildOverview() {
        allFrames.forEach((id, i) => {
          const card = document.createElement('div');
          card.className = 'rounded-xl border border-white/10 bg-neutral-900/50 overflow-hidden hover:border-cyan-400/40 transition';
          const preview = document.createElement('div');
          preview.className = 'frame-tile';
          const clone = cloneScreen(id);
          if (clone) {
            clone.style.width = '390px'; clone.style.height = '844px';
            clone.style.transform = 'scale(0.26)'; clone.style.transformOrigin = 'top left'; clone.style.position = 'absolute'; clone.style.top = '10px'; clone.style.left = '10px';
            preview.appendChild(clone);
          }
          const meta = document.createElement('div');
          meta.className = 'flex items-center justify-between p-3';
          meta.innerHTML = `<div class=\"flex items-center gap-2\"><span class=\"text-[11px] text-neutral-500\">#${i+1}</span><span class=\"text-[12.5px] text-neutral-200\">${titles.get(id) || id}</span></div><div class=\"text-[11px] text-neutral-500\">390×844</div>`;
          card.append(preview, meta);
          grid.appendChild(card);
        });
      }

      function buildLanes() {
        lanes.forEach(l => {
          const sec = document.createElement('div');
          sec.innerHTML = `<div class=\"flex items-center gap-2 mb-2\"><div class=\"size-2 rounded-full bg-cyan-400\"></div><div class=\"text-[13px] text-neutral-300 tracking-tight\">${l.title}</div></div>`;
          const scroller = document.createElement('div');
          scroller.className = 'flex gap-4 overflow-x-auto pb-2';
          l.frames.forEach(id => {
            const tile = document.createElement('div');
            tile.className = 'frame-live rounded-[2.5rem] overflow-hidden border border-white/10 shrink-0';
            const clone = cloneScreen(id);
            if (clone) tile.appendChild(clone);
            scroller.appendChild(tile);
          });
          sec.appendChild(scroller);
          lanesWrap.appendChild(sec);
        });
      }

      const drawRect = (x,y,w,h,txt,id) => {
        const g = document.createElementNS(NS,'g');
        g.setAttribute('class','cursor-pointer');
        const rect = document.createElementNS(NS,'rect');
        rect.setAttribute('x',x); rect.setAttribute('y',y); rect.setAttribute('width',w); rect.setAttribute('height',h);
        rect.setAttribute('rx',rx); rect.setAttribute('fill',nodeFill); rect.setAttribute('stroke',nodeStroke); rect.setAttribute('stroke-width','1');
        const text = document.createElementNS(NS,'text');
        text.setAttribute('x', x + 12); text.setAttribute('y', y + h/2 + 4); text.setAttribute('style', labelStyle);
        text.textContent = txt;
        g.appendChild(rect); g.appendChild(text);
        if (id) {
          g.addEventListener('click', () => {
            previewContainer.innerHTML = '';
            const n = cloneScreen(id);
            if (n) previewContainer.appendChild(n);
          });
        }
        svg.appendChild(g);
        return {x,y,w,h};
      };

      let y = 30;
      function buildTree() {
        // Clear svg
        while (svg.firstChild) svg.removeChild(svg.firstChild);
        let y = 30;
        lanes.forEach(l => {
          // parent node
          drawRect(colX[0], y, nodeW, nodeH, l.title);
          // children
          const children = l.frames;
          let cy = y;
          children.forEach((id, idx) => {
            const title = titles.get(id) || id;
            drawRect(colX[1], cy, nodeW, nodeH, title, id);
            // connector
            const line = document.createElementNS(NS,'line');
            line.setAttribute('x1', colX[0] + nodeW);
            line.setAttribute('y1', y + nodeH/2);
            line.setAttribute('x2', colX[1]);
            line.setAttribute('y2', cy + nodeH/2);
            line.setAttribute('stroke', '#52525b');
            line.setAttribute('stroke-width', '1');
            svg.insertBefore(line, svg.firstChild);
            cy += rowH;
          });
          y += Math.max(children.length,1) * rowH + 20;
        });
      }

      // Wait for ref source to load, then build views with inline clones
      refFrame.addEventListener('load', () => {
        buildOverview();
        buildLanes();
        buildTree();
        // Set initial tree preview
        const first = cloneScreen(allFrames[0]);
        if (first) previewContainer.appendChild(first);
      }, { once: true });
    });
  </script>
</body></html>
