<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="color-scheme" content="light dark" />
    <title>HAUS Mobile App — Master Flow Document</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/geist@1.3.0/dist/geist.min.css" />
    <script defer src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/lucide@0.468.0/dist/umd/lucide.js"></script>
  </head>
  <body class="bg-neutral-50 text-neutral-900 dark:bg-neutral-950 dark:text-white antialiased" style="font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', Segoe UI, Roboto, Helvetica, Arial, system-ui, sans-serif;">
    <div class="fixed inset-0 -z-10">
      <div class="absolute inset-0 bg-gradient-to-b from-white to-neutral-100 dark:hidden"></div>
      <div class="absolute inset-0 opacity-40 dark:hidden" style="background: radial-gradient(800px 500px at 15% 10%, rgba(14,165,233,.10), transparent), radial-gradient(700px 500px at 85% 90%, rgba(14,165,233,.08), transparent);"></div>
      <div class="absolute inset-0 hidden dark:block bg-gradient-to-br from-neutral-950 via-neutral-900 to-neutral-950"></div>
      <div class="absolute inset-0 hidden dark:block opacity-30" style="background: radial-gradient(1000px 600px at 20% 10%, rgba(99,102,241,.20), transparent), radial-gradient(900px 500px at 80% 90%, rgba(14,165,233,.18), transparent);"></div>
    </div>

    <div class="h-screen flex overflow-hidden">
      <!-- Sidebar -->
      <aside class="w-[260px] shrink-0 border-r border-black/5 dark:border-white/10 bg-white/70 dark:bg-white/5 backdrop-blur overflow-y-auto">
        <div class="p-4 border-b border-black/5 dark:border-white/10">
          <div class="flex items-center gap-2">
            <div class="w-8 h-8 rounded-xl bg-sky-500/15 border border-sky-400/25 flex items-center justify-center text-sky-300">
              <i data-lucide="home" class="w-4 h-4"></i>
            </div>
            <div>
              <h1 class="text-base font-semibold leading-tight">HAUS Flows</h1>
              <div class="text-[11px] text-neutral-500 dark:text-neutral-400">Master Screenflow Document</div>
            </div>
          </div>
          <div class="mt-3 flex items-center gap-2">
            <button id="toggleTheme" class="text-[12px] px-2 py-1 rounded-lg bg-black/5 dark:bg-white/10 border border-black/10 dark:border-white/10 hover:bg-black/10 dark:hover:bg-white/15 transition">Toggle Theme</button>
            <a href="ref.html" class="text-[12px] px-2 py-1 rounded-lg bg-black/5 dark:bg-white/10 border border-black/10 dark:border-white/10 hover:bg-black/10 dark:hover:bg-white/15 transition">Prototype</a>
          </div>
        </div>
        <nav class="p-2 text-sm">
          <div class="px-2 py-1 text-[11px] uppercase tracking-wide text-neutral-500 dark:text-neutral-400">Journeys</div>
          <ul id="navList" class="space-y-1">
            <li><button data-target="overview" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Overview</button></li>
            <li><button data-target="onboarding" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Onboarding</button></li>
            <li><button data-target="discovery" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Discovery</button></li>
            <li><button data-target="property" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Property</button></li>
            <li><button data-target="finance" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Finance</button></li>
            <li><button data-target="education" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Education</button></li>
            <li><button data-target="docs" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Docs</button></li>
            <li><button data-target="communication" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Communication</button></li>
            <li><button data-target="application" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Application</button></li>
            <li><button data-target="analytics" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Analytics</button></li>
            <li><button data-target="community" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Community</button></li>
            <li><button data-target="postpurchase" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Post‑Purchase</button></li>
            <li><button data-target="settings" class="nav-btn w-full text-left px-3 py-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/10">Settings</button></li>
          </ul>
        </nav>
      </aside>

      <!-- Main -->
      <main class="flex-1 overflow-y-auto">
        <div class="max-w-7xl mx-auto p-4 sm:p-6">
          <!-- Section Template: Overview -->
          <section id="section-overview" class="flow-section">
            <h2 class="text-xl font-semibold tracking-tight mb-3">Overview</h2>
            <p class="text-sm text-neutral-600 dark:text-neutral-300 mb-4">High‑level map of key journeys with representative screens. Use the sidebar to jump between flows; tabs switch screen states.</p>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <!-- Reuse existing frames by linking -->
              <a href="ref.html#frame1" class="block w-80 shrink-0 rounded-2xl border border-black/10 dark:border-white/10 bg-white/70 dark:bg-white/5 p-3 hover:bg-black/[0.03] dark:hover:bg-white/10 transition">
                <div class="text-sm font-medium mb-2 flex items-center gap-2"><i data-lucide="home" class="w-4 h-4 text-sky-400"></i>Today on HAUS</div>
                <div class="text-[12px] text-neutral-500 dark:text-neutral-400">Daily overview with Academy + WAU sparkline.</div>
              </a>
              <a href="ref.html#frame2" class="block w-80 shrink-0 rounded-2xl border border-black/10 dark:border-white/10 bg-white/70 dark:bg-white/5 p-3 hover:bg-black/[0.03] dark:hover:bg-white/10 transition">
                <div class="text-sm font-medium mb-2 flex items-center gap-2"><i data-lucide="search" class="w-4 h-4 text-purple-400"></i>Open to Offers</div>
                <div class="text-[12px] text-neutral-500 dark:text-neutral-400">Explore properties and jump to Filters.</div>
              </a>
              <a href="ref.html#frame4" class="block w-80 shrink-0 rounded-2xl border border-black/10 dark:border-white/10 bg-white/70 dark:bg-white/5 p-3 hover:bg-black/[0.03] dark:hover:bg-white/10 transition">
                <div class="text-sm font-medium mb-2 flex items-center gap-2"><i data-lucide="folder-lock" class="w-4 h-4 text-teal-400"></i>Document Vault</div>
                <div class="text-[12px] text-neutral-500 dark:text-neutral-400">Uploads, verification, checklist.</div>
              </a>
              <a href="ref.html#frame13" class="block w-80 shrink-0 rounded-2xl border border-black/10 dark:border-white/10 bg-white/70 dark:bg-white/5 p-3 hover:bg-black/[0.03] dark:hover:bg-white/10 transition">
                <div class="text-sm font-medium mb-2 flex items-center gap-2"><i data-lucide="scale" class="w-4 h-4 text-amber-400"></i>Lender Offers</div>
                <div class="text-[12px] text-neutral-500 dark:text-neutral-400">Compare and select offers.</div>
              </a>
            </div>
          </section>

          <!-- SECTION: Onboarding -->
          <section id="section-onboarding" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-xl font-semibold tracking-tight">Onboarding</h2>
            </div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="onboarding-welcome"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="onboarding-register"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="onboarding-preferences"></div></div>
            </div>
          </section>

          <!-- SECTION: Discovery -->
          <section id="section-discovery" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-xl font-semibold tracking-tight">Discovery</h2>
            </div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame2"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame7"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame8"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="discovery-map"></div></div>
            </div>
          </section>

          <!-- SECTION: Property -->
          <section id="section-property" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-xl font-semibold tracking-tight">Property</h2>
            </div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="property-detail"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="property-analytics"></div></div>
            </div>
          </section>

          <!-- SECTION: Finance -->
          <section id="section-finance" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-xl font-semibold tracking-tight">Finance</h2>
            </div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame3"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame5"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame13"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame12"></div></div>
            </div>
          </section>

          <!-- SECTION: Docs (short) -->
          <section id="section-docs" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3">
              <h2 class="text-xl font-semibold tracking-tight">Documentation</h2>
            </div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame4"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame9"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="docs-scanner"></div></div>
            </div>
          </section>

          <!-- Additional sections (Communication, Application, Analytics, Community, Post‑Purchase, Education, Settings) scaffold -->
          <section id="section-communication" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Communication</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame6"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame11"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="comm-live-chat"></div></div>
            </div>
          </section>

          <section id="section-application" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Application</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame10"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="application-offer"></div></div>
            </div>
          </section>

          <section id="section-analytics" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Analytics</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="analytics-dashboard"></div></div>
            </div>
          </section>

          <section id="section-community" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Community</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="community-forum"></div></div>
            </div>
          </section>

          <section id="section-postpurchase" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Post‑Purchase</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="postpurchase-maintenance"></div></div>
            </div>
          </section>

          <section id="section-education" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Education</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="frame1"></div></div>
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="education-library"></div></div>
            </div>
          </section>

          <section id="section-settings" class="flow-section hidden">
            <div class="flex items-center justify-between mb-3"><h2 class="text-xl font-semibold">Settings</h2></div>
            <div class="flex gap-4 overflow-x-auto pb-2">
              <div class="panel block"><div class="w-80 h-[640px] rounded-[2.5rem] overflow-hidden border border-black/10 dark:border-white/10 frame-placeholder" data-frameid="settings-notifications"></div></div>
            </div>
          </section>
        </div>
      </main>
    </div>

    <!-- Hidden source to clone exact screens from ref.html -->
    <iframe id="refSource" src="ref.html" class="hidden"></iframe>

    <script>
      const onReady = (fn) => document.readyState !== 'loading' ? fn() : document.addEventListener('DOMContentLoaded', fn);
      onReady(() => {
        if (window.lucide && typeof window.lucide.createIcons === 'function') {
          window.lucide.createIcons({ attrs: { 'stroke-width': 1.5 } });
        }

        const body = document.body;
        document.getElementById('toggleTheme')?.addEventListener('click', () => body.classList.toggle('dark'));

        // Sidebar navigation
        const sections = Array.from(document.querySelectorAll('.flow-section'));
        const showSection = (key) => {
          sections.forEach(s => s.classList.add('hidden'));
          const el = document.getElementById(`section-${key}`);
          if (el) el.classList.remove('hidden');
        };
        document.querySelectorAll('.nav-btn').forEach(btn => {
          btn.addEventListener('click', () => {
            document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('bg-black/10','dark:bg-white/15'));
            btn.classList.add('bg-black/10','dark:bg-white/15');
            showSection(btn.dataset.target);
          });
        });
        showSection('overview');

        // After ref loads, replace placeholders with exact cloned screens
        const refFrame = document.getElementById('refSource');
        function cloneScreen(id) {
          const doc = refFrame.contentDocument;
          if (!doc) return null;
          const src = doc.getElementById(id);
          if (!src) return null;
          const clone = src.cloneNode(true);
          const srcCanvases = src.querySelectorAll('canvas');
          const dstCanvases = clone.querySelectorAll('canvas');
          srcCanvases.forEach((sc, i) => {
            const dc = dstCanvases[i];
            if (!dc) return;
            try { dc.width = sc.width; dc.height = sc.height; dc.getContext('2d').drawImage(sc,0,0); } catch {}
          });
          return clone;
        }

        refFrame.addEventListener('load', () => {
          document.querySelectorAll('.frame-placeholder').forEach(holder => {
            const id = holder.getAttribute('data-frameid');
            const node = cloneScreen(id);
            if (node) holder.appendChild(node);
          });
        }, { once: true });

        // No tabs now; sections show a horizontal lane of frames
      });
    </script>
  </body>
  </html>
