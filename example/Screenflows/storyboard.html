<html lang="en"><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>ALIAS — Storyboard</title>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&amp;family=Manrope:wght@400;500;600&amp;display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>
<style>
@keyframes neonPulse {
0%, 100% {
box-shadow:
inset 0 0 0 1px rgba(255,255,255,0.1),
inset 0 2px 4px rgba(0,0,0,0.3),
inset 0 0 15px rgba(6,182,212,0.3),
inset 0 0 25px rgba(59,130,246,0.2);
}
50% {
box-shadow:
inset 0 0 0 1px rgba(255,255,255,0.15),
inset 0 2px 4px rgba(0,0,0,0.3),
inset 0 0 25px rgba(6,182,212,0.5),
inset 0 0 40px rgba(59,130,246,0.3),
inset 0 0 60px rgba(6,182,212,0.2);
}
}
/* Outer edge glow off */
.neon-border { animation: none !important; box-shadow: none !important; }
@keyframes entranceSlideUp {
0% { opacity: 0; transform: translateY(80px) scale(0.95); }
100% { opacity: 1; transform: translateY(0) scale(1); }
}
.entrance-animation-1 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.2s; opacity: 0; }
.entrance-animation-2 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.4s; opacity: 0; }
.entrance-animation-3 { animation: entranceSlideUp 0.8s ease-out forwards; animation-delay: 0.6s; opacity: 0; }
</style></head>
  <body class="min-h-screen bg-neutral-950 text-neutral-100 antialiased" style="font-family: Inter, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, Apple Color Emoji, Segoe UI Emoji;">
    <!-- Backdrop -->
    <div class="pointer-events-none fixed inset-0 -z-10">
      <div class="absolute -top-32 left-1/2 -translate-x-1/2 w-[1200px] h-[1200px] bg-gradient-to-br from-cyan-400/20 via-blue-500/10 to-cyan-500/20 blur-3xl rounded-full"></div>
    </div>

    <!-- Top Nav -->
    <header class="sticky top-0 z-40 backdrop-blur supports-[backdrop-filter]:bg-neutral-950/60 bg-neutral-950/80 border-b border-white/10">
      <div class="max-w-[1600px] mx-auto px-4 sm:px-6">
        <div class="h-14 flex items-center justify-between">
          <div class="flex items-center gap-3">
            <div class="size-8 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-300/60 shadow-[0_0_24px_rgba(34,211,238,0.35)] flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="lucide lucide-sparkles w-4.5 h-4.5"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
            </div>
            <div class="text-[15px] tracking-tight">ALIAS</div>
            <div class="text-neutral-600">/</div>
            <div class="text-[14px] text-neutral-300">Mobile v1</div>
            <div class="hidden md:flex items-center gap-2 ml-4 text-[12px] text-neutral-400">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="map" class="lucide lucide-map w-4 h-4"><path d="M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z"></path><path d="M15 5.764v15"></path><path d="M9 3.236v15"></path></svg>
              <span class="">Storyboard</span>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <button class="hidden sm:flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="zoom-in" class="lucide lucide-zoom-in w-4.5 h-4.5"><circle cx="11" cy="11" r="8"></circle><line x1="21" x2="16.65" y1="21" y2="16.65"></line><line x1="11" x2="11" y1="8" y2="14"></line><line x1="8" x2="14" y1="11" y2="11"></line></svg>
              Zoom
            </button>
            <button class="hidden md:flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="share" class="lucide lucide-share w-4.5 h-4.5"><path d="M12 2v13"></path><path d="m16 6-4-4-4 4"></path><path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path></svg>
              Export
            </button>
            <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="more-horizontal" class="lucide lucide-more-horizontal w-4.5 h-4.5 text-neutral-300"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <main class="w-full">
      <div class="max-w-[1800px] mx-auto">
        <div class="flex">
          <!-- Left Rail -->
          <aside class="hidden lg:block w-64 shrink-0 border-r border-white/10 min-h-[calc(100vh-56px)]">
            <div class="p-4">
              <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Flows</div>
              <nav class="space-y-1" id="flowNav">
                <!-- Overview (new) -->
                <button class="w-full text-left flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px] flow-link" data-section="overview">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="images" class="w-4.5 h-4.5 text-neutral-300"><path d="M19 7l-7 10-3-3-4 6h16"></path><path d="M5 3a2 2 0 0 0-2 2v12"></path><rect width="14" height="14" x="7" y="5" rx="2"></rect></svg>
                  Overview
                </button>
                <!-- Existing Core flow toggles Core section -->
                <button class="w-full text-left flex items-center gap-2 px-2.5 py-2 rounded-lg bg-neutral-900/60 border border-white/10 text-[13px] flow-link" data-section="core">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="workflow" class="w-4.5 h-4.5 text-cyan-300"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg>
                  Core: Tailor → Toolbox → Chat
                </button>
                <!-- Keep the rest unchanged (non-interactive) -->
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="log-in" class="w-4.5 h-4.5 text-neutral-400"><path d="m10 17 5-5-5-5"></path><path d="M15 12H3"></path><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path></svg>
                  Onboarding
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="house" class="w-4.5 h-4.5 text-neutral-400"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>
                  Home
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="audio-lines" class="w-4.5 h-4.5 text-neutral-400"><path d="M2 10v3"></path><path d="M6 6v11"></path><path d="M10 3v18"></path><path d="M14 8v7"></path><path d="M18 5v13"></path><path d="M22 10v3"></path></svg>
                  Voice Picker
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="target" class="w-4.5 h-4.5 text-neutral-400"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg>
                  Goal Setup
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bell" class="w-4.5 h-4.5 text-neutral-400"><path d="M10.268 21a2 2 0 0 0 3.464 0"></path><path d="M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326"></path></svg>
                  Notifications
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="settings" class="w-4.5 h-4.5 text-neutral-400"><path d="M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915"></path><circle cx="12" cy="12" r="3"></circle></svg>
                  Settings
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="credit-card" class="w-4.5 h-4.5 text-neutral-400"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg>
                  Billing
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="history" class="w-4.5 h-4.5 text-neutral-400"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M12 7v5l4 2"></path></svg>
                  History
                </a>
                <a class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 border border-transparent hover:border-white/10 text-[13px]" href="#">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="shield-alert" class="w-4.5 h-4.5 text-neutral-400"><path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg>
                  Error States
                </a>
              </nav>
            </div>
            <div class="px-4">
              <div class="h-px w-full bg-gradient-to-r from-transparent via-white/10 to-transparent my-3"></div>
              <div class="text-[12px] uppercase tracking-wider text-neutral-500 mb-2">Artifacts</div>
              <nav class="space-y-1">
                <div class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 text-[13px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="library" class="w-4.5 h-4.5 text-neutral-400"><path d="m16 6 4 14"></path><path d="M12 6v14"></path><path d="M8 8v12"></path><path d="M4 4v16"></path></svg>
                  Components
                </div>
                <div class="flex items-center gap-2 px-2.5 py-2 rounded-lg hover:bg-neutral-900/40 text-[13px]">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="list-tree" class="w-4.5 h-4.5 text-neutral-400"><path d="M21 12h-8"></path><path d="M21 6H8"></path><path d="M21 18h-8"></path><path d="M3 6v4c0 1.1.9 2 2 2h3"></path><path d="M3 10v6c0 1.1.9 2 2 2h3"></path></svg>
                  User Journeys
                </div>
              </nav>
            </div>
          </aside>

          <!-- Main + RHS Detail -->
          <section class="flex-1 min-h-[calc(100vh-56px)]">
            <div class="sm:px-8 pt-6 pr-4 pb-6 pl-4">
              <div class="flex items-center justify-between">
                <div class="">
                  <h1 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent">Storyboard</h1>
                  <p class="text-[13px] text-neutral-400">High-level flow with in-progress frames</p>
                </div>
                <div class="hidden sm:flex items-center gap-2">
                  <div class="h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="grid-2x2-check" class="w-4.5 h-4.5 text-neutral-300"><path d="M12 3v17a1 1 0 0 1-1 1H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v6a1 1 0 0 1-1 1H3"></path><path d="m16 19 2 2 4-4"></path></svg>
                    Snap to grid
                  </div>

                  <!-- Device selector -->
                  <div class="h-9 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] flex items-center p-1 gap-1">
                    <button class="h-7 px-2.5 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 text-white border border-cyan-300/60 flex items-center gap-1.5">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="smartphone" class="w-4 h-4 text-white"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect><path d="M12 18h.01"></path></svg>
                      <span class="hidden xl:block">Mobile</span>
                      <span class="hidden lg:block text-white/80">390×844</span>
                    </button>
                    <button class="h-7 px-2.5 rounded-lg border border-white/10 text-neutral-300 hover:border-cyan-400/40 flex items-center gap-1.5">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="tablet" class="w-4 h-4 text-neutral-300"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><line x1="12" x2="12.01" y1="18" y2="18"></line></svg>
                      <span class="hidden xl:block">Tablet</span>
                      <span class="hidden lg:block text-neutral-400">768×1024</span>
                    </button>
                    <button class="h-7 px-2.5 rounded-lg border border-white/10 text-neutral-300 hover:border-cyan-400/40 flex items-center gap-1.5">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="monitor" class="w-4 h-4 text-neutral-300"><rect width="20" height="14" x="2" y="3" rx="2"></rect><line x1="8" x2="16" y1="21" y2="21"></line><line x1="12" x2="12" y1="17" y2="21"></line></svg>
                      <span class="hidden xl:block">Desktop</span>
                      <span class="hidden lg:block text-neutral-400">1280×800</span>
                    </button>
                  </div>

                  <div class="h-9 text-[13px] flex gap-2 bg-neutral-900/60 border-white/10 border rounded-xl pr-3 pl-3 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="ruler" class="w-4.5 h-4.5 text-neutral-300"><path d="M21.3 15.3a2.4 2.4 0 0 1 0 3.4l-2.6 2.6a2.4 2.4 0 0 1-3.4 0L2.7 8.7a2.41 2.41 0 0 1 0-3.4l2.6-2.6a2.41 2.41 0 0 1 3.4 0Z"></path><path d="m14.5 12.5 2-2"></path><path d="m11.5 9.5 2-2"></path><path d="m8.5 6.5 2-2"></path><path d="m17.5 15.5 2-2"></path></svg>
                    390 × 844
                  </div>
                </div>
              </div>

              <!-- Sections: Overview + Core -->
              <div class="mt-5 space-y-6">
                <!-- Overview -->
                <section id="overview" class="">
                  <div class="rounded-2xl border border-white/10 bg-neutral-900/30 p-4">
                    <div class="flex items-center gap-2 mb-3">
                      <div class="size-2 rounded-full bg-white/30"></div>
                      <div class="text-[13px] text-neutral-300 tracking-tight">Overview</div>
                      <div class="text-[12px] text-neutral-500">All frames at a glance</div>
                    </div>
                    <div id="autoGallery" class="grid grid-cols-2 sm:grid-cols-3 xl:grid-cols-4 gap-4"></div>
                  </div>
                </section>

                <!-- Core Flow (existing canvas wrapped) -->
                <section id="core" class="hidden">
                  <!-- Canvas backdrop -->
                  <div class="rounded-2xl border border-white/10 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.04)_1px,transparent_1px),linear-gradient(to_right,rgba(255,255,255,0.04)_1px,transparent_1px)] bg-[size:32px_32px] p-4 sm:p-6">
                    <!-- Lane: Core Flow -->
                    <div class="">
                      <div class="flex items-center gap-2 mb-4">
                        <div class="size-2 rounded-full bg-cyan-400"></div>
                        <div class="text-[13px] text-neutral-300 tracking-tight">Core Flow</div>
                        <div class="text-[12px] text-neutral-500">Tailor → Toolbox → Conversation</div>
                      </div>

                      <div class="relative">
                        <div class="flex gap-6 overflow-x-auto pb-2 items-start">
                          <!-- Screen 1 — Tailor Your Agent -->
                          <div class="w-[390px] max-w-full shrink-0 mobile-screen" id="screen-tailor" data-title="Tailor Your Agent">
                            <div class="w-[390px] h-[844px] rounded-[2.9rem] bg-black overflow-hidden relative neon-border shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                              <!-- Status bar -->
                              <div class="flex items-center justify-between px-8 pt-4 pb-2">
                                <div class="text-white text-sm">9:41</div>
                                <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                                <div class="flex items-center gap-1 text-white">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                  <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                    <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                    <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                  </div>
                                </div>
                              </div>

                              <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                                <!-- Header -->
                                <div class="text-center mt-2">
                                  <h2 class="text-[22px] md:text-[24px] tracking-tight bg-gradient-to-r from-cyan-300 via-blue-300 to-cyan-200 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(6,182,212,0.45)]">Tailor Your Agent</h2>
                                  <p class="text-[13px] text-neutral-400 mt-2">Customize appearance, voice, and capabilities</p>
                                </div>

                                <!-- Avatar Orb -->
                                <div class="relative mx-auto mt-7">
                                  <div class="absolute inset-0 -z-10 blur-2xl bg-gradient-to-br from-cyan-500/35 via-blue-500/25 to-cyan-400/30 rounded-full w-64 h-64"></div>
                                  <div class="relative w-64 h-64 rounded-full overflow-hidden border border-cyan-400/30 bg-gradient-to-br from-neutral-900 via-neutral-950 to-black shadow-[0_0_40px_4px_rgba(34,211,238,0.25)]">
                                    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,211,238,0.35),transparent_60%),radial-gradient(circle_at_70%_70%,rgba(59,130,246,0.35),transparent_55%)]"></div>
                                    <div class="absolute inset-0 mix-blend-screen opacity-70">
                                      <video autoplay="" muted="" loop="" playsinline="" class="w-full h-full object-cover" style="filter: brightness(1.05) contrast(1.1) saturate(1.2);">
                                        <source src="https://cdn.midjourney.com/video/5029e61e-010a-4f9f-8daf-ef0a0ff34af6/3.mp4" type="video/mp4">
                                      </video>
                                    </div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                      <div class="p-4 rounded-full bg-gradient-to-br from-cyan-500 to-blue-600 shadow-[0_0_24px_8px_rgba(34,211,238,0.35)]">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="w-8 h-8"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Customization quick picks -->
                                  <div class="flex items-center justify-center gap-3 mt-4">
                                    <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="w-5 h-5 text-cyan-300"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                    </button>
                                    <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="headphones" class="w-5 h-5 text-blue-300"><path d="M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3"></path></svg>
                                    </button>
                                    <button class="p-2.5 rounded-full bg-neutral-900/60 backdrop-blur-md border border-white/10 hover:border-cyan-400/40 transition">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wand-2" class="w-5 h-5 text-cyan-200"><path d="m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72"></path><path d="m14 7 3 3"></path><path d="M5 6v4"></path><path d="M19 14v4"></path><path d="M10 2v2"></path><path d="M7 8H3"></path><path d="M21 16h-4"></path><path d="M11 3H9"></path></svg>
                                    </button>
                                  </div>
                                </div>

                                <!-- Stepper -->
                                <div class="mt-7">
                                  <div class="flex items-center justify-between">
                                    <div class="flex-1 flex items-center">
                                      <div class="size-7 rounded-full bg-gradient-to-br from-cyan-500 to-blue-500 text-white text-[12px] flex items-center justify-center shadow-[0_0_16px_rgba(34,211,238,0.5)]">1</div>
                                      <div class="h-0.5 flex-1 mx-2 bg-gradient-to-r from-cyan-500/50 to-blue-500/40"></div>
                                      <div class="size-7 rounded-full bg-neutral-900/70 border border-white/10 text-[12px] text-neutral-300 flex items-center justify-center">2</div>
                                      <div class="h-0.5 flex-1 mx-2 bg-white/10"></div>
                                      <div class="size-7 rounded-full bg-neutral-900/70 border border-white/10 text-[12px] text-neutral-300 flex items-center justify-center">3</div>
                                    </div>
                                  </div>
                                  <div class="mt-2 flex items-center justify-between text-[11px] text-neutral-400 px-1">
                                    <span>Choose Skills</span>
                                    <span>Pick Voice</span>
                                    <span>Set Goals</span>
                                  </div>
                                </div>

                                <!-- Skills -->
                                <div class="mt-5">
                                  <div class="text-[13px] text-neutral-300 mb-2">Example skills</div>
                                  <div class="flex flex-wrap gap-2.5">
                                    <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition shadow-[0_0_12px_rgba(34,211,238,0.25)]">
                                      <div class="flex items-center gap-1.5">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="search" class="w-3.5 h-3.5"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg>
                                        <span>Research</span>
                                      </div>
                                    </button>
                                    <button class="px-3.5 py-2 rounded-full text:[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition">
                                      <div class="flex items-center gap-1.5">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="workflow" class="w-3.5 h-3.5"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg>
                                        <span>Project Management</span>
                                      </div>
                                    </button>
                                    <button class="px-3.5 py-2 rounded-full text-[12px] bg-gradient-to-br from-cyan-500/15 to-blue-500/10 border border-cyan-400/30 text-cyan-200 backdrop-blur-md hover:border-cyan-300/60 transition">
                                      <div class="flex items-center gap-1.5">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="headset" class="w-3.5 h-3.5"><path d="M3 11h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-5Zm0 0a9 9 0 1 1 18 0m0 0v5a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3Z"></path><path d="M21 16v2a4 4 0 0 1-4 4h-5"></path></svg>
                                        <span>Customer Support</span>
                                      </div>
                                    </button>
                                  </div>
                                </div>

                                <!-- CTA -->
                                <div class="mt-auto">
                                  <button class="w-full h-14 rounded-2xl bg-gradient-to-r from-cyan-500 to-blue-500 text-[15px] tracking-tight text-white border border-cyan-300/60 shadow-[0_10px_40px_-8px_rgba(34,211,238,0.55)] hover:shadow-[0_10px_50px_-6px_rgba(34,211,238,0.75)] transition">
                                    Deploy My Agent
                                  </button>
                                </div>

                                <!-- Home indicator -->
                                <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                              </div>
                            </div>
                            <div class="text-[12px] text-neutral-400 mt-2 text-center">1 · Tailor Your Agent</div>
                          </div>

                          <!-- Connector -->
                          <div class="shrink-0 mt-[180px]">
                            <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="arrow-right" class="w-5 h-5 text-neutral-300"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                            </div>
                          </div>

                          <!-- Screen 2 — ALIAS Toolbox -->
                          <div class="w-[390px] max-w-full shrink-0 mobile-screen" id="screen-toolbox" data-title="ALIAS Toolbox">
                            <div class="w-[390px] h-[844px] rounded-[2.9rem] bg-black overflow-hidden relative neon-border shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                              <!-- Status bar -->
                              <div class="flex items-center justify-between px-8 pt-4 pb-2">
                                <div class="text-white text-sm">9:41</div>
                                <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                                <div class="flex items-center gap-1 text-white">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                  <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                    <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                    <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                  </div>
                                </div>
                              </div>

                              <div class="relative flex flex-col h-full pt-6 pr-6 pb-6 pl-6">
                                <!-- Header -->
                                <div class="flex items-center justify-between mb-4">
                                  <h2 class="text-[22px] tracking-tight bg-gradient-to-r from-cyan-300 via-cyan-200 to-blue-300 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(6,182,212,0.45)]">ALIAS Toolbox</h2>
                                  <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sliders" class="w-4.5 h-4.5 text-neutral-300"><line x1="4" x2="4" y1="21" y2="14"></line><line x1="4" x2="4" y1="10" y2="3"></line><line x1="12" x2="12" y1="21" y2="12"></line><line x1="12" x2="12" y1="8" y2="3"></line><line x1="20" x2="20" y1="21" y2="16"></line><line x1="20" x2="20" y1="12" y2="3"></line><line x1="2" x2="6" y1="14" y2="14"></line><line x1="10" x2="14" y1="8" y2="8"></line><line x1="18" x2="22" y1="16" y2="16"></line></svg>
                                  </button>
                                </div>

                                <!-- Tools Grid -->
                                <div class="grid grid-cols-2 gap-3">
                                  <!-- Finance Tracker -->
                                  <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-green-400/40 transition relative overflow-hidden">
                                    <div class="absolute -inset-6 bg-gradient-to-br from-green-400/10 via-transparent to-transparent pointer-events-none"></div>
                                    <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-green-400/20 to-emerald-400/10 border border-green-300/30 shadow-[0_0_24px_rgba(74,222,128,0.35)]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wallet" class="w-5 h-5 text-green-300"><path d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1"></path><path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4"></path></svg>
                                    </div>
                                    <div class="mt-3">
                                      <div class="text-[14px] text-neutral-100 tracking-tight">Finance Tracker</div>
                                      <div class="text-[12px] text-neutral-400">Spending insights</div>
                                    </div>
                                  </div>

                                  <!-- Task Automator -->
                                  <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-cyan-400/40 transition relative overflow-hidden">
                                    <div class="absolute -inset-6 bg-gradient-to-br from-cyan-400/10 via-transparent to-transparent pointer-events-none"></div>
                                    <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-cyan-400/20 to-blue-400/10 border border-cyan-300/30 shadow-[0_0_24px_rgba(34,211,238,0.35)]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="bot" class="w-5 h-5 text-cyan-300"><path d="M12 8V4H8"></path><rect width="16" height="12" x="4" y="8" rx="2"></rect><path d="M2 14h2"></path><path d="M20 14h2"></path><path d="M15 13v2"></path><path d="M9 13v2"></path></svg>
                                    </div>
                                    <div class="mt-3">
                                      <div class="text-[14px] text-neutral-100 tracking-tight">Task Automator</div>
                                      <div class="text-[12px] text-neutral-400">Workflow scripts</div>
                                    </div>
                                  </div>

                                  <!-- Meeting Summarizer -->
                                  <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-blue-400/40 transition relative overflow-hidden">
                                    <div class="absolute -inset-6 bg-gradient-to-br from-blue-400/10 via-transparent to-transparent pointer-events-none"></div>
                                    <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-blue-400/20 to-cyan-400/10 border border-blue-300/30 shadow-[0_0_24px_rgba(59,130,246,0.35)]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="file-text" class="w-5 h-5 text-blue-300"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>
                                    </div>
                                    <div class="mt-3">
                                      <div class="text-[14px] text-neutral-100 tracking-tight">Meeting Summarizer</div>
                                      <div class="text-[12px] text-neutral-400">Notes &amp; action items</div>
                                    </div>
                                  </div>

                                  <!-- Idea Generator -->
                                  <div class="p-4 rounded-2xl bg-neutral-900/40 backdrop-blur-xl border border-white/10 hover:border-fuchsia-400/40 transition relative overflow-hidden">
                                    <div class="absolute -inset-6 bg-gradient-to-br from-fuchsia-400/10 via-transparent to-transparent pointer-events-none"></div>
                                    <div class="size-10 rounded-xl flex items-center justify-center bg-gradient-to-br from-fuchsia-400/20 to-purple-400/10 border border-fuchsia-300/30 shadow-[0_0_24px_rgba(232,121,249,0.35)]">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="sparkles" class="w-5 h-5 text-fuchsia-300"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg>
                                    </div>
                                    <div class="mt-3">
                                      <div class="text-[14px] text-neutral-100 tracking-tight">Idea Generator</div>
                                      <div class="text-[12px] text-neutral-400">Brainstorming prompts</div>
                                    </div>
                                  </div>
                                </div>

                                <!-- CTA -->
                                <div class="mt-5">
                                  <button aria-label="Open conversation" class="w-full h-12 rounded-2xl bg-gradient-to-r from-cyan-500 to-blue-500 text-[14px] tracking-tight text-white border border-cyan-300/60 shadow-[0_10px_34px_-10px_rgba(34,211,238,0.6)] hover:shadow-[0_12px_46px_-8px_rgba(34,211,238,0.8)] transition">
                                    Open Conversation
                                  </button>
                                </div>

                                <!-- Home indicator -->
                                <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                              </div>
                            </div>
                            <div class="text-[12px] text-neutral-400 mt-2 text-center">2 · Toolbox</div>
                          </div>

                          <!-- Connector -->
                          <div class="shrink-0 mt-[180px]">
                            <div class="size-10 rounded-full border border-white/10 bg-neutral-900/60 flex items-center justify-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="arrow-right" class="w-5 h-5 text-neutral-300"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
                            </div>
                          </div>

                          <!-- Screen 3 — Conversation -->
                          <div class="w-[390px] max-w-full shrink-0 mobile-screen" id="screen-chat" data-title="Conversation">
                            <div class="w-[390px] h-[844px] rounded-[2.9rem] bg-black overflow-hidden relative neon-border shadow-[0_20px_80px_-12px_rgba(6,182,212,0.4)]">
                              <!-- Status bar -->
                              <div class="flex items-center justify-between px-8 pt-4 pb-2">
                                <div class="text-white text-sm">9:41</div>
                                <div class="w-6 h-5 rounded-full bg-neutral-800/70"></div>
                                <div class="flex items-center gap-1 text-white">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="signal" class="w-4 h-4"><path d="M2 20h.01"></path><path d="M7 20v-4"></path><path d="M12 20v-8"></path><path d="M17 20V8"></path><path d="M22 4v16"></path></svg>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" data-lucide="wifi" class="w-4 h-4"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg>
                                  <div class="w-6 h-3 rounded-sm border border-white/60 relative">
                                    <div class="absolute inset-0.5 bg-white rounded-[1px]"></div>
                                    <div class="absolute -right-0.5 top-1 w-0.5 h-1 bg-white/60 rounded-r-sm"></div>
                                  </div>
                                </div>
                              </div>

                              <!-- Conversation body -->
                              <div class="relative h-full flex flex-col pt-3 px-4 pb-6">
                                <!-- Header -->
                                <div class="flex items-center justify-between px-2">
                                  <div class="flex items-center gap-3">
                                    <div class="relative">
                                      <div class="size-9 rounded-full border border-cyan-300/40 bg-gradient-to-br from-cyan-500/20 to-blue-500/10 shadow-[0_0_18px_rgba(34,211,238,0.35)]"></div>
                                      <div class="absolute -bottom-0.5 -right-0.5 size-3 rounded-full bg-emerald-400 border border-black"></div>
                                    </div>
                                    <div>
                                      <div class="text-[14px] tracking-tight text-neutral-100">Alias</div>
                                      <div class="text-[11px] text-neutral-400">Online</div>
                                    </div>
                                  </div>
                                  <div class="flex items-center gap-1.5">
                                    <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="Start voice call">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-neutral-300" data-lucide="phone">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2A19.8 19.8 0 0 1 3.11 5.18 2 2 0 0 1 5.06 3h3a2 2 0 0 1 2 1.72c.12.9.33 1.77.64 2.6a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6.31 6.31l1.27-1.27a2 2 0 0 1 2.11-.45c.83 .31 1.7 .52 2.6 .64A2 2 0 0 1 22 16.92z"></path>
                                      </svg>
                                    </button>
                                    <button class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="More options">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-neutral-300" data-lucide="more-horizontal">
                                        <circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle>
                                      </svg>
                                    </button>
                                  </div>
                                </div>

                                <!-- Messages -->
                                <div class="mt-4 space-y-3 flex-1 overflow-y-auto px-2">
                                  <!-- Agent message -->
                                  <div class="flex items-start gap-3">
                                    <div class="size-7 rounded-full border border-cyan-300/30 bg-gradient-to-br from-cyan-500/20 to-blue-500/10 shadow-[0_0_10px_rgba(34,211,238,0.35)] shrink-0"></div>
                                    <div class="max-w-[78%] rounded-2xl rounded-tl-sm border border-cyan-300/20 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 text-[13px] leading-relaxed text-neutral-100 p-3">
                                      Hey! I can help summarize meetings, automate tasks, or plan your week. What should we start with?
                                    </div>
                                  </div>

                                  <!-- User message -->
                                  <div class="flex items-start gap-3 justify-end">
                                    <div class="max-w-[78%] rounded-2xl rounded-tr-sm bg-neutral-800/70 border border-white/10 text-[13px] leading-relaxed text-neutral-100 p-3">
                                      Let’s start with a summary for today’s standup and action items.
                                    </div>
                                  </div>

                                  <!-- Agent thinking -->
                                  <div class="flex items-start gap-3">
                                    <div class="size-7 rounded-full border border-cyan-300/30 bg-gradient-to-br from-cyan-500/20 to-blue-500/10 shadow-[0_0_10px_rgba(34,211,238,0.35)] shrink-0"></div>
                                    <div class="max-w-[78%] rounded-2xl rounded-tl-sm border border-cyan-300/20 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 text-[13px] p-3">
                                      <div class="flex items-center gap-1.5 text-cyan-200/90">
                                        <span class="size-1.5 rounded-full bg-cyan-300 animate-bounce" style="animation-delay:0ms"></span>
                                        <span class="size-1.5 rounded-full bg-cyan-300 animate-bounce" style="animation-delay:120ms"></span>
                                        <span class="size-1.5 rounded-full bg-cyan-300 animate-bounce" style="animation-delay:240ms"></span>
                                        <span class="text-[12px] text-cyan-100/80 ml-2">Drafting summary...</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                <!-- Quick actions -->
                                <div class="mt-3 px-2">
                                  <div class="flex gap-2 overflow-x-auto pb-1">
                                    <button class="px-3 py-1.5 rounded-full text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-200 hover:border-cyan-400/40 transition whitespace-nowrap">Attach notes</button>
                                    <button class="px-3 py-1.5 rounded-full text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-200 hover:border-cyan-400/40 transition whitespace-nowrap"> calendar</button>
                                    <button class="px-3 py-1.5 rounded-full text-[12px] bg-neutral-900/60 border border-white/10 text-neutral-200 hover:border-cyan-400/40 transition whitespace-nowrap">Tag owners</button>
                                  </div>
                                </div>

                                <!-- Composer -->
                                <form class="mt-3 px-2" action="javascript:void(0)" aria-label="Message composer">
                                  <div class="flex items-end gap-2">
                                    <button type="button" aria-label="Add attachment" class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition">
                                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-neutral-300" data-lucide="plus">
                                        <line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line>
                                      </svg>
                                    </button>
                                    <div class="flex-1 relative">
                                      <div class="rounded-2xl bg-neutral-900/60 border border-white/10 focus-within:border-cyan-400/40 transition">
                                        <textarea id="composerInput" rows="1" placeholder="Type a message..." class="w-full bg-transparent outline-none resize-none text-[13px] text-neutral-100 placeholder:text-neutral-500 px-3.5 pr-24 py-3" aria-label="Message input"></textarea>
                                      </div>
                                      <div class="absolute right-1.5 bottom-1.5 flex items-center gap-1.5">
                                        <button type="button" aria-label="Voice input" class="size-9 rounded-full bg-neutral-900/60 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-neutral-300" data-lucide="audio-lines">
                                            <path d="M2 10v3"></path><path d="M6 6v11"></path><path d="M10 3v18"></path><path d="M14 8v7"></path><path d="M18 5v13"></path><path d="M22 10v3"></path>
                                          </svg>
                                        </button>
                                        <button type="submit" aria-label="Send message" class="size-9 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 text-white border border-cyan-300/60 flex items-center justify-center shadow-[0_8px_24px_-10px_rgba(34,211,238,0.7)] hover:shadow-[0_10px_32px_-10px_rgba(34,211,238,0.85)] transition">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5" data-lucide="arrow-right">
                                            <path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path>
                                          </svg>
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </form>

                                <!-- Home indicator -->
                                <div class="w-36 h-1 bg-white/30 rounded-full mx-auto mt-6"></div>
                              </div>
                            </div>
                            <div class="text-[12px] text-neutral-400 mt-2 text-center">3 · Conversation</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>

              <!-- Footer utility -->
              <div class="mt-8 rounded-2xl border border-white/10 bg-neutral-900/40 p-4 flex items-center justify-between">
                <div class="text-[12px] text-neutral-400">© 2025 ALIAS · Prototype storyboard</div>
                <div class="flex items-center gap-2">
                  <a id="downloadStoryboard" href="#" download="alias-storyboard.json" class="inline-flex items-center gap-2 h-9 px-3 rounded-xl bg-neutral-900/60 border border-white/10 text-[13px] hover:border-cyan-400/40 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="7 10 12 15 17 10"></polyline>
                      <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Download
                  </a>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </main>

    <!-- Scripts -->
    <script>
      // Section toggling (Overview/Core)
      document.addEventListener('DOMContentLoaded', () => {
        const links = Array.from(document.querySelectorAll('.flow-link'));
        const sections = {
          overview: document.getElementById('overview'),
          core: document.getElementById('core')
        };

        // Normalize active state to Overview on load to match visible content
        setActive('overview');

        links.forEach(btn => {
          btn.addEventListener('click', () => {
            const target = btn.getAttribute('data-section');
            setActive(target);
          });
        });

        function setActive(key) {
          Object.entries(sections).forEach(([k, el]) => {
            if (!el) return;
            if (k === key) {
              el.classList.remove('hidden');
            } else {
              el.classList.add('hidden');
            }
          });

          links.forEach(b => {
            const isActive = b.getAttribute('data-section') === key;
            if (isActive) {
              b.classList.add('bg-neutral-900/60');
              b.classList.remove('border-transparent');
              b.classList.add('border', 'border-white/10');
            } else {
              b.classList.remove('bg-neutral-900/60');
              b.classList.remove('border', 'border-white/10');
              b.classList.add('border-transparent');
            }
          });

          // When switching to Core, focus the first screen for accessibility
          if (key === 'core') {
            const first = document.querySelector('#core .mobile-screen');
            if (first) first.setAttribute('tabindex', '-1'), first.focus({preventScroll: true});
          }
        }

        // Auto-gallery generation from screens
        const gallery = document.getElementById('autoGallery');
        const screens = Array.from(document.querySelectorAll('.mobile-screen'));

        screens.forEach((screen, i) => {
          const title = screen.getAttribute('data-title') || `Frame ${i + 1}`;
          const id = screen.id || `frame-${i+1}`;
          const card = document.createElement('div');
          card.className = 'rounded-xl border border-white/10 bg-neutral-900/50 overflow-hidden hover:border-cyan-400/40 transition';

          // Preview wrapper
          const preview = document.createElement('div');
          preview.className = 'relative h-56 bg-neutral-950/60';

          const device = screen.firstElementChild.cloneNode(true);
          device.style.width = '390px';
          device.style.height = '844px';
          device.style.transform = 'scale(0.26)';
          device.style.transformOrigin = 'top left';
          device.style.position = 'absolute';
          device.style.top = '12px';
          device.style.left = '12px';
          device.classList.add('pointer-events-none'); // Prevent inner interactions

          // Controls overlay
          const controls = document.createElement('div');
          controls.className = 'absolute top-2 right-2 flex items-center gap-1.5';
          controls.innerHTML = `
            <button data-open="${id}" class="size-8 rounded-lg bg-neutral-900/80 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="Open in Core">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-neutral-200">
                <path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path>
              </svg>
            </button>
            <button data-download="${id}" class="size-8 rounded-lg bg-neutral-900/80 border border-white/10 flex items-center justify-center hover:border-cyan-400/40 transition" aria-label="Download frame HTML">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4.5 h-4.5 text-neutral-200">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
            </button>
          `;

          preview.append(device, controls);

          // Meta row
          const meta = document.createElement('div');
          meta.className = 'flex items-center justify-between p-3';
          meta.innerHTML = `
            <div class="flex items-center gap-2">
              <span class="text-[11px] text-neutral-500">#${i + 1}</span>
              <span class="text-[12.5px] text-neutral-200">${title}</span>
            </div>
            <div class="text-[11px] text-neutral-500">390×844</div>
          `;

          card.append(preview, meta);
          gallery.appendChild(card);
        });

        // Open in Core and scroll to screen
        gallery.addEventListener('click', (e) => {
          const openBtn = e.target.closest('button[data-open]');
          const dlBtn = e.target.closest('button[data-download]');
          if (openBtn) {
            const targetId = openBtn.getAttribute('data-open');
            const target = document.getElementById(targetId);
            if (target) {
              // Switch to core
              const coreBtn = document.querySelector('.flow-link[data-section="core"]');
              if (coreBtn) coreBtn.click();

              // Smooth scroll within the canvas strip
              setTimeout(() => {
                const scroller = document.querySelector('#core .overflow-x-auto');
                if (scroller) {
                  const left = target.offsetLeft - 24;
                  scroller.scrollTo({ left, behavior: 'smooth' });
                }
                target.classList.add('ring-2','ring-cyan-400/60','rounded-xl');
                setTimeout(() => target.classList.remove('ring-2','ring-cyan-400/60','rounded-xl'), 1500);
              }, 50);
            }
          }
          if (dlBtn) {
            const targetId = dlBtn.getAttribute('data-download');
            const wrap = document.getElementById(targetId);
            if (wrap) {
              const device = wrap.firstElementChild.cloneNode(true);
              const doc = `
<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>${wrap.getAttribute('data-title') || 'Frame'}</title>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>


<div class="mx-auto" style="width:390px;height:844px;">${device.outerHTML}</div>

`;
              const blob = new Blob([doc], { type: 'text/html' });
              const url = URL.createObjectURL(blob);
              const a = document.createElement('a');
              a.href = url;
              const name = (wrap.getAttribute('data-title') || 'frame').toLowerCase().replace(/\s+/g,'-');
              a.download = `${name}.html`;
              document.body.appendChild(a);
              a.click();
              URL.revokeObjectURL(url);
              a.remove();
            }
          }
        });

        // Footer: Download storyboard (JSON)
        const dlAll = document.getElementById('downloadStoryboard');
        if (dlAll) {
          const payload = screens.map((el, i) =&gt; ({
            id: el.id || `frame-${i+1}`,
            title: el.getAttribute('data-title') || `Frame ${i+1}`,
            size: { w: 390, h: 844 },
            order: i + 1
          }));
          const blob = new Blob([JSON.stringify(payload, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          dlAll.href = url;
          // Cleanup on page unload
          window.addEventListener('beforeunload', () =&gt; URL.revokeObjectURL(url));
        }

        // Composer autosize + submit behavior
        const input = document.getElementById('composerInput');
        if (input) {
          const maxRows = 6;
          const lineHeight = 20; // approximate
          const resize = () =&gt; {
            input.style.height = 'auto';
            const h = Math.min(input.scrollHeight, maxRows * lineHeight + 24);
            input.style.height = h + 'px';
          };
          input.addEventListener('input', resize);
          resize();

          input.form?.addEventListener('submit', (e) =&gt; {
            e.preventDefault();
            const value = input.value.trim();
            if (!value) return;
            // Append user's message mock
            const stream = document.querySelector('#screen-chat .overflow-y-auto');
            if (stream) {
              const bubble = document.createElement('div');
              bubble.className = 'flex items-start gap-3 justify-end';
              bubble.innerHTML = `<div class="max-w-[78%] rounded-2xl rounded-tr-sm bg-neutral-800/70 border border-white/10 text-[13px] leading-relaxed text-neutral-100 p-3">${value}</div>`;
              stream.appendChild(bubble);
              stream.scrollTop = stream.scrollHeight;
            }
            input.value = '';
            resize();
          });

          input.addEventListener('keydown', (e) =&gt; {
            if (e.key === 'Enter' &amp;&amp; !e.shiftKey) {
              e.preventDefault();
              input.form?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
            }
          });
        }
      });
    
  
</body></html>