<html lang="en"><head>
    <meta charset="UTF-8">
    <meta name="color-scheme" content="light dark">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hearth v2 — Mobile Experience</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/geist@1.3.0/dist/geist.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.3/dist/chart.umd.min.js"></script>
  </head>
  <body class="bg-neutral-50 text-neutral-900 dark:bg-neutral-950 dark:text-white antialiased" style="font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', Se<PERSON>e UI, Roboto, Helvetica, Arial, system-ui, sans-serif;">
    <!-- Background -->
    <div class="fixed inset-0 -z-10">
      <!-- Light mode background -->
      <div class="absolute inset-0 bg-gradient-to-b from-white to-neutral-100 dark:hidden"></div>
      <div class="absolute inset-0 opacity-40 dark:hidden" style="background: radial-gradient(800px 500px at 15% 10%, rgba(14,165,233,.10), transparent), radial-gradient(700px 500px at 85% 90%, rgba(14,165,233,.08), transparent);"></div>

      <!-- Dark mode background -->
      <div class="absolute inset-0 hidden dark:block bg-gradient-to-br from-neutral-950 via-neutral-900 to-neutral-950"></div>
      <div class="absolute inset-0 hidden dark:block opacity-30" style="background: radial-gradient(1000px 600px at 20% 10%, rgba(99,102,241,.20), transparent), radial-gradient(900px 500px at 80% 90%, rgba(14,165,233,.18), transparent);"></div>
    </div>

    <div class="mx-auto max-w-6xl px-4 py-7 sm:py-8">
      <!-- Screen Flow Map (Categorised) -->
      <div class="mb-6 bg-white/60 dark:bg-white/5 border border-black/5 dark:border-white/10 rounded-2xl p-3 text-neutral-900 dark:text-white backdrop-blur">
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center gap-2">
            <i data-lucide="route" class="w-4 h-4 text-sky-500"></i>
            <h2 class="text-[15px] sm:text-base font-medium tracking-tight">Screen Flows</h2>
          </div>
          <div class="text-[11px] text-neutral-500 dark:text-neutral-300">Tap a chip to jump</div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2.5">
          <!-- Learn -->
          <div class="rounded-xl bg-white/70 dark:bg-black/30 border border-black/5 dark:border-white/10 p-2.5">
            <div class="flex items-center gap-2 mb-2">
              <span class="px-2 py-1 text-[11px] rounded-full bg-sky-500/10 border border-sky-400/20 text-sky-600 dark:text-sky-200 font-medium">Learn</span>
              <span class="text-[11px] text-neutral-500 dark:text-neutral-400">Education</span>
            </div>
            <div class="flex flex-wrap gap-1.5">
              <a href="#frame1" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Today on Hearth</a>
            </div>
          </div>
          <!-- Plan -->
          <div class="rounded-xl bg-white/70 dark:bg-black/30 border border-black/5 dark:border-white/10 p-2.5">
            <div class="flex items-center gap-2 mb-2">
              <span class="px-2 py-1 text-[11px] rounded-full bg-blue-500/10 border border-blue-400/20 text-blue-600 dark:text-blue-200 font-medium">Plan</span>
              <span class="text-[11px] text-neutral-500 dark:text-neutral-400">Budgeting</span>
            </div>
            <div class="flex flex-wrap gap-1.5">
              <a href="#frame3" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Affordability</a>
            </div>
          </div>
          <!-- Explore -->
          <div class="rounded-xl bg-white/70 dark:bg-black/30 border border-black/5 dark:border-white/10 p-2.5">
            <div class="flex items-center gap-2 mb-2">
              <span class="px-2 py-1 text-[11px] rounded-full bg-cyan-500/10 border border-cyan-400/20 text-cyan-600 dark:text-cyan-200 font-medium">Explore</span>
              <span class="text-[11px] text-neutral-500 dark:text-neutral-400">Supply</span>
            </div>
            <div class="flex flex-wrap gap-1.5">
              <a href="#frame2" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Open to Offers</a>
              <a href="#frame7" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Filters</a>
              <a href="#frame8" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Request Intro</a>
            </div>
          </div>
          <!-- Prepare -->
          <div class="rounded-xl bg-white/70 dark:bg-black/30 border border-black/5 dark:border-white/10 p-2.5">
            <div class="flex items-center gap-2 mb-2">
              <span class="px-2 py-1 text-[11px] rounded-full bg-sky-500/10 border border-sky-400/20 text-sky-600 dark:text-sky-200 font-medium">Prepare</span>
              <span class="text-[11px] text-neutral-500 dark:text-neutral-400">Docs</span>
            </div>
            <div class="flex flex-wrap gap-1.5">
              <a href="#frame4" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Document Vault</a>
              <a href="#frame9" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Upload Document</a>
            </div>
          </div>
          <!-- Apply -->
          <div class="rounded-xl bg-white/70 dark:bg-black/30 border border-black/5 dark:border-white/10 p-2.5">
            <div class="flex items-center gap-2 mb-2">
              <span class="px-2 py-1 text-[11px] rounded-full bg-blue-500/10 border border-blue-400/20 text-blue-600 dark:text-blue-200 font-medium">Apply</span>
              <span class="text-[11px] text-neutral-500 dark:text-neutral-400">Pre-Approval</span>
            </div>
            <div class="flex flex-wrap gap-1.5">
              <a href="#frame5" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Readiness</a>
              <a href="#frame10" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Review &amp; Submit</a>
              <a href="#frame12" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Finance Timeline</a>
              <a href="#frame13" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Lender Offers</a>
            </div>
          </div>
          <!-- Collaborate -->
          <div class="rounded-xl bg-white/70 dark:bg-black/30 border border-black/5 dark:border-white/10 p-2.5">
            <div class="flex items-center gap-2 mb-2">
              <span class="px-2 py-1 text-[11px] rounded-full bg-cyan-500/10 border border-cyan-400/20 text-cyan-600 dark:text-cyan-200 font-medium">Collaborate</span>
              <span class="text-[11px] text-neutral-500 dark:text-neutral-400">Support</span>
            </div>
            <div class="flex flex-wrap gap-1.5">
              <a href="#frame6" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Messages</a>
              <a href="#frame11" class="px-2 py-1 text-[12px] rounded-lg bg-black/[0.03] dark:bg-white/5 border border-black/5 dark:border-white/10 hover:bg-black/[0.06] dark:hover:bg-white/10 transition">Agent Pro</a>
            </div>
          </div>
        </div>

        <div class="flex flex-wrap items-start justify-center gap-6 sm:gap-7">
          <!-- Mobile Frame 1 — Today on Hearth (FTHB) -->
          <div id="frame1" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/[0.03] dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <!-- Notch -->
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>

              <!-- Screen Content -->
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2.5">
                    <div class="w-7 h-7 rounded-xl bg-sky-500/15 border border-sky-400/25 flex items-center justify-center text-sky-300">
                      <i data-lucide="home" class="w-3.5 h-3.5"></i>
                    </div>
                    <span class="text-[15px] font-medium tracking-tight">Hearth</span>
                  </div>
                  <div class="flex items-center gap-3">
                    <div class="text-right">
                      <div class="text-[11px] text-neutral-400">Engagement</div>
                      <div class="text-[15px] font-semibold text-sky-300 leading-none">Streak 8</div>
                    </div>
                    <button class="w-7 h-7 rounded-full bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="bell" class="w-3.5 h-3.5"></i>
                    </button>
                  </div>
                </div>

                <!-- Main Content -->
                <div class="flex-1 flex flex-col gap-4.5">
                  <!-- Hero: Academy Session -->
                  <div class="relative h-56 rounded-3xl overflow-hidden border border-white/10 group" style="background-image: url('https://images.unsplash.com/photo-1600585154526-990dced4db0d?q=80&amp;w=1600&amp;auto=format&amp;fit=crop'); background-size: cover; background-position: center;">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/75 via-black/35 to-transparent"></div>
                    <div class="relative h-full p-4 flex flex-col justify-between">
                      <div class="flex items-center justify-between">
                        <span class="px-2.5 py-1.5 text-[11px] rounded-full bg-white/15 backdrop-blur-sm border border-white/20 font-medium">Hearth Academy</span>
                        <div class="flex items-center gap-1.5 text-xs">
                          <i data-lucide="clock" class="w-3.5 h-3.5 text-white/80"></i>
                          <span class="opacity-90">12 min</span>
                        </div>
                      </div>
                      <div>
                        <h1 class="text-[19px] tracking-tight font-semibold mb-1">Module 3: True Affordability</h1>
                        <p class="text-sky-100 text-[12px] opacity-95 mb-3">Including fees, insurance, and ongoing rates</p>
                        <button class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-2xl py-2.5 px-4 flex items-center justify-center gap-2 text-sm font-medium">
                          <i data-lucide="play" class="w-4 h-4"></i>
                          Continue Module
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Quick Actions -->
                  <div class="grid grid-cols-2 gap-3">
                    <button class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5 text-left hover:border-neutral-700 hover:bg-neutral-900/80 transition">
                      <div class="flex items-center justify-between mb-2.5">
                        <div class="flex items-center gap-2 text-blue-300">
                          <div class="w-7 h-7 rounded-xl bg-blue-500/15 border border-blue-400/20 flex items-center justify-center">
                            <i data-lucide="calculator" class="w-3.5 h-3.5"></i>
                          </div>
                          <span class="text-[12px] text-neutral-300">Budget</span>
                        </div>
                        <span class="text-[11px] text-neutral-400">5 min</span>
                      </div>
                      <h3 class="text-sm font-medium mb-0.5">Affordability</h3>
                      <p class="text-xs text-neutral-400">Real costs, real number</p>
                    </button>

                    <a href="#frame4" class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5 hover:border-neutral-700 hover:bg-neutral-900/80 transition block">
                      <div class="flex items-center justify-between mb-2.5">
                        <div class="flex items-center gap-2 text-sky-300">
                          <div class="w-7 h-7 rounded-xl bg-sky-500/15 border border-sky-400/20 flex items-center justify-center">
                            <i data-lucide="folder-lock" class="w-3.5 h-3.5"></i>
                          </div>
                          <span class="text-[12px] text-neutral-300">Vault</span>
                        </div>
                        <span class="text-[11px] text-neutral-400">Secure</span>
                      </div>
                      <h3 class="text-sm font-medium mb-0.5">Document Vault</h3>
                      <p class="text-xs text-neutral-400">IDs, payslips, bank statements</p>
                    </a>
                  </div>

                  <!-- Progress + WAU Insight -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2.5">
                      <div class="flex items-center gap-2">
                        <i data-lucide="activity" class="w-4 h-4 text-amber-300"></i>
                        <span class="text-sm font-medium">This Week</span>
                      </div>
                      <span class="text-[11px] bg-amber-400 text-black px-2 py-0.5 rounded-full font-semibold">+24%</span>
                    </div>

                    <div class="space-y-2 text-xs">
                      <div class="flex justify-between">
                        <span class="text-neutral-400">Academy modules</span>
                        <span class="font-medium">3/5</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-neutral-400">Pre-approval readiness</span>
                        <span class="font-medium">64%</span>
                      </div>
                      <div class="w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                        <div class="bg-blue-400 h-1.5 rounded-full" style="width: 64%"></div>
                      </div>
                    </div>

                    <!-- WAU Sparkline -->
                    <div class="mt-3.5 pt-3 border-t border-neutral-800">
                      <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center gap-2">
                          <i data-lucide="users" class="w-4 h-4 text-sky-300"></i>
                          <span class="text-sm font-medium">Weekly Active Users</span>
                        </div>
                        <span class="text-xs text-neutral-300">1,128</span>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="relative">
                          <div>
                            <canvas id="wauChart1" height="60"></canvas>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Bottom Pager -->
                <div class="flex justify-center pt-3">
                  <div class="flex gap-2">
                    <div class="w-2 h-2 bg-white rounded-full"></div>
                    <div class="w-2 h-2 bg-neutral-600 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 2 — Open to Offers Explorer -->
          <div id="frame2" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/[0.03] dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <!-- Notch -->
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>

              <!-- Screen Content -->
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <h1 class="text-[17px] tracking-tight font-semibold">Open to Offers</h1>
                  <div class="flex items-center gap-2.5">
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="search" class="w-3.5 h-3.5"></i>
                    </button>
                    <a href="#frame7" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="sliders-horizontal" class="w-3.5 h-3.5"></i>
                    </a>
                  </div>
                </div>

                <!-- Featured Property -->
                <div class="relative h-44 rounded-3xl overflow-hidden mb-4.5 border border-white/10" style="background-image: url('https://images.unsplash.com/photo-1575517111478-7f6f2c94b3b5?q=80&amp;w=1600&amp;auto=format&amp;fit=crop'); background-size: cover; background-position: center;">
                  <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                  <div class="relative h-full p-4 flex flex-col justify-between">
                    <div class="flex items-center justify-between">
                      <span class="text-[11px] bg-sky-400/20 text-sky-100 px-2.5 py-1.5 rounded-full border border-sky-300/30 font-medium">Featured</span>
                      <div class="flex items-center gap-1 text-[12px]">
                        <i data-lucide="map-pin" class="w-3.5 h-3.5 text-white/90"></i>
                        <span class="opacity-90">Melbourne 3000</span>
                      </div>
                    </div>
                    <div>
                      <h2 class="text-[15px] font-medium mb-1">Owner’s guide: $950k–$1.02m</h2>
                      <p class="text-[12px] text-neutral-200 opacity-90 mb-2.5">3 bed • 2 bath • 1 car • 220m²</p>
                      <div class="flex items-center gap-2">
                        <a href="#frame8" class="bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2 px-3 flex items-center gap-2 text-[12px] font-medium">
                          <i data-lucide="send" class="w-3.5 h-3.5"></i>
                          Request intro
                        </a>
                        <button class="bg-white/5 hover:bg-white/10 active:bg-white/15 transition border border-white/10 rounded-xl py-2 px-3 flex items-center gap-2 text-[12px]">
                          <i data-lucide="bookmark" class="w-3.5 h-3.5"></i>
                          Save
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Lists -->
                <div class="flex-1">
                  <h3 class="text-sm font-medium mb-3">Browse</h3>
                  <div class="space-y-2.5">
                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-7 h-7 bg-blue-500/15 rounded-xl border border-blue-400/20 flex items-center justify-center text-blue-300">
                          <i data-lucide="wallet" class="w-3.5 h-3.5"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Within your budget</div>
                          <div class="text-xs text-neutral-400">23 matches • Personalised</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>

                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-7 h-7 bg-amber-500/15 rounded-xl border border-amber-400/20 flex items-center justify-center text-amber-300">
                          <i data-lucide="radar" class="w-3.5 h-3.5"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">New signals in 3000</div>
                          <div class="text-xs text-neutral-400">7 homeowners updated price</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>

                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-7 h-7 bg-cyan-500/15 rounded-xl border border-cyan-400/20 flex items-center justify-center text-cyan-300">
                          <i data-lucide="shield-check" class="w-3.5 h-3.5"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Agent oversight</div>
                          <div class="text-xs text-neutral-400">Hearth Pro verified</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>
                  </div>

                  <!-- Demand Trigger Progress -->
                  <div class="mt-5 bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2.5">
                      <div class="flex items-center gap-2">
                        <i data-lucide="flag" class="w-4 h-4 text-cyan-300"></i>
                        <span class="text-sm font-medium">Phase 2 Demand Trigger</span>
                      </div>
                      <span class="text-[11px] text-neutral-300">Postcode 3000</span>
                    </div>

                    <div class="space-y-3">
                      <div>
                        <div class="flex items-center justify-between text-xs mb-1.5">
                          <span class="text-neutral-400">Academy graduates</span>
                          <span class="font-medium">842/1000</span>
                        </div>
                        <div class="w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                          <div class="bg-sky-400 h-1.5 rounded-full" style="width: 84%"></div>
                        </div>
                      </div>
                      <div>
                        <div class="flex items-center justify-between text-xs mb-1.5">
                          <span class="text-neutral-400">Active “Open to Offers”</span>
                          <span class="font-medium">412/500</span>
                        </div>
                        <div class="w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                          <div class="bg-blue-400 h-1.5 rounded-full" style="width: 82%"></div>
                        </div>
                      </div>
                    </div>

                    <div class="mt-3 flex items-center gap-2 text-xs text-neutral-300">
                      <i data-lucide="info" class="w-3.5 h-3.5"></i>
                      <span>Unlock Agent Tools + Marketplace when both bars reach 100%.</span>
                    </div>
                  </div>
                </div>

                <!-- Footer Actions -->
                <div class="pt-3 pb-2">
                  <div class="flex items-center justify-between gap-2">
                    <button class="flex-1 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                      <i data-lucide="message-square" class="w-4 h-4"></i>
                      Talk to a Hearth Pro
                    </button>
                    <button class="w-11 h-9 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="more-horizontal" class="w-4 h-4"></i>
                    </button>
                  </div>

                  <!-- Pager -->
                  <div class="flex justify-center pt-3">
                    <div class="flex gap-2">
                      <div class="w-2 h-2 bg-neutral-600 rounded-full"></div>
                      <div class="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- End Frame 2 -->

          <!-- Mobile Frame 3 — Affordability Detail -->
          <div id="frame3" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/[0.03] dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </button>
                    <h1 class="text-[17px] tracking-tight font-semibold">Affordability</h1>
                  </div>
                  <div class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                    <i data-lucide="ellipsis" class="w-3.5 h-3.5"></i>
                  </div>
                </div>

                <div class="flex-1 flex flex-col gap-3">
                  <!-- Summary Card -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2.5">
                      <div class="flex items-center gap-2">
                        <i data-lucide="wallet" class="w-4 h-4 text-sky-300"></i>
                        <span class="text-sm font-medium">Your range</span>
                      </div>
                      <span class="text-xs text-neutral-300">Updated 2m ago</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-3">
                        <div class="text-[11px] text-neutral-400 mb-1">Target</div>
                        <div class="text-base font-semibold">$720k–$840k</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-3">
                        <div class="text-[11px] text-neutral-400 mb-1">Rate</div>
                        <div class="text-base font-semibold">6.10% p.a.</div>
                      </div>
                    </div>
                  </div>

                  <!-- Deposit Progress -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-2">
                        <i data-lucide="piggy-bank" class="w-4 h-4 text-blue-300"></i>
                        <span class="text-sm font-medium">Deposit saved</span>
                      </div>
                      <span class="text-xs text-neutral-300">$84k / $120k</span>
                    </div>
                    <div class="w-full bg-neutral-800 rounded-full h-2 overflow-hidden">
                      <div class="bg-blue-400 h-2 rounded-full" style="width: 70%"></div>
                    </div>
                    <div class="mt-3 grid grid-cols-3 gap-2 text-xs">
                      <div class="rounded-lg bg-black/30 border border-white/10 p-2">
                        <div class="text-neutral-400">LVR</div>
                        <div class="font-medium">88%</div>
                      </div>
                      <div class="rounded-lg bg-black/30 border border-white/10 p-2">
                        <div class="text-neutral-400">LMI</div>
                        <div class="font-medium">Likely</div>
                      </div>
                      <div class="rounded-lg bg-black/30 border border-white/10 p-2">
                        <div class="text-neutral-400">Buffer</div>
                        <div class="font-medium">3 mo</div>
                      </div>
                    </div>
                  </div>

                  <!-- Repayments -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-2">
                        <i data-lucide="calendar" class="w-4 h-4 text-amber-300"></i>
                        <span class="text-sm font-medium">Estimated repayments</span>
                      </div>
                      <span class="text-xs text-neutral-300">P&amp;I, 30y</span>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-3">
                        <div class="text-[11px] text-neutral-400 mb-1">Monthly</div>
                        <div class="text-base font-semibold">$3,480</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-3">
                        <div class="text-[11px] text-neutral-400 mb-1">Weekly</div>
                        <div class="text-base font-semibold">$803</div>
                      </div>
                    </div>
                    <div class="mt-3 rounded-xl bg-black/30 border border-white/10 p-2">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-neutral-300">Savings over time</span>
                        <span class="text-xs text-neutral-400">12 mo</span>
                      </div>
                      <div class="relative">
                        <div>
                          <canvas id="savingsChart3" height="70"></canvas>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2">
                  <button class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="sparkles" class="w-4 h-4"></i>
                    Improve affordability tips
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 4 — Document Vault -->
          <div id="frame4" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/[0.03] dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <h1 class="text-[17px] tracking-tight font-semibold">Document Vault</h1>
                  <a href="#frame9" class="w-9 h-9 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                    <i data-lucide="plus" class="w-4 h-4"></i>
                  </a>
                </div>

                <div class="flex-1 flex flex-col gap-3">
                  <!-- Status -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <i data-lucide="shield-check" class="w-4 h-4 text-sky-300"></i>
                        <span class="text-sm font-medium">Verification status</span>
                      </div>
                      <span class="text-[11px] bg-sky-400 text-black px-2 py-0.5 rounded-full font-semibold">On track</span>
                    </div>
                    <div class="mt-3 w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                      <div class="bg-sky-400 h-1.5 rounded-full" style="width: 68%"></div>
                    </div>
                    <div class="mt-2 text-[12px] text-neutral-400">8/12 required docs uploaded</div>
                  </div>

                  <!-- Documents List -->
                  <div class="space-y-2">
                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-8 h-8 rounded-xl bg-blue-500/15 border border-blue-400/20 text-blue-300 flex items-center justify-center">
                          <i data-lucide="id-card" class="w-4 h-4"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Primary ID</div>
                          <div class="text-xs text-neutral-400">Passport.pdf • 420 KB</div>
                        </div>
                      </div>
                      <span class="text-[11px] text-emerald-300">Verified</span>
                    </div>

                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-8 h-8 rounded-xl bg-amber-500/15 border border-amber-400/20 text-amber-300 flex items-center justify-center">
                          <i data-lucide="file-text" class="w-4 h-4"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Payslips (last 3)</div>
                          <div class="text-xs text-neutral-400">2/3 uploaded • Pending</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>

                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-8 h-8 rounded-xl bg-rose-500/15 border border-rose-400/20 text-rose-300 flex items-center justify-center">
                          <i data-lucide="banknote" class="w-4 h-4"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Bank statements</div>
                          <div class="text-xs text-neutral-400">Last 90 days • PDF</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>

                    <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex items-center justify-between hover:border-neutral-700 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-8 h-8 rounded-xl bg-cyan-500/15 border border-cyan-400/20 text-cyan-300 flex items-center justify-center">
                          <i data-lucide="file-check-2" class="w-4 h-4"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Employment letter</div>
                          <div class="text-xs text-neutral-400">Optional • 180 KB</div>
                        </div>
                      </div>
                      <span class="text-[11px] text-neutral-400">Not provided</span>
                    </div>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2">
                  <div class="flex items-center gap-2">
                    <a href="#frame9" class="flex-1 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                      <i data-lucide="upload-cloud" class="w-4 h-4"></i>
                      Upload document
                    </a>
                    <button class="w-11 h-9 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="help-circle" class="w-4 h-4"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 5 — Pre-Approval Readiness -->
          <div id="frame5" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/[0.03] dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <h1 class="text-[17px] tracking-tight font-semibold">Pre-Approval</h1>
                  <div class="flex items-center gap-2.5">
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="info" class="w-3.5 h-3.5"></i>
                    </button>
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="share" class="w-3.5 h-3.5"></i>
                    </button>
                  </div>
                </div>

                <div class="flex-1 flex flex-col gap-3">
                  <!-- Progress Donut -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2.5">
                      <div class="flex items-center gap-2">
                        <i data-lucide="target" class="w-4 h-4 text-sky-300"></i>
                        <span class="text-sm font-medium">Readiness score</span>
                      </div>
                      <span class="text-xs text-neutral-300">64%</span>
                    </div>
                    <div class="grid grid-cols-3 gap-3 items-center">
                      <div class="col-span-1">
                        <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                          <div class="relative">
                            <div>
                              <canvas id="preapprovalChart5" height="110"></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-span-2 space-y-2">
                        <div>
                          <div class="flex items-center justify-between text-xs mb-1">
                            <span class="text-neutral-400">Documents</span>
                            <span class="font-medium">68%</span>
                          </div>
                          <div class="w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                            <div class="bg-sky-400 h-1.5 rounded-full" style="width: 68%"></div>
                          </div>
                        </div>
                        <div>
                          <div class="flex items-center justify-between text-xs mb-1">
                            <span class="text-neutral-400">Income check</span>
                            <span class="font-medium">72%</span>
                          </div>
                          <div class="w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                            <div class="bg-blue-400 h-1.5 rounded-full" style="width: 72%"></div>
                          </div>
                        </div>
                        <div>
                          <div class="flex items-center justify-between text-xs mb-1">
                            <span class="text-neutral-400">Credit</span>
                            <span class="font-medium">58%</span>
                          </div>
                          <div class="w-full bg-neutral-800 rounded-full h-1.5 overflow-hidden">
                            <div class="bg-amber-400 h-1.5 rounded-full" style="width: 58%"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Checklist -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5 space-y-2.5">
                    <div class="flex items-center gap-2 text-sm font-medium">
                      <i data-lucide="list-checks" class="w-4 h-4 text-blue-300"></i>
                      Required steps
                    </div>

                    <div class="flex items-center justify-between rounded-xl bg-black/30 border border-white/10 p-3 hover:border-neutral-600 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-7 h-7 rounded-lg bg-sky-500/15 border border-sky-400/20 text-sky-300 flex items-center justify-center">
                          <i data-lucide="file-check" class="w-3.5 h-3.5"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">ID Verification</div>
                          <div class="text-xs text-neutral-400">Complete</div>
                        </div>
                      </div>
                      <i data-lucide="check" class="w-4 h-4 text-emerald-400"></i>
                    </div>

                    <div class="flex items-center justify-between rounded-xl bg-black/30 border border-white/10 p-3 hover:border-neutral-600 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-7 h-7 rounded-lg bg-amber-500/15 border border-amber-400/20 text-amber-300 flex items-center justify-center">
                          <i data-lucide="file-text" class="w-3.5 h-3.5"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Payslips</div>
                          <div class="text-xs text-neutral-400">2/3 Uploaded</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>

                    <div class="flex items-center justify-between rounded-xl bg-black/30 border border-white/10 p-3 hover:border-neutral-600 transition">
                      <div class="flex items-center gap-2.5">
                        <div class="w-7 h-7 rounded-lg bg-rose-500/15 border border-rose-400/20 text-rose-300 flex items-center justify-center">
                          <i data-lucide="scan-text" class="w-3.5 h-3.5"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Bank statements</div>
                          <div class="text-xs text-neutral-400">Upload last 90 days</div>
                        </div>
                      </div>
                      <i data-lucide="chevron-right" class="w-4 h-4 text-neutral-400"></i>
                    </div>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2">
                  <a href="#frame10" class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="user-plus" class="w-4 h-4"></i>
                    Invite a broker
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 6 — Messages -->
          <div id="frame6" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/[0.03] dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </button>
                    <div>
                      <div class="text-[12px] text-neutral-300">Hearth Pro</div>
                      <h1 class="text-[15px] tracking-tight font-semibold">Ava Reed</h1>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="phone" class="w-3.5 h-3.5"></i>
                    </button>
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="video" class="w-3.5 h-3.5"></i>
                    </button>
                  </div>
                </div>

                <!-- Messages -->
                <div class="flex-1 overflow-hidden">
                  <div class="h-full overflow-y-auto pr-1 space-y-2.5">
                    <div class="flex items-start gap-2">
                      <div class="w-7 h-7 rounded-full bg-white/10 border border-white/10 flex items-center justify-center">
                        <i data-lucide="user" class="w-3.5 h-3.5 text-neutral-300"></i>
                      </div>
                      <div class="max-w-[72%] rounded-2xl rounded-tl-sm bg-neutral-900 border border-neutral-800 p-2.5">
                        <div class="text-[11px] text-neutral-400 mb-1">Ava • 09:10</div>
                        <div class="text-sm">Hey! I reviewed your affordability—looking great. Ready to move to pre-approval?</div>
                      </div>
                    </div>

                    <div class="flex items-start gap-2 justify-end">
                      <div class="max-w-[72%] rounded-2xl rounded-tr-sm bg-blue-500/20 border border-blue-400/20 p-2.5">
                        <div class="text-[11px] text-neutral-300 mb-1 text-right">You • 09:14</div>
                        <div class="text-sm">Yes, what documents do you still need from me?</div>
                      </div>
                    </div>

                    <div class="flex items-start gap-2">
                      <div class="w-7 h-7 rounded-full bg-white/10 border border-white/10 flex items-center justify-center">
                        <i data-lucide="user" class="w-3.5 h-3.5 text-neutral-300"></i>
                      </div>
                      <div class="max-w-[72%] rounded-2xl rounded-tl-sm bg-neutral-900 border border-neutral-800 p-2.5">
                        <div class="text-[11px] text-neutral-400 mb-1">Ava • 09:16</div>
                        <div class="text-sm">Just the last payslip and your bank statements. You can add them in Vault.</div>
                        <a href="#frame4" class="mt-2 inline-flex items-center gap-2 text-xs text-neutral-200 hover:text-white transition">
                          <i data-lucide="folder-open" class="w-3.5 h-3.5"></i>
                          <span>Open Vault</span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Input -->
                <div class="pt-3 pb-2">
                  <div class="flex items-center gap-2 bg-neutral-900 border border-neutral-800 rounded-2xl p-1.5">
                    <button class="w-8 h-8 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center text-neutral-300 hover:bg-white/10 transition">
                      <i data-lucide="plus" class="w-3.5 h-3.5"></i>
                    </button>
                    <input type="text" placeholder="Message Ava..." class="flex-1 bg-transparent outline-none text-sm placeholder:text-neutral-500 px-1">
                    <button class="w-9 h-8 rounded-xl bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 flex items-center justify-center text-neutral-100">
                      <i data-lucide="send" class="w-3.5 h-3.5"></i>
                    </button>
                  </div>
                </div>
                             </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 7 — Filters -->
          <div id="frame7" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame2" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Filters</h1>
                  </div>
                  <button id="resetFilters" class="text-[12px] text-neutral-300 hover:text-white transition underline underline-offset-4">Reset</button>
                </div>

                <!-- Content -->
                <div class="flex-1 space-y-3 overflow-y-auto pr-1">
                  <!-- Price Range -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center gap-2 mb-2">
                      <i data-lucide="wallet" class="w-4 h-4 text-blue-300"></i>
                      <span class="text-sm font-medium">Price range</span>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <label class="text-[11px] text-neutral-400">Min</label>
                        <input id="minPrice" type="text" value="$700k" class="w-full bg-transparent outline-none text-sm">
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <label class="text-[11px] text-neutral-400">Max</label>
                        <input id="maxPrice" type="text" value="$1.0m" class="w-full bg-transparent outline-none text-sm">
                      </div>
                    </div>
                    <div class="mt-3 space-y-2">
                      <input type="range" min="300" max="2000" value="700" class="w-full accent-sky-400">
                      <input type="range" min="300" max="2000" value="1000" class="w-full accent-sky-400">
                    </div>
                  </div>

                  <!-- Property Type -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center gap-2 mb-2">
                      <i data-lucide="building-2" class="w-4 h-4 text-amber-300"></i>
                      <span class="text-sm font-medium">Property type</span>
                    </div>
                    <div id="typeChips" class="flex flex-wrap gap-1.5">
                      <button type="button" class="chip px-2.5 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-selected="true">House</button>
                      <button type="button" class="chip px-2.5 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition">Apartment</button>
                      <button type="button" class="chip px-2.5 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition">Townhouse</button>
                      <button type="button" class="chip px-2.5 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition">Unit</button>
                    </div>
                  </div>

                  <!-- Beds / Baths -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="grid grid-cols2 gap-2">
                      <div>
                        <div class="flex items-center gap-2 mb-2">
                          <i data-lucide="bed" class="w-4 h-4 text-cyan-300"></i>
                          <span class="text-sm font-medium">Bedrooms</span>
                        </div>
                        <div class="flex gap-1.5">
                          <button class="toggle px-2 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-group="beds">2+</button>
                          <button class="toggle px-2 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-group="beds">3+</button>
                          <button class="toggle px-2 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-group="beds">4+</button>
                        </div>
                      </div>
                      <div>
                        <div class="flex items-center gap-2 mb-2">
                          <i data-lucide="shower-head" class="w-4 h-4 text-rose-300"></i>
                          <span class="text-sm font-medium">Bathrooms</span>
                        </div>
                        <div class="flex gap-1.5">
                          <button class="toggle px-2 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-group="baths">1+</button>
                          <button class="toggle px-2 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-group="baths">2+</button>
                          <button class="toggle px-2 py-1.5 text-[12px] rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition" data-group="baths">3+</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Extras -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center gap-2 mb-2">
                      <i data-lucide="sparkles" class="w-4 h-4 text-emerald-300"></i>
                      <span class="text-sm font-medium">Extras</span>
                    </div>
                    <label class="flex items-center justify-between rounded-xl bg-black/30 border border-white/10 p-2.5 text-sm">
                      <span class="text-neutral-200">Garage</span>
                      <input type="checkbox" class="accent-sky-400">
                    </label>
                    <label class="mt-2 flex items-center justify-between rounded-xl bg-black/30 border border-white/10 p-2.5 text-sm">
                      <span class="text-neutral-200">Outdoor area</span>
                      <input type="checkbox" class="accent-sky-400" checked="">
                    </label>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2">
                  <button class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="sliders-horizontal" class="w-4 h-4"></i>
                    Apply 23 results
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 8 — Request Intro -->
          <div id="frame8" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame2" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Request Intro</h1>
                  </div>
                  <div class="text-[12px] text-neutral-300">1–2 min</div>
                </div>

                <!-- Form -->
                <form class="flex-1 flex flex-col gap-3 overflow-y-auto pr-1">
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5 space-y-3">
                    <div class="grid grid-cols-1 gap-2">
                      <label class="text-xs text-neutral-300">Full name
                        <input type="text" class="mt-1 w-full rounded-xl bg-black/30 border border-white/10 px-3 py-2 text-sm outline-none focus:border-sky-400/40" placeholder="Alex Taylor" required="">
                      </label>
                      <label class="text-xs text-neutral-300">Email
                        <input type="email" class="mt-1 w-full rounded-xl bg-black/30 border border-white/10 px-3 py-2 text-sm outline-none focus:border-sky-400/40" placeholder="<EMAIL>" required="">
                      </label>
                      <label class="text-xs text-neutral-300">Phone
                        <input type="tel" class="mt-1 w-full rounded-xl bg-black/30 border border-white/10 px-3 py-2 text-sm outline-none focus:border-sky-400/40" placeholder="+61 4xx xxx xxx">
                      </label>
                      <label class="text-xs text-neutral-300">Message
                        <textarea rows="3" class="mt-1 w-full rounded-xl bg-black/30 border border-white/10 px-3 py-2 text-sm outline-none focus:border-sky-400/40" placeholder="Say hello and share your interest..."></textarea>
                      </label>
                    </div>
                    <div class="pt-1">
                      <div class="text-xs text-neutral-300 mb-2">Preferred time</div>
                      <div class="grid grid-cols-3 gap-1.5">
                        <label class="flex items-center justify-center gap-2 rounded-xl bg-white/5 border border-white/10 px-2 py-2 text-[12px] cursor-pointer hover:bg-white/10 transition">
                          <input type="radio" name="time" class="accent-sky-400" checked="">
                          <span>Morning</span>
                        </label>
                        <label class="flex items-center justify-center gap-2 rounded-xl bg-white/5 border border-white/10 px-2 py-2 text-[12px] cursor-pointer hover:bg-white/10 transition">
                          <input type="radio" name="time" class="accent-sky-400">
                          <span>Afternoon</span>
                        </label>
                        <label class="flex items-center justify-center gap-2 rounded-xl bg-white/5 border border-white/10 px-2 py-2 text-[12px] cursor-pointer hover:bg-white/10 transition">
                          <input type="radio" name="time" class="accent-sky-400">
                          <span>Evening</span>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center gap-2 text-xs text-neutral-300">
                      <i data-lucide="shield" class="w-4 h-4 text-sky-300"></i>
                      Your details are shared securely with the listing agent.
                    </div>
                  </div>

                  <div class="pb-2">
                    <button type="submit" class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                      <i data-lucide="send" class="w-4 h-4"></i>
                      Send request
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 9 — Upload Document -->
          <div id="frame9" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame4" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Upload Document</h1>
                  </div>
                  <div class="text-[12px] text-neutral-300">Secure</div>
                </div>

                <div class="flex-1 flex flex-col gap-3">
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <label class="text-xs text-neutral-300">Document type</label>
                    <select class="mt-1 w-full rounded-xl bg-black/30 border border-white/10 px-3 py-2 text-sm outline-none focus:border-sky-400/40">
                      <option>Primary ID</option>
                      <option>Secondary ID</option>
                      <option selected="">Payslip</option>
                      <option>Bank statement</option>
                      <option>Employment letter</option>
                    </select>
                  </div>

                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="rounded-xl border border-dashed border-white/15 bg-black/20 p-4 flex flex-col items-center justify-center text-center">
                      <i data-lucide="upload-cloud" class="w-6 h-6 text-sky-300 mb-2"></i>
                      <div class="text-sm font-medium mb-1">Drag &amp; drop files here</div>
                      <div class="text-xs text-neutral-400 mb-3">PDF, JPG, PNG up to 10MB</div>
                      <label class="inline-flex items-center gap-2 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-lg px-3 py-2 text-[12px] cursor-pointer">
                        <i data-lucide="file-plus" class="w-3.5 h-3.5"></i>
                        <span>Browse files</span>
                        <input type="file" class="sr-only" multiple="">
                      </label>
                    </div>
                  </div>

                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="text-sm font-medium mb-2">Selected</div>
                    <div class="space-y-2 text-[12px]">
                      <div class="flex items-center justify-between rounded-lg bg-black/30 border border-white/10 p-2">
                        <div class="flex items-center gap-2">
                          <i data-lucide="file" class="w-3.5 h-3.5 text-neutral-300"></i>
                          <span>Payslip_May.pdf • 380 KB</span>
                        </div>
                        <button class="w-7 h-7 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Remove">
                          <i data-lucide="x" class="w-3.5 h-3.5"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="pt-3 pb-2">
                  <button class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="check-circle-2" class="w-4 h-4"></i>
                    Upload and verify
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 10 — Review & Submit -->
          <div id="frame10" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header (typo fixed + restored border on action) -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame5" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Review &amp; Submit</h1>
                  </div>
                  <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="More">
                    <i data-lucide="ellipsis" class="w-3.5 h-3.5"></i>
                  </button>
                </div>

                <div class="flex-1 flex flex-col gap-3 overflow-y-auto pr-1">
                  <!-- Summary -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center gap-2">
                        <i data-lucide="file-badge-2" class="w-4 h-4 text-sky-300"></i>
                        <span class="text-sm font-medium">Application summary</span>
                      </div>
                      <span class="text-[11px] text-neutral-300">Draft</span>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Property range</div>
                        <div class="text-sm font-medium">$720k–$840k</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Deposit</div>
                        <div class="text-sm font-medium">$84k (70%)</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Loan type</div>
                        <div class="text-sm font-medium">P&amp;I • Variable</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Term</div>
                        <div class="text-sm font-medium">30 years</div>
                      </div>
                    </div>
                  </div>

                  <!-- Applicants -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5 space-y-2">
                    <div class="flex items-center gap-2 text-sm font-medium">
                      <i data-lucide="users-2" class="w-4 h-4 text-emerald-300"></i>
                      Applicants
                    </div>
                    <div class="rounded-xl bg-black/30 border border-white/10 p-2.5 flex items-center justify-between">
                      <div>
                        <div class="text-sm font-medium">Alex Taylor</div>
                        <div class="text-[12px] text-neutral-400">Primary • PAYG</div>
                      </div>
                      <button class="w-8 h-8 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Edit Applicant">
                        <i data-lucide="pencil" class="w-3.5 h-3.5"></i>
                      </button>
                    </div>
                    <div class="rounded-xl bg-black/30 border border-white/10 p-2.5 flex items-center justify-between">
                      <div>
                        <div class="text-sm font-medium">Sam Lee</div>
                        <div class="text-[12px] text-neutral-400">Co-applicant • Contractor</div>
                      </div>
                      <button class="w-8 h-8 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Edit Applicant">
                        <i data-lucide="pencil" class="w-3.5 h-3.5"></i>
                      </button>
                    </div>
                  </div>

                  <!-- Declarations -->
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center gap-2 mb-2">
                      <i data-lucide="shield-check" class="w-4 h-4 text-blue-300"></i>
                      <span class="text-sm font-medium">Declarations</span>
                    </div>
                    <label class="flex items-start gap-2 text-sm rounded-xl bg-black/30 border border-white/10 p-2.5">
                      <input type="checkbox" class="mt-0.5 accent-sky-400">
                      <span>I confirm information provided is true and correct.</span>
                    </label>
                    <label class="mt-2 flex items-start gap-2 text-sm rounded-xl bg-black/30 border border-white/10 p-2.5">
                      <input type="checkbox" class="mt-0.5 accent-sky-400">
                      <span>I consent to a credit check with participating lenders.</span>
                    </label>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2">
                  <a href="#frame12" class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="send" class="w-4 h-4"></i>
                    Submit to lenders
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 11 — Agent Pro -->
          <div id="frame11" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame2" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Agent Pro</h1>
                  </div>
                  <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="More">
                    <i data-lucide="ellipsis" class="w-3.5 h-3.5"></i>
                  </button>
                </div>

                <!-- Profile -->
                <div class="flex-1 space-y-3 overflow-y-auto pr-1">
                  <div class="relative h-36 rounded-3xl overflow-hidden border border-white/10" style="background-image: url('https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?q=80&amp;w=1600&amp;auto=format&amp;fit=crop'); background-size: cover; background-position: center;">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>
                    <div class="absolute bottom-3 left-3 right-3 flex items-end justify-between">
                      <div>
                        <div class="text-[12px] text-neutral-300">Hearth Pro</div>
                        <div class="text-[18px] font-semibold">Ava Reed</div>
                      </div>
                      <div class="flex items-center gap-1.5 text-[12px]">
                        <i data-lucide="star" class="w-4 h-4 text-amber-300"></i>
                        <span>4.9 (128)</span>
                      </div>
                    </div>
                  </div>

                  <div class="grid grid-cols-3 gap-2">
                    <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-2 text-center">
                      <div class="text-[11px] text-neutral-400">Years</div>
                      <div class="text-sm font-semibold">12</div>
                    </div>
                    <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-2 text-center">
                      <div class="text-[11px] text-neutral-400">Clients</div>
                      <div class="text-sm font-semibold">850+</div>
                    </div>
                    <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-2 text-center">
                      <div class="text-[11px] text-neutral-400">Response</div>
                      <div class="text-sm font-semibold">~2h</div>
                    </div>
                  </div>

                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="text-sm font-medium mb-1">About</div>
                    <p class="text-[13px] text-neutral-300">Mortgage specialist focusing on first home buyers and complex incomes. Clear, fast communication with a practical approach.</p>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2 flex items-center gap-2">
                  <a href="#frame6" class="flex-1 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="message-circle" class="w-4 h-4"></i>
                    Message
                  </a>
                  <button class="w-11 h-9 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Call">
                    <i data-lucide="phone" class="w-4 h-4"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 12 — Finance Timeline -->
          <div id="frame12" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame10" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Finance Timeline</h1>
                  </div>
                  <span class="text-[12px] text-neutral-300">ETA ~7–10d</span>
                </div>

                <div class="flex-1 overflow-y-auto pr-1">
                  <ol class="relative border-s border-neutral-800 pl-4 space-y-5">
                    <li class="group">
                      <div class="absolute -left-[9px] mt-0.5 w-4 h-4 rounded-full bg-emerald-400/30 border border-emerald-300/40"></div>
                      <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2">
                            <i data-lucide="graduation-cap" class="w-4 h-4 text-emerald-300"></i>
                            <span class="text-sm font-medium">Learn: Hearth Academy</span>
                          </div>
                          <span class="text-[11px] text-emerald-300">Done</span>
                        </div>
                        <p class="mt-1 text-[12px] text-neutral-400">Modules 1–3 completed</p>
                      </div>
                    </li>

                    <li class="group">
                      <div class="absolute -left-[9px] mt-0.5 w-4 h-4 rounded-full bg-sky-400/30 border border-sky-300/40"></div>
                      <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2">
                            <i data-lucide="folder-check" class="w-4 h-4 text-sky-300"></i>
                            <span class="text-sm font-medium">Prepare: Docs uploaded</span>
                          </div>
                          <span class="text-[11px] text-neutral-300">68%</span>
                        </div>
                        <p class="mt-1 text-[12px] text-neutral-400">Payslip (1 remaining), bank statements</p>
                      </div>
                    </li>

                    <li class="group">
                      <div class="absolute -left-[9px] mt-0.5 w-4 h-4 rounded-full bg-blue-400/30 border border-blue-300/40"></div>
                      <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2">
                            <i data-lucide="user-plus" class="w-4 h-4 text-blue-300"></i>
                            <span class="text-sm font-medium">Invite broker</span>
                          </div>
                          <span class="text-[11px] text-neutral-300">In progress</span>
                        </div>
                        <p class="mt-1 text-[12px] text-neutral-400">Ava invited, awaiting acceptance</p>
                      </div>
                    </li>

                    <li class="group">
                      <div class="absolute -left-[9px] mt-0.5 w-4 h-4 rounded-full bg-amber-400/30 border border-amber-300/40"></div>
                      <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2">
                            <i data-lucide="clipboard-check" class="w-4 h-4 text-amber-300"></i>
                            <span class="text-sm font-medium">Review &amp; submit</span>
                          </div>
                          <span class="text-[11px] text-neutral-300">Ready</span>
                        </div>
                        <p class="mt-1 text-[12px] text-neutral-400">Final checks complete</p>
                      </div>
                    </li>

                    <li class="group">
                      <div class="absolute -left-[9px] mt-0.5 w-4 h-4 rounded-full bg-purple-400/30 border border-purple-300/40"></div>
                      <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2">
                            <i data-lucide="hourglass" class="w-4 h-4 text-purple-300"></i>
                            <span class="text-sm font-medium">Lender review</span>
                          </div>
                          <span class="text-[11px] text-neutral-300">~5–7d</span>
                        </div>
                        <p class="mt-1 text-[12px] text-neutral-400">Assessment underway</p>
                      </div>
                    </li>

                    <li class="group">
                      <div class="absolute -left-[9px] mt-0.5 w-4 h-4 rounded-full bg-emerald-400/30 border border-emerald-300/40"></div>
                      <div class="rounded-xl bg-neutral-900 border border-neutral-800 p-3">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center gap-2">
                            <i data-lucide="badge-check" class="w-4 h-4 text-emerald-300"></i>
                            <span class="text-sm font-medium">Conditional approval</span>
                          </div>
                          <span class="text-[11px] text-neutral-300">Pending</span>
                        </div>
                        <p class="mt-1 text-[12px] text-neutral-400">Subject to valuation and LMI</p>
                      </div>
                    </li>
                  </ol>
                </div>

                <div class="pt-3 pb-2">
                  <a href="#frame13" class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="scale" class="w-4 h-4"></i>
                    View lender offers
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Frame 13 — Lender Offers -->
          <div id="frame13" data-anim="" class="w-80 sm:w-80 h-[640px] bg-black/5 dark:bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-1 shadow-2xl border border-black/5 dark:border-white/20 opacity-0 translate-y-3 blur-sm transition-all duration-700 ease-out">
            <div class="w-full h-full rounded-[2rem] bg-black overflow-hidden relative">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 w-28 h-6 bg-black rounded-b-xl z-50"></div>
              <div class="h-full flex flex-col p-3 bg-gradient-to-br from-black to-neutral-900 text-white">
                <!-- Header -->
                <div class="flex items-center justify-between pt-6 pb-4">
                  <div class="flex items-center gap-2">
                    <a href="#frame12" class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Back">
                      <i data-lucide="chevron-left" class="w-3.5 h-3.5"></i>
                    </a>
                    <h1 class="text-[17px] tracking-tight font-semibold">Lender Offers</h1>
                  </div>
                  <div class="flex items-center gap-2">
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Sort">
                      <i data-lucide="arrow-up-down" class="w-3.5 h-3.5"></i>
                    </button>
                    <button class="w-7 h-7 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Filter">
                      <i data-lucide="sliders-horizontal" class="w-3.5 h-3.5"></i>
                    </button>
                  </div>
                </div>

                <!-- Offers -->
                <div class="flex-1 overflow-y-auto pr-1 space-y-2.5">
                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center">
                          <i data-lucide="landmark" class="w-4 h-4 text-sky-300"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">BlueBank</div>
                          <div class="text-[12px] text-neutral-400">Variable • 30y • P&amp;I</div>
                        </div>
                      </div>
                      <span class="text-[11px] bg-emerald-400/20 text-emerald-200 border border-emerald-300/30 px-2 py-0.5 rounded-full">Recommended</span>
                    </div>
                    <div class="grid grid-cols-3 gap-2 mt-2">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Rate</div>
                        <div class="text-sm font-semibold">6.04%</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Comp. rate</div>
                        <div class="text-sm font-semibold">6.21%</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Monthly</div>
                        <div class="text-sm font-semibold">$3,440</div>
                      </div>
                    </div>
                    <div class="mt-2 flex items-center gap-2">
                      <button class="flex-1 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-lg py-2 text-[12px] font-medium">Compare</button>
                      <button class="w-10 h-9 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Save">
                        <i data-lucide="bookmark" class="w-4 h-4"></i>
                      </button>
                    </div>
                  </div>

                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center">
                          <i data-lucide="landmark" class="w-4 h-4 text-rose-300"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Harbor Finance</div>
                          <div class="text-[12px] text-neutral-400">Fixed 2y • 30y • P&amp;I</div>
                        </div>
                      </div>
                    </div>
                    <div class="grid grid-cols-3 gap-2 mt-2">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Rate</div>
                        <div class="text-sm font-semibold">5.89%</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Comp. rate</div>
                        <div class="text-sm font-semibold">6.35%</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Monthly</div>
                        <div class="text-sm font-semibold">$3,510</div>
                      </div>
                    </div>
                    <div class="mt-2 flex items-center gap-2">
                      <button class="flex-1 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-lg py-2 text-[12px] font-medium">Compare</button>
                      <button class="w-10 h-9 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Save">
                        <i data-lucide="bookmark" class="w-4 h-4"></i>
                      </button>
                    </div>
                  </div>

                  <div class="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center">
                          <i data-lucide="landmark" class="w-4 h-4 text-purple-300"></i>
                        </div>
                        <div>
                          <div class="text-sm font-medium">Metro Mutual</div>
                          <div class="text-[12px] text-neutral-400">Variable • Offset</div>
                        </div>
                      </div>
                    </div>
                    <div class="grid grid-cols-3 gap-2 mt-2">
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Rate</div>
                        <div class="text-sm font-semibold">6.12%</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Comp. rate</div>
                        <div class="text-sm font-semibold">6.28%</div>
                      </div>
                      <div class="rounded-xl bg-black/30 border border-white/10 p-2">
                        <div class="text-[11px] text-neutral-400">Monthly</div>
                        <div class="text-sm font-semibold">$3,470</div>
                      </div>
                    </div>
                    <div class="mt-2 flex items-center gap-2">
                      <button class="flex-1 bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-lg py-2 text-[12px] font-medium">Compare</button>
                      <button class="w-10 h-9 rounded-lg bg-white/5 border border-white/10 flex items-center justify-center hover:bg-white/10 transition" aria-label="Save">
                        <i data-lucide="bookmark" class="w-4 h-4"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Footer -->
                <div class="pt-3 pb-2">
                  <a href="#frame6" class="w-full bg-white/10 hover:bg-white/15 active:bg-white/20 transition border border-white/20 rounded-xl py-2.5 text-sm font-medium flex items-center justify-center gap-2">
                    <i data-lucide="message-square" class="w-4 h-4"></i>
                    Ask a broker for guidance
                  </a>
                </div>
              </div>
            </div>
          </div>
          <!-- End Frame 13 -->
        </div>
      </div>

      <!-- Footer Note -->
      <p class="mt-6 text-center text-[12px] text-neutral-500 dark:text-neutral-400">Prototype UI — for demonstration purposes only.</p>
    

    <!-- Scripts -->
    <script>
      // Animate frames on view
      const onReady = (fn) => document.readyState !== 'loading' ? fn() : document.addEventListener('DOMContentLoaded', fn);

      onReady(() => {
        // Lucide icons init with 1.5 stroke width
        if (window.lucide && typeof window.lucide.createIcons === 'function') {
          window.lucide.createIcons({ attrs: { 'stroke-width': 1.5 } });
        }

        // Intersection animation
        const frames = document.querySelectorAll('[data-anim]');
        const io = new IntersectionObserver((entries) => {
          entries.forEach((e) => {
            if (e.isIntersecting) {
              e.target.classList.remove('opacity-0', 'translate-y-3', 'blur-sm');
              io.unobserve(e.target);
            }
          });
        }, { threshold: 0.15 });
        frames.forEach(f => io.observe(f));

        // Chips toggle
        document.querySelectorAll('#typeChips .chip').forEach(chip => {
          chip.addEventListener('click', () => {
            const selected = chip.getAttribute('data-selected') === 'true';
            chip.setAttribute('data-selected', String(!selected));
            chip.classList.toggle('bg-white/10');
            chip.classList.toggle('border-white/20');
          });
        });

        // Toggle groups (beds/baths)
        document.querySelectorAll('.toggle').forEach(btn => {
          btn.addEventListener('click', () => {
            const group = btn.dataset.group;
            document.querySelectorAll(`.toggle[data-group="${group}"]`).forEach(b => {
              b.classList.remove('bg-white/15', 'border-white/20');
            });
            btn.classList.add('bg-white/15', 'border-white/20');
          });
        });

        // Reset filters
        const resetBtn = document.getElementById('resetFilters');
        if (resetBtn) {
          resetBtn.addEventListener('click', () => {
            document.getElementById('minPrice').value = '$700k';
            document.getElementById('maxPrice').value = '$1.0m';
            document.querySelectorAll('#typeChips .chip').forEach((chip, idx) => {
              const active = idx === 0;
              chip.setAttribute('data-selected', String(active));
              chip.classList.toggle('bg-white/10', active);
              chip.classList.toggle('border-white/20', active);
            });
            document.querySelectorAll('.toggle[data-group="beds"], .toggle[data-group="baths"]').forEach((b, idx) => {
              b.classList.toggle('bg-white/15', idx === 0);
              b.classList.toggle('border-white/20', idx === 0);
            });
          });
        }

        // Charts
        const withCtx = (id, cb) => {
          const el = document.getElementById(id);
          if (!el) return;
          const ctx = el.getContext('2d');
          cb(ctx, el);
        };

        // WAU sparkline
        withCtx('wauChart1', (ctx) => {
          const gradient = ctx.createLinearGradient(0, 0, 0, 60);
          gradient.addColorStop(0, 'rgba(56,189,248,0.35)');
          gradient.addColorStop(1, 'rgba(56,189,248,0.00)');
          new Chart(ctx, {
            type: 'line',
            data: {
              labels: Array.from({ length: 14 }, (_, i) => i + 1),
              datasets: [{
                data: [8,9,10,9,11,13,12,14,15,16,17,18,17,19],
                borderColor: 'rgb(56,189,248)',
                backgroundColor: gradient,
                fill: true,
                tension: 0.35,
                pointRadius: 0
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: { x: { display: false }, y: { display: false } },
              plugins: { legend: { display: false }, tooltip: { enabled: false } },
              elements: { line: { borderWidth: 2 } }
            }
          });
        });

        // Savings line
        withCtx('savingsChart3', (ctx) => {
          new Chart(ctx, {
            type: 'line',
            data: {
              labels: Array.from({ length: 12 }, (_, i) => i + 1),
              datasets: [{
                data: [0.2,0.5,0.9,1.3,1.6,2.0,2.5,2.9,3.2,3.6,4.0,4.5],
                borderColor: 'rgb(96,165,250)',
                backgroundColor: 'rgba(96,165,250,0.12)',
                fill: true,
                tension: 0.35,
                pointRadius: 0
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: { x: { display: false }, y: { display: false } },
              plugins: { legend: { display: false }, tooltip: { enabled: false } },
              elements: { line: { borderWidth: 2 } }
            }
          });
        });

        // Pre-approval donut
        withCtx('preapprovalChart5', (ctx, el) => {
          new Chart(ctx, {
            type: 'doughnut',
            data: {
              labels: ['Complete', 'Remaining'],
              datasets: [{
                data: [64, 36],
                backgroundColor: ['rgb(56,189,248)', 'rgba(255,255,255,0.08)'],
                borderWidth: 0,
                hoverOffset: 0
              }]
            },
            options: {
              responsive: true,
              cutout: '70%',
              plugins: { legend: { display: false }, tooltip: { enabled: false } }
            }
          });
        });
      });
    </script>
  
</body></html>