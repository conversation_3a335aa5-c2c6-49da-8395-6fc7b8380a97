<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HAUS Mobile App - Complete Screen Flows</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/geist@1.3.0/dist/geist.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.3/dist/chart.umd.min.js"></script>
    <style>
        :root {
            --haus-primary: #0EA5E9;
            --haus-secondary: #3B82F6;
            --haus-accent: #10B981;
            --haus-warning: #F59E0B;
            --haus-error: #EF4444;
            --haus-dark: #111827;
            --haus-light: #F9FAFB;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', system-ui, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .mobile-frame {
            width: 360px;
            height: 780px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 50px 100px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        .screen-content {
            height: 100%;
            overflow-y: auto;
            padding-top: 40px;
        }

        .flow-category {
            padding: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .category-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #E5E7EB;
        }

        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .screen-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
            gap: 30px;
            padding: 20px;
        }

        .flow-connector {
            position: absolute;
            width: 2px;
            background: linear-gradient(180deg, transparent, var(--haus-primary), transparent);
            z-index: 1;
        }

        .navigation-menu {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 80vh;
            overflow-y: auto;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 5px;
        }

        .nav-item:hover {
            background: var(--haus-primary);
            color: white;
        }

        .progress-tracker {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.95);
            padding: 15px 30px;
            border-radius: 30px;
            backdrop-filter: blur(10px);
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .screen-animation {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-new {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .badge-ai {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .badge-3d {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation Menu -->
    <div class="navigation-menu">
        <h3 class="text-lg font-bold mb-4">Screen Categories</h3>
        <div class="nav-item" onclick="scrollToSection('onboarding')">
            <span>🚀</span> Onboarding & Profile
        </div>
        <div class="nav-item" onclick="scrollToSection('search')">
            <span>🔍</span> Search & Discovery
        </div>
        <div class="nav-item" onclick="scrollToSection('property')">
            <span>🏠</span> Property Details
        </div>
        <div class="nav-item" onclick="scrollToSection('financial')">
            <span>💰</span> Financial Planning
        </div>
        <div class="nav-item" onclick="scrollToSection('education')">
            <span>📚</span> Education & Academy
        </div>
        <div class="nav-item" onclick="scrollToSection('documentation')">
            <span>📄</span> Documentation
        </div>
        <div class="nav-item" onclick="scrollToSection('communication')">
            <span>💬</span> Communication
        </div>
        <div class="nav-item" onclick="scrollToSection('transaction')">
            <span>✅</span> Application & Transaction
        </div>
        <div class="nav-item" onclick="scrollToSection('analytics')">
            <span>📊</span> Analytics & Insights
        </div>
        <div class="nav-item" onclick="scrollToSection('settings')">
            <span>⚙️</span> Settings & Support
        </div>
    </div>

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-8">
        <header class="text-center mb-12">
            <h1 class="text-5xl font-bold text-white mb-4">HAUS Mobile App</h1>
            <p class="text-xl text-white/90">Complete Screen Flow Architecture</p>
            <div class="flex justify-center gap-4 mt-6">
                <span class="status-badge badge-new">86 Screens</span>
                <span class="status-badge badge-ai">AI-Powered</span>
                <span class="status-badge badge-3d">3D/VR Ready</span>
            </div>
        </header>

        <!-- SECTION 1: ONBOARDING & PROFILE -->
        <div id="onboarding" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    🚀
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Onboarding & Profile</h2>
                    <p class="text-gray-600">First-time user experience and profile setup</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Splash -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gradient-to-br from-sky-500 to-blue-600">
                            <div class="flex flex-col items-center justify-center h-full text-white p-8">
                                <div class="w-24 h-24 bg-white/20 rounded-3xl flex items-center justify-center mb-8">
                                    <span class="text-5xl">🏠</span>
                                </div>
                                <h1 class="text-4xl font-bold mb-4">HAUS</h1>
                                <p class="text-lg opacity-90 text-center">Find your perfect property 10x faster with AI</p>
                                <div class="mt-auto space-y-4 w-full">
                                    <button class="w-full bg-white text-blue-600 rounded-2xl py-4 font-semibold">Get Started</button>
                                    <button class="w-full bg-white/20 text-white rounded-2xl py-4">I have an account</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Welcome -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-6">
                                <div class="text-center mb-8">
                                    <h2 class="text-2xl font-bold mb-2">Welcome to HAUS</h2>
                                    <p class="text-gray-600">Let's get you started in 3 simple steps</p>
                                </div>
                                <div class="space-y-4">
                                    <div class="flex items-center gap-4 p-4 bg-gray-50 rounded-2xl">
                                        <div class="w-12 h-12 bg-sky-100 rounded-xl flex items-center justify-center text-sky-600 font-bold">1</div>
                                        <div>
                                            <h3 class="font-semibold">Tell us about yourself</h3>
                                            <p class="text-sm text-gray-600">Basic info to personalize your experience</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-4 p-4 bg-gray-50 rounded-2xl">
                                        <div class="w-12 h-12 bg-sky-100 rounded-xl flex items-center justify-center text-sky-600 font-bold">2</div>
                                        <div>
                                            <h3 class="font-semibold">Set your preferences</h3>
                                            <p class="text-sm text-gray-600">Location, budget, and property type</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-4 p-4 bg-gray-50 rounded-2xl">
                                        <div class="w-12 h-12 bg-sky-100 rounded-xl flex items-center justify-center text-sky-600 font-bold">3</div>
                                        <div>
                                            <h3 class="font-semibold">Discover properties</h3>
                                            <p class="text-sm text-gray-600">AI-powered matches just for you</p>
                                        </div>
                                    </div>
                                </div>
                                <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold mt-8">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 3: Account Creation -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-6">
                                <h2 class="text-2xl font-bold mb-6">Create Account</h2>
                                <div class="space-y-4">
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Full Name</label>
                                        <input type="text" class="w-full p-4 bg-gray-50 rounded-xl" placeholder="John Smith">
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Email</label>
                                        <input type="email" class="w-full p-4 bg-gray-50 rounded-xl" placeholder="<EMAIL>">
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Phone</label>
                                        <input type="tel" class="w-full p-4 bg-gray-50 rounded-xl" placeholder="+61 4XX XXX XXX">
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Password</label>
                                        <input type="password" class="w-full p-4 bg-gray-50 rounded-xl" placeholder="••••••••">
                                    </div>
                                </div>
                                <div class="mt-6">
                                    <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold">Create Account</button>
                                    <div class="flex items-center gap-4 my-6">
                                        <div class="flex-1 h-px bg-gray-300"></div>
                                        <span class="text-gray-500 text-sm">or continue with</span>
                                        <div class="flex-1 h-px bg-gray-300"></div>
                                    </div>
                                    <div class="flex gap-4">
                                        <button class="flex-1 p-3 border border-gray-300 rounded-xl flex items-center justify-center gap-2">
                                            <span>🍎</span> Apple
                                        </button>
                                        <button class="flex-1 p-3 border border-gray-300 rounded-xl flex items-center justify-center gap-2">
                                            <span>🔍</span> Google
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 4: User Type Selection -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-6">
                                <h2 class="text-2xl font-bold mb-2">I'm looking to...</h2>
                                <p class="text-gray-600 mb-6">This helps us personalize your experience</p>
                                <div class="space-y-4">
                                    <button class="w-full p-6 bg-gradient-to-r from-sky-50 to-blue-50 border-2 border-sky-200 rounded-2xl text-left">
                                        <div class="flex items-start gap-4">
                                            <span class="text-3xl">🏡</span>
                                            <div>
                                                <h3 class="font-semibold text-lg">Buy my first home</h3>
                                                <p class="text-sm text-gray-600 mt-1">New to property buying, need guidance</p>
                                            </div>
                                        </div>
                                    </button>
                                    <button class="w-full p-6 bg-gray-50 border-2 border-gray-200 rounded-2xl text-left">
                                        <div class="flex items-start gap-4">
                                            <span class="text-3xl">📈</span>
                                            <div>
                                                <h3 class="font-semibold text-lg">Invest in property</h3>
                                                <p class="text-sm text-gray-600 mt-1">Looking for rental yield and growth</p>
                                            </div>
                                        </div>
                                    </button>
                                    <button class="w-full p-6 bg-gray-50 border-2 border-gray-200 rounded-2xl text-left">
                                        <div class="flex items-start gap-4">
                                            <span class="text-3xl">🔄</span>
                                            <div>
                                                <h3 class="font-semibold text-lg">Upgrade my home</h3>
                                                <p class="text-sm text-gray-600 mt-1">Selling current property, buying new</p>
                                            </div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 5: Location Preferences -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-6">
                                <h2 class="text-2xl font-bold mb-2">Where are you looking?</h2>
                                <p class="text-gray-600 mb-6">Add your preferred suburbs or regions</p>
                                <div class="mb-4">
                                    <div class="flex gap-2">
                                        <input type="text" class="flex-1 p-4 bg-gray-50 rounded-xl" placeholder="Search suburb or postcode">
                                        <button class="px-6 py-4 bg-sky-500 text-white rounded-xl">Add</button>
                                    </div>
                                </div>
                                <div class="space-y-3 mb-6">
                                    <div class="flex items-center justify-between p-3 bg-sky-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span>📍</span>
                                            <div>
                                                <p class="font-semibold">Melbourne CBD</p>
                                                <p class="text-sm text-gray-600">3000</p>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">✕</button>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-sky-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span>📍</span>
                                            <div>
                                                <p class="font-semibold">Richmond</p>
                                                <p class="text-sm text-gray-600">3121</p>
                                            </div>
                                        </div>
                                        <button class="text-gray-400">✕</button>
                                    </div>
                                </div>
                                <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 6: Budget Range -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-6">
                                <h2 class="text-2xl font-bold mb-2">What's your budget?</h2>
                                <p class="text-gray-600 mb-6">We'll show properties in your range</p>
                                <div class="space-y-6">
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Minimum Price</label>
                                        <input type="text" class="w-full p-4 bg-gray-50 rounded-xl text-2xl font-bold" value="$700,000">
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Maximum Price</label>
                                        <input type="text" class="w-full p-4 bg-gray-50 rounded-xl text-2xl font-bold" value="$1,000,000">
                                    </div>
                                    <div class="bg-sky-50 p-4 rounded-xl">
                                        <p class="text-sm text-gray-600 mb-2">Estimated deposit needed</p>
                                        <p class="text-2xl font-bold text-sky-600">$140,000 - $200,000</p>
                                        <p class="text-xs text-gray-500 mt-1">Based on 20% deposit</p>
                                    </div>
                                    <div class="flex items-center gap-3 p-4 bg-amber-50 rounded-xl">
                                        <span>💡</span>
                                        <p class="text-sm">We'll calculate your true affordability based on your income and expenses</p>
                                    </div>
                                </div>
                                <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold mt-6">Continue</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 7: Property Preferences -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-6">
                                <h2 class="text-2xl font-bold mb-2">Property preferences</h2>
                                <p class="text-gray-600 mb-6">What are you looking for?</p>
                                <div class="space-y-4">
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Property Type</label>
                                        <div class="grid grid-cols-2 gap-3">
                                            <button class="p-3 bg-sky-500 text-white rounded-xl">House</button>
                                            <button class="p-3 bg-gray-100 rounded-xl">Apartment</button>
                                            <button class="p-3 bg-gray-100 rounded-xl">Townhouse</button>
                                            <button class="p-3 bg-gray-100 rounded-xl">Unit</button>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Bedrooms</label>
                                        <div class="flex gap-3">
                                            <button class="px-4 py-3 bg-gray-100 rounded-xl">1+</button>
                                            <button class="px-4 py-3 bg-gray-100 rounded-xl">2+</button>
                                            <button class="px-4 py-3 bg-sky-500 text-white rounded-xl">3+</button>
                                            <button class="px-4 py-3 bg-gray-100 rounded-xl">4+</button>
                                            <button class="px-4 py-3 bg-gray-100 rounded-xl">5+</button>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Must-haves</label>
                                        <div class="grid grid-cols-2 gap-3">
                                            <label class="flex items-center gap-2 p-3 bg-gray-50 rounded-xl">
                                                <input type="checkbox" class="w-5 h-5">
                                                <span>Parking</span>
                                            </label>
                                            <label class="flex items-center gap-2 p-3 bg-gray-50 rounded-xl">
                                                <input type="checkbox" class="w-5 h-5" checked>
                                                <span>Outdoor Space</span>
                                            </label>
                                            <label class="flex items-center gap-2 p-3 bg-gray-50 rounded-xl">
                                                <input type="checkbox" class="w-5 h-5">
                                                <span>Pet Friendly</span>
                                            </label>
                                            <label class="flex items-center gap-2 p-3 bg-gray-50 rounded-xl">
                                                <input type="checkbox" class="w-5 h-5">
                                                <span>Study/Office</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold mt-6">Complete Setup</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 8: Profile Complete -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gradient-to-br from-green-400 to-green-600">
                            <div class="flex flex-col items-center justify-center h-full text-white p-8">
                                <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mb-6">
                                    <span class="text-5xl">✓</span>
                                </div>
                                <h2 class="text-3xl font-bold mb-4">You're all set!</h2>
                                <p class="text-lg opacity-90 text-center mb-8">We found 47 properties matching your criteria</p>
                                <div class="bg-white/20 rounded-2xl p-4 w-full mb-6">
                                    <p class="text-sm opacity-90 mb-2">Your AI property assistant is ready</p>
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-white/30 rounded-full flex items-center justify-center">
                                            <span>🤖</span>
                                        </div>
                                        <div>
                                            <p class="font-semibold">HAUS AI</p>
                                            <p class="text-sm opacity-90">Ask me anything about properties</p>
                                        </div>
                                    </div>
                                </div>
                                <button class="w-full bg-white text-green-600 rounded-2xl py-4 font-bold">Start Exploring</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 2: SEARCH & DISCOVERY -->
        <div id="search" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #0EA5E9 0%, #3B82F6 100%);">
                    🔍
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Search & Discovery</h2>
                    <p class="text-gray-600">AI-powered property search and exploration</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Home Dashboard -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gray-50">
                            <div class="p-4">
                                <!-- Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <p class="text-sm text-gray-600">Good morning, John</p>
                                        <h1 class="text-xl font-bold">Find your dream home</h1>
                                    </div>
                                    <div class="flex gap-2">
                                        <button class="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                                            <span>🔔</span>
                                        </button>
                                        <button class="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                                            <span>👤</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- AI Search Bar -->
                                <div class="bg-gradient-to-r from-sky-500 to-blue-600 rounded-2xl p-4 mb-4">
                                    <div class="bg-white/20 rounded-xl p-3 flex items-center gap-3">
                                        <span class="text-white">🎤</span>
                                        <input type="text" placeholder="Say 'modern 3BR near good schools'" class="bg-transparent text-white placeholder-white/70 flex-1 outline-none">
                                    </div>
                                    <div class="flex gap-2 mt-3">
                                        <span class="px-3 py-1 bg-white/20 rounded-full text-xs text-white">Under $1M</span>
                                        <span class="px-3 py-1 bg-white/20 rounded-full text-xs text-white">Melbourne</span>
                                        <span class="px-3 py-1 bg-white/20 rounded-full text-xs text-white">3+ beds</span>
                                    </div>
                                </div>

                                <!-- Quick Stats -->
                                <div class="grid grid-cols-3 gap-3 mb-4">
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-sky-500">47</p>
                                        <p class="text-xs text-gray-600">New matches</p>
                                    </div>
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-green-500">12</p>
                                        <p class="text-xs text-gray-600">Price drops</p>
                                    </div>
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-purple-500">3</p>
                                        <p class="text-xs text-gray-600">Open today</p>
                                    </div>
                                </div>

                                <!-- Featured Property -->
                                <div class="bg-white rounded-2xl overflow-hidden mb-4">
                                    <div class="h-40 bg-gradient-to-br from-gray-200 to-gray-300 relative">
                                        <span class="absolute top-3 left-3 px-3 py-1 bg-yellow-400 text-xs font-bold rounded-full">AI MATCH 98%</span>
                                        <span class="absolute top-3 right-3 text-2xl">❤️</span>
                                    </div>
                                    <div class="p-3">
                                        <p class="font-bold text-lg">$850,000 - $920,000</p>
                                        <p class="text-gray-600 text-sm">123 Collins St, Melbourne</p>
                                        <div class="flex gap-4 mt-2 text-sm text-gray-500">
                                            <span>🛏 3</span>
                                            <span>🚿 2</span>
                                            <span>🚗 2</span>
                                            <span>📐 350m²</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Natural Language Search -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-lg font-bold">AI Search</h2>
                                </div>

                                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-4 mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                            <span class="text-2xl">🤖</span>
                                        </div>
                                        <div class="flex-1">
                                            <p class="text-white font-semibold">HAUS AI</p>
                                            <p class="text-white/80 text-sm">I understand natural language</p>
                                        </div>
                                    </div>
                                    <div class="bg-white/20 rounded-xl p-3">
                                        <p class="text-white text-sm">Try saying: "Show me family homes near Camberwell station with a big backyard under 1.2 million"</p>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="p-3 bg-gray-50 rounded-xl">
                                        <p class="text-sm text-gray-600">Recent searches</p>
                                    </div>
                                    <button class="w-full p-3 bg-white border border-gray-200 rounded-xl text-left">
                                        <p class="text-sm">"Modern apartment with gym and pool"</p>
                                        <p class="text-xs text-gray-500">Found 23 matches</p>
                                    </button>
                                    <button class="w-full p-3 bg-white border border-gray-200 rounded-xl text-left">
                                        <p class="text-sm">"House near good schools, quiet street"</p>
                                        <p class="text-xs text-gray-500">Found 18 matches</p>
                                    </button>
                                    <button class="w-full p-3 bg-white border border-gray-200 rounded-xl text-left">
                                        <p class="text-sm">"Investment property with high rental yield"</p>
                                        <p class="text-xs text-gray-500">Found 31 matches</p>
                                    </button>
                                </div>

                                <div class="mt-6">
                                    <button class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl py-4 font-semibold flex items-center justify-center gap-2">
                                        <span>🎤</span>
                                        <span>Start Voice Search</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 3: Map View -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gray-100">
                            <!-- Map Area -->
                            <div class="h-2/3 bg-gradient-to-br from-green-100 to-blue-100 relative">
                                <!-- Search Bar Overlay -->
                                <div class="absolute top-4 left-4 right-4 bg-white rounded-xl shadow-lg p-3">
                                    <input type="text" placeholder="Search area..." class="w-full outline-none text-sm">
                                </div>

                                <!-- Map Markers -->
                                <div class="absolute top-32 left-20">
                                    <div class="bg-sky-500 text-white px-3 py-1 rounded-full text-sm font-bold">$850k</div>
                                </div>
                                <div class="absolute top-48 right-16">
                                    <div class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">$920k</div>
                                </div>
                                <div class="absolute bottom-32 left-32">
                                    <div class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold">$780k</div>
                                </div>
                                <div class="absolute bottom-20 right-24">
                                    <div class="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold">12</div>
                                </div>

                                <!-- Map Controls -->
                                <div class="absolute bottom-4 right-4 space-y-2">
                                    <button class="w-10 h-10 bg-white rounded-xl shadow flex items-center justify-center">
                                        <span>+</span>
                                    </button>
                                    <button class="w-10 h-10 bg-white rounded-xl shadow flex items-center justify-center">
                                        <span>-</span>
                                    </button>
                                    <button class="w-10 h-10 bg-white rounded-xl shadow flex items-center justify-center">
                                        <span>📍</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Bottom Sheet -->
                            <div class="h-1/3 bg-white rounded-t-3xl p-4">
                                <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="font-bold">Properties nearby</h3>
                                    <button class="text-sky-500 text-sm">Filter</button>
                                </div>
                                <div class="flex gap-3 overflow-x-auto">
                                    <div class="min-w-[200px] bg-gray-50 rounded-xl p-3">
                                        <div class="h-24 bg-gray-200 rounded-lg mb-2"></div>
                                        <p class="font-semibold text-sm">$850,000</p>
                                        <p class="text-xs text-gray-600">3 bed • 2 bath</p>
                                    </div>
                                    <div class="min-w-[200px] bg-gray-50 rounded-xl p-3">
                                        <div class="h-24 bg-gray-200 rounded-lg mb-2"></div>
                                        <p class="font-semibold text-sm">$920,000</p>
                                        <p class="text-xs text-gray-600">4 bed • 2 bath</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 4: List View -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gray-50">
                            <div class="p-4">
                                <!-- Header with filters -->
                                <div class="flex items-center justify-between mb-4">
                                    <h2 class="text-xl font-bold">47 Properties</h2>
                                    <div class="flex gap-2">
                                        <button class="px-3 py-1 bg-white rounded-lg text-sm">Sort</button>
                                        <button class="px-3 py-1 bg-white rounded-lg text-sm">Filter</button>
                                    </div>
                                </div>

                                <!-- Property Cards -->
                                <div class="space-y-3">
                                    <div class="bg-white rounded-2xl overflow-hidden">
                                        <div class="flex">
                                            <div class="w-32 h-32 bg-gradient-to-br from-sky-200 to-blue-300"></div>
                                            <div class="flex-1 p-3">
                                                <div class="flex items-start justify-between">
                                                    <div>
                                                        <p class="font-bold">$850,000</p>
                                                        <p class="text-sm text-gray-600">Richmond</p>
                                                    </div>
                                                    <span class="text-xl">❤️</span>
                                                </div>
                                                <div class="flex gap-3 mt-2 text-xs text-gray-500">
                                                    <span>🛏 3</span>
                                                    <span>🚿 2</span>
                                                    <span>🚗 2</span>
                                                </div>
                                                <div class="mt-2">
                                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">New</span>
                                                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs ml-1">98% match</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-2xl overflow-hidden">
                                        <div class="flex">
                                            <div class="w-32 h-32 bg-gradient-to-br from-purple-200 to-pink-300"></div>
                                            <div class="flex-1 p-3">
                                                <div class="flex items-start justify-between">
                                                    <div>
                                                        <p class="font-bold">$920,000</p>
                                                        <p class="text-sm text-gray-600">Hawthorn</p>
                                                    </div>
                                                    <span class="text-xl">🤍</span>
                                                </div>
                                                <div class="flex gap-3 mt-2 text-xs text-gray-500">
                                                    <span>🛏 4</span>
                                                    <span>🚿 2</span>
                                                    <span>🚗 1</span>
                                                </div>
                                                <div class="mt-2">
                                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs">Price drop</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 5: AI Recommendations -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">AI Recommendations</h2>
                                </div>
                                
                                <!-- AI Insight Card -->
                                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-4 text-white mb-4">
                                    <div class="flex items-center gap-3 mb-3">
                                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                                            <span>🤖</span>
                                        </div>
                                        <div>
                                            <p class="font-semibold">HAUS AI Insights</p>
                                            <p class="text-sm opacity-90">Based on your preferences and market data</p>
                                        </div>
                                    </div>
                                    <p class="text-sm">I've found 5 properties that match your criteria but you haven't viewed yet. Here are my top recommendations:</p>
                                </div>
                                
                                <!-- Recommended Properties -->
                                <div class="space-y-3">
                                    <div class="border border-green-200 bg-green-50 rounded-xl p-3">
                                        <div class="flex items-start gap-3">
                                            <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
                                            <div class="flex-1">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <p class="font-semibold">$785,000</p>
                                                    <span class="px-2 py-0.5 bg-green-500 text-white text-xs rounded-full">98% Match</span>
                                                </div>
                                                <p class="text-sm text-gray-600">45 Park Ave, Richmond</p>
                                                <p class="text-xs text-gray-500">3 bed • 2 bath • 1 car</p>
                                                <p class="text-xs text-green-600 mt-1">✓ Excellent school zone ✓ North-facing ✓ Under budget by $15k</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="border border-sky-200 bg-sky-50 rounded-xl p-3">
                                        <div class="flex items-start gap-3">
                                            <div class="w-16 h-16 bg-gray-200 rounded-lg"></div>
                                            <div class="flex-1">
                                                <div class="flex items-center gap-2 mb-1">
                                                    <p class="font-semibold">$820,000</p>
                                                    <span class="px-2 py-0.5 bg-sky-500 text-white text-xs rounded-full">95% Match</span>
                                                </div>
                                                <p class="text-sm text-gray-600">12 Beach Rd, St Kilda</p>
                                                <p class="text-xs text-gray-500">3 bed • 2 bath • 2 car</p>
                                                <p class="text-xs text-sky-600 mt-1">✓ Beach proximity ✓ Price drop 5% ✓ Motivated seller</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-2xl py-3 font-semibold mt-4">
                                    View All Recommendations
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 6: Neighborhood Insights -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Neighborhood Insights</h2>
                                </div>
                                
                                <!-- Area Overview -->
                                <div class="bg-gray-50 rounded-xl p-4 mb-4">
                                    <h3 class="font-semibold mb-2">Richmond, VIC</h3>
                                    <p class="text-sm text-gray-600">Suburb snapshot and livability score</p>
                                </div>
                                
                                <!-- Neighborhood Scores -->
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span>🚶</span>
                                            <div>
                                                <p class="font-semibold">Walkability</p>
                                                <p class="text-xs text-gray-600">Shops, cafes, transport</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-green-600">9/10</p>
                                            <p class="text-xs text-green-600">Excellent</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span>🚇</span>
                                            <div>
                                                <p class="font-semibold">Transport</p>
                                                <p class="text-xs text-gray-600">Trains, trams, buses</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-blue-600">8/10</p>
                                            <p class="text-xs text-blue-600">Very Good</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span>🎓</span>
                                            <div>
                                                <p class="font-semibold">Schools</p>
                                                <p class="text-xs text-gray-600">Primary, secondary</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-purple-600">9/10</p>
                                            <p class="text-xs text-purple-600">Excellent</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span>💰</span>
                                            <div>
                                                <p class="font-semibold">Affordability</p>
                                                <p class="text-xs text-gray-600">Value for money</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-bold text-yellow-600">7/10</p>
                                            <p class="text-xs text-yellow-600">Good</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Demographics -->
                                <div class="mt-4 p-4 bg-gray-50 rounded-xl">
                                    <h4 class="font-semibold mb-3">Demographics</h4>
                                    <div class="grid grid-cols-2 gap-3 text-sm">
                                        <div>
                                            <p class="text-gray-600">Population</p>
                                            <p class="font-semibold">28,500</p>
                                        </div>
                                        <div>
                                            <p class="text-gray-600">Median Age</p>
                                            <p class="font-semibold">34 years</p>
                                        </div>
                                        <div>
                                            <p class="text-gray-600">Families</p>
                                            <p class="font-semibold">62%</p>
                                        </div>
                                        <div>
                                            <p class="text-gray-600">Crime Rate</p>
                                            <p class="font-semibold">Low</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>

        <!-- SECTION 3: PROPERTY DETAILS -->
        <div id="property" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #10B981 0%, #059669 100%);">
                    🏠
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Property Details</h2>
                    <p class="text-gray-600">Deep dive into property information with AI insights</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Property Overview -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <!-- Image Gallery -->
                            <div class="h-64 bg-gradient-to-br from-blue-200 to-cyan-300 relative">
                                <button class="absolute top-4 left-4 w-10 h-10 bg-white/80 backdrop-blur rounded-xl flex items-center justify-center">
                                    <span>←</span>
                                </button>
                                <button class="absolute top-4 right-4 w-10 h-10 bg-white/80 backdrop-blur rounded-xl flex items-center justify-center">
                                    <span>❤️</span>
                                </button>
                                <div class="absolute bottom-4 left-4 right-4 flex justify-between items-end">
                                    <div>
                                        <p class="text-white text-2xl font-bold">$850,000 - $920,000</p>
                                        <p class="text-white/90">123 Collins Street, Melbourne</p>
                                    </div>
                                    <button class="px-4 py-2 bg-white/20 backdrop-blur rounded-xl text-white text-sm">
                                        <span>🖼 24 photos</span>
                                    </button>
                                </div>
                            </div>

                            <div class="p-4 space-y-4">
                                <!-- AI Match Score -->
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <span class="text-2xl">🤖</span>
                                            <div>
                                                <p class="font-semibold">AI Match Score</p>
                                                <p class="text-sm text-gray-600">Based on your preferences</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-2xl font-bold text-green-600">98%</p>
                                            <p class="text-xs text-gray-600">Excellent</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Stats -->
                                <div class="grid grid-cols-4 gap-2">
                                    <div class="text-center p-3 bg-gray-50 rounded-xl">
                                        <span class="text-xl">🛏</span>
                                        <p class="text-sm font-semibold">3</p>
                                        <p class="text-xs text-gray-600">Beds</p>
                                    </div>
                                    <div class="text-center p-3 bg-gray-50 rounded-xl">
                                        <span class="text-xl">🚿</span>
                                        <p class="text-sm font-semibold">2</p>
                                        <p class="text-xs text-gray-600">Baths</p>
                                    </div>
                                    <div class="text-center p-3 bg-gray-50 rounded-xl">
                                        <span class="text-xl">🚗</span>
                                        <p class="text-sm font-semibold">2</p>
                                        <p class="text-xs text-gray-600">Cars</p>
                                    </div>
                                    <div class="text-center p-3 bg-gray-50 rounded-xl">
                                        <span class="text-xl">📐</span>
                                        <p class="text-sm font-semibold">350</p>
                                        <p class="text-xs text-gray-600">m²</p>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex gap-3">
                                    <button class="flex-1 bg-sky-500 text-white rounded-xl py-3 font-semibold">
                                        Book Inspection
                                    </button>
                                    <button class="flex-1 bg-purple-500 text-white rounded-xl py-3 font-semibold">
                                        3D Tour
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: AI Analysis -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gray-50">
                            <div class="p-4">
                                <h2 class="text-xl font-bold mb-4">AI Property Analysis</h2>
                                
                                <!-- DEEPHAUS Score -->
                                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-4 text-white mb-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <div>
                                            <p class="text-sm opacity-90">DEEPHAUS Score</p>
                                            <p class="text-3xl font-bold">92/100</p>
                                        </div>
                                        <div class="w-20 h-20 rounded-full border-4 border-white/30 flex items-center justify-center">
                                            <span class="text-2xl">A+</span>
                                        </div>
                                    </div>
                                    <p class="text-sm opacity-90">Excellent investment potential based on 15+ metrics</p>
                                </div>

                                <!-- Metrics -->
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl p-3">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-semibold">Fair Market Value</span>
                                            <span class="text-sm text-green-600">Good deal</span>
                                        </div>
                                        <div class="text-lg font-bold">$875,000</div>
                                        <div class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="h-full w-4/5 bg-green-500 rounded-full"></div>
                                        </div>
                                    </div>

                                    <div class="bg-white rounded-xl p-3">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-semibold">Rental Yield</span>
                                            <span class="text-sm text-sky-600">Above average</span>
                                        </div>
                                        <div class="text-lg font-bold">5.2% p.a.</div>
                                        <p class="text-xs text-gray-600 mt-1">Est. $850/week</p>
                                    </div>

                                    <div class="bg-white rounded-xl p-3">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-semibold">Growth Potential</span>
                                            <span class="text-sm text-purple-600">High</span>
                                        </div>
                                        <div class="text-lg font-bold">+8.5% yearly</div>
                                        <p class="text-xs text-gray-600 mt-1">Next 5 years projection</p>
                                    </div>

                                    <div class="bg-white rounded-xl p-3">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm font-semibold">Neighborhood Score</span>
                                            <span class="text-sm text-blue-600">Excellent</span>
                                        </div>
                                        <div class="flex gap-4 mt-2">
                                            <div class="text-center">
                                                <p class="text-xs text-gray-600">Schools</p>
                                                <p class="font-bold">9/10</p>
                                            </div>
                                            <div class="text-center">
                                                <p class="text-xs text-gray-600">Transport</p>
                                                <p class="font-bold">8/10</p>
                                            </div>
                                            <div class="text-center">
                                                <p class="text-xs text-gray-600">Amenities</p>
                                                <p class="font-bold">9/10</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 3: 3D Virtual Tour -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-black">
                            <!-- 3D View -->
                            <div class="h-3/4 bg-gradient-to-br from-indigo-900 to-purple-900 relative">
                                <!-- VR Controls -->
                                <div class="absolute top-4 left-4 right-4 flex justify-between">
                                    <button class="w-10 h-10 bg-white/20 backdrop-blur rounded-xl flex items-center justify-center text-white">
                                        <span>✕</span>
                                    </button>
                                    <div class="flex gap-2">
                                        <button class="px-3 py-2 bg-white/20 backdrop-blur rounded-xl text-white text-sm">
                                            VR Mode
                                        </button>
                                        <button class="w-10 h-10 bg-white/20 backdrop-blur rounded-xl flex items-center justify-center text-white">
                                            <span>⛶</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Room Label -->
                                <div class="absolute bottom-4 left-4">
                                    <p class="text-white text-lg font-semibold">Living Room</p>
                                    <p class="text-white/80 text-sm">Tap markers for AI insights</p>
                                </div>

                                <!-- AI Insight Markers -->
                                <div class="absolute top-1/3 left-1/4">
                                    <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse">
                                        <span class="text-xs">💡</span>
                                    </div>
                                </div>
                                <div class="absolute top-1/2 right-1/3">
                                    <div class="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center animate-pulse">
                                        <span class="text-xs">🌱</span>
                                    </div>
                                </div>

                                <!-- Navigation -->
                                <div class="absolute bottom-4 right-4 flex gap-2">
                                    <button class="w-12 h-12 bg-white/20 backdrop-blur rounded-xl flex items-center justify-center text-white">
                                        <span>←</span>
                                    </button>
                                    <button class="w-12 h-12 bg-white/20 backdrop-blur rounded-xl flex items-center justify-center text-white">
                                        <span>→</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Room Selector -->
                            <div class="h-1/4 bg-gray-900 p-4">
                                <p class="text-white text-sm mb-3">Select Room</p>
                                <div class="flex gap-2 overflow-x-auto">
                                    <button class="px-4 py-2 bg-purple-600 text-white rounded-xl text-sm whitespace-nowrap">
                                        Living Room
                                    </button>
                                    <button class="px-4 py-2 bg-gray-800 text-gray-400 rounded-xl text-sm whitespace-nowrap">
                                        Kitchen
                                    </button>
                                    <button class="px-4 py-2 bg-gray-800 text-gray-400 rounded-xl text-sm whitespace-nowrap">
                                        Master Bedroom
                                    </button>
                                    <button class="px-4 py-2 bg-gray-800 text-gray-400 rounded-xl text-sm whitespace-nowrap">
                                        Bathroom
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 4: AI Copilot Chat -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="flex flex-col h-full">
                                <!-- Header -->
                                <div class="p-4 bg-gradient-to-r from-purple-500 to-pink-500">
                                    <div class="flex items-center gap-3 text-white">
                                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                                            <span>🤖</span>
                                        </div>
                                        <div>
                                            <p class="font-semibold">HAUS AI Copilot</p>
                                            <p class="text-sm opacity-90">Ask me anything about this property</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Chat Messages -->
                                <div class="flex-1 p-4 space-y-3 overflow-y-auto">
                                    <div class="flex gap-2">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <span class="text-xs">🤖</span>
                                        </div>
                                        <div class="flex-1 bg-gray-100 rounded-2xl rounded-tl-sm p-3">
                                            <p class="text-sm">Hi! I've analyzed this property. Here are the key highlights:</p>
                                            <ul class="mt-2 space-y-1 text-sm">
                                                <li>✓ Excellent school catchment zone</li>
                                                <li>✓ 5 min walk to train station</li>
                                                <li>✓ North-facing backyard</li>
                                                <li>✓ Recently renovated kitchen</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="flex gap-2 justify-end">
                                        <div class="bg-sky-500 text-white rounded-2xl rounded-tr-sm p-3 max-w-[80%]">
                                            <p class="text-sm">Does it get good sunlight?</p>
                                        </div>
                                    </div>

                                    <div class="flex gap-2">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                            <span class="text-xs">🤖</span>
                                        </div>
                                        <div class="flex-1 bg-gray-100 rounded-2xl rounded-tl-sm p-3">
                                            <p class="text-sm">Yes! The property has excellent natural light:</p>
                                            <div class="mt-2 bg-white rounded-xl p-3">
                                                <p class="text-xs font-semibold mb-2">Sunlight Analysis</p>
                                                <div class="space-y-1 text-xs">
                                                    <div class="flex justify-between">
                                                        <span>Morning sun:</span>
                                                        <span class="font-semibold">Kitchen, Dining</span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span>Afternoon sun:</span>
                                                        <span class="font-semibold">Living, Backyard</span>
                                                    </div>
                                                    <div class="flex justify-between">
                                                        <span>Energy rating:</span>
                                                        <span class="font-semibold">6.5 stars</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Input Area -->
                                <div class="p-4 border-t">
                                    <div class="flex gap-2">
                                        <input type="text" placeholder="Ask about this property..." class="flex-1 p-3 bg-gray-100 rounded-xl outline-none">
                                        <button class="w-12 h-12 bg-purple-500 text-white rounded-xl flex items-center justify-center">
                                            <span>→</span>
                                        </button>
                                    </div>
                                    <div class="flex gap-2 mt-2 overflow-x-auto">
                                        <button class="px-3 py-1 bg-gray-100 rounded-full text-xs whitespace-nowrap">
                                            Renovation potential?
                                        </button>
                                        <button class="px-3 py-1 bg-gray-100 rounded-full text-xs whitespace-nowrap">
                                            Compare to others
                                        </button>
                                        <button class="px-3 py-1 bg-gray-100 rounded-full text-xs whitespace-nowrap">
                                            Investment analysis
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 4: FINANCIAL PLANNING -->
        <div id="financial" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #F59E0B 0%, #DC2626 100%);">
                    💰
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Financial Planning</h2>
                    <p class="text-gray-600">Affordability, pre-approval, and lending</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Affordability Calculator -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gray-50">
                            <div class="p-4">
                                <h2 class="text-xl font-bold mb-4">Affordability Calculator</h2>
                                
                                <!-- Summary Card -->
                                <div class="bg-gradient-to-r from-sky-500 to-blue-600 rounded-2xl p-4 text-white mb-4">
                                    <p class="text-sm opacity-90 mb-1">You can afford</p>
                                    <p class="text-3xl font-bold mb-2">$720k - $840k</p>
                                    <div class="flex justify-between text-sm">
                                        <span>Monthly: $3,480</span>
                                        <span>Weekly: $803</span>
                                    </div>
                                </div>

                                <!-- Input Fields -->
                                <div class="space-y-3">
                                    <div class="bg-white rounded-xl p-3">
                                        <label class="text-xs text-gray-600">Annual Income (before tax)</label>
                                        <input type="text" class="w-full text-xl font-bold mt-1" value="$120,000">
                                    </div>
                                    
                                    <div class="bg-white rounded-xl p-3">
                                        <label class="text-xs text-gray-600">Deposit Saved</label>
                                        <input type="text" class="w-full text-xl font-bold mt-1" value="$84,000">
                                        <div class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
                                            <div class="h-full w-3/4 bg-green-500 rounded-full"></div>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-1">70% of target</p>
                                    </div>

                                    <div class="bg-white rounded-xl p-3">
                                        <label class="text-xs text-gray-600">Interest Rate</label>
                                        <div class="flex items-center gap-2 mt-1">
                                            <input type="text" class="flex-1 text-xl font-bold" value="6.10%">
                                            <span class="text-xs text-gray-600">p.a.</span>
                                        </div>
                                    </div>

                                    <div class="bg-amber-50 border border-amber-200 rounded-xl p-3">
                                        <div class="flex gap-2">
                                            <span>💡</span>
                                            <div>
                                                <p class="text-sm font-semibold">Improve affordability</p>
                                                <p class="text-xs text-gray-600 mt-1">Add a co-buyer to increase by ~40%</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold mt-4">
                                    Get Pre-Approval
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Pre-Approval Application -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Pre-Approval Application</h2>
                                </div>
                                
                                <!-- Progress Indicator -->
                                <div class="flex items-center gap-2 mb-6">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">✓</div>
                                    <div class="h-0.5 bg-gray-200 flex-1"></div>
                                    <div class="w-8 h-8 bg-sky-500 rounded-full flex items-center justify-center text-white text-sm">2</div>
                                    <div class="h-0.5 bg-gray-200 flex-1"></div>
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-white text-sm">3</div>
                                </div>
                                
                                <!-- Employment Details -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Employment Status</label>
                                        <select class="w-full p-4 bg-gray-50 rounded-xl">
                                            <option>Full-time employed</option>
                                            <option>Part-time employed</option>
                                            <option>Self-employed</option>
                                            <option>Contractor</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Employer Name</label>
                                        <input type="text" class="w-full p-4 bg-gray-50 rounded-xl" placeholder="Company name">
                                    </div>
                                    
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Length of Employment</label>
                                        <select class="w-full p-4 bg-gray-50 rounded-xl">
                                            <option>Less than 6 months</option>
                                            <option>6 months - 1 year</option>
                                            <option>1-2 years</option>
                                            <option>2+ years</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-sky-500 text-white rounded-2xl py-4 font-semibold mt-6">
                                    Continue
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 3: Loan Comparison -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Loan Options</h2>
                                </div>
                                
                                <p class="text-gray-600 mb-4">Compare pre-approved loan options</p>
                                
                                <!-- Loan Options -->
                                <div class="space-y-3">
                                    <div class="border-2 border-sky-500 bg-sky-50 rounded-xl p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <h3 class="font-semibold">Variable Rate</h3>
                                            <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">Recommended</span>
                                        </div>
                                        <p class="text-2xl font-bold text-sky-600">5.89%</p>
                                        <p class="text-sm text-gray-600">Monthly repayment: $3,245</p>
                                        <div class="mt-3 flex gap-2">
                                            <button class="px-3 py-1 bg-white border border-sky-200 rounded-lg text-sm">Details</button>
                                            <button class="px-3 py-1 bg-sky-500 text-white rounded-lg text-sm">Select</button>
                                        </div>
                                    </div>
                                    
                                    <div class="border border-gray-200 bg-white rounded-xl p-4">
                                        <h3 class="font-semibold mb-2">Fixed Rate</h3>
                                        <p class="text-2xl font-bold text-gray-600">6.15%</p>
                                        <p class="text-sm text-gray-600">Monthly repayment: $3,340</p>
                                        <div class="mt-3 flex gap-2">
                                            <button class="px-3 py-1 bg-gray-100 rounded-lg text-sm">Details</button>
                                            <button class="px-3 py-1 bg-gray-500 text-white rounded-lg text-sm">Select</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </div>
        </div>

        <!-- SECTION 5: EDUCATION & ACADEMY (Sample) -->
        <div id="education" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);">
                    📚
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Education & Academy</h2>
                    <p class="text-gray-600">Learn about property buying</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Academy Dashboard Screen -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <h2 class="text-xl font-bold mb-4">HAUS Academy</h2>
                                
                                <!-- Progress Overview -->
                                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-4 text-white mb-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <div>
                                            <p class="text-sm opacity-90">Your Progress</p>
                                            <p class="text-2xl font-bold">Level 3</p>
                                        </div>
                                        <div class="w-16 h-16 rounded-full border-4 border-white/30 flex items-center justify-center">
                                            <span class="text-xl font-bold">64%</span>
                                        </div>
                                    </div>
                                    <div class="h-2 bg-white/30 rounded-full overflow-hidden">
                                        <div class="h-full w-2/3 bg-white rounded-full"></div>
                                    </div>
                                </div>

                                <!-- Course Modules -->
                                <div class="space-y-3">
                                    <div class="bg-gray-50 rounded-xl p-3">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                                    <span>✓</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-sm">Module 1: Getting Started</p>
                                                    <p class="text-xs text-gray-600">Completed</p>
                                                </div>
                                            </div>
                                            <span class="text-green-600 text-sm">100%</span>
                                        </div>
                                    </div>

                                    <div class="bg-sky-50 border-2 border-sky-200 rounded-xl p-3">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-3">
                                                <div class="w-10 h-10 bg-sky-100 rounded-xl flex items-center justify-center">
                                                    <span>📖</span>
                                                </div>
                                                <div>
                                                    <p class="font-semibold text-sm">Module 2: Understanding Finance</p>
                                                    <p class="text-xs text-gray-600">In Progress • 12 min left</p>
                                                </div>
                                            </div>
                                            <span class="text-sky-600 text-sm">60%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 6: DOCUMENTATION -->
        <div id="documentation" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);">
                    📄
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Documentation</h2>
                    <p class="text-gray-600">Contracts, forms, and paperwork management</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Document Hub -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h2 class="text-xl font-bold">Documents</h2>
                                    <button class="w-10 h-10 bg-sky-500 text-white rounded-xl flex items-center justify-center">
                                        <span>+</span>
                                    </button>
                                </div>
                                
                                <!-- Document Categories -->
                                <div class="space-y-3">
                                    <div class="p-4 bg-red-50 border border-red-200 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span class="text-2xl">📋</span>
                                            <div>
                                                <h3 class="font-semibold">Required Documents</h3>
                                                <p class="text-sm text-gray-600">3 pending</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="p-4 bg-amber-50 border border-amber-200 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span class="text-2xl">✍️</span>
                                            <div>
                                                <h3 class="font-semibold">Signature Required</h3>
                                                <p class="text-sm text-gray-600">2 documents</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="p-4 bg-green-50 border border-green-200 rounded-xl">
                                        <div class="flex items-center gap-3">
                                            <span class="text-2xl">✅</span>
                                            <div>
                                                <h3 class="font-semibold">Completed</h3>
                                                <p class="text-sm text-gray-600">12 documents</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Recent Documents -->
                                <div class="mt-6">
                                    <h3 class="font-semibold mb-3">Recent</h3>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>📄</span>
                                                <div>
                                                    <p class="font-medium">Contract of Sale</p>
                                                    <p class="text-xs text-gray-600">2 days ago</p>
                                                </div>
                                            </div>
                                            <span class="text-green-600">✓</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: E-Signature -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Sign Document</h2>
                                </div>
                                
                                <!-- Document Preview -->
                                <div class="bg-gray-100 rounded-xl p-4 mb-4" style="height: 300px;">
                                    <div class="text-center text-gray-500">
                                        <p>Contract of Sale Preview</p>
                                        <p class="text-sm mt-2">Swipe to review pages</p>
                                    </div>
                                </div>
                                
                                <!-- Signature Area -->
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center">
                                    <p class="text-gray-600 mb-2">Sign in the box below</p>
                                    <div class="bg-white border border-gray-300 rounded-lg h-24 mb-3"></div>
                                    <button class="text-sky-500 text-sm">Clear Signature</button>
                                </div>
                                
                                <button class="w-full bg-green-500 text-white rounded-2xl py-4 font-semibold mt-4">
                                    Complete Signature
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 7: COMMUNICATION -->
        <div id="communication" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);">
                    💬
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Communication</h2>
                    <p class="text-gray-600">Chat, calls, and collaboration</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Messages Hub -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center justify-between mb-4">
                                    <h2 class="text-xl font-bold">Messages</h2>
                                    <div class="flex gap-2">
                                        <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                            <span>🔍</span>
                                        </button>
                                        <button class="w-10 h-10 bg-sky-500 text-white rounded-xl flex items-center justify-center">
                                            <span>+</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Active Conversations -->
                                <div class="space-y-3">
                                    <div class="flex items-center gap-3 p-3 bg-sky-50 rounded-xl">
                                        <div class="w-12 h-12 bg-sky-500 rounded-full flex items-center justify-center text-white">
                                            JD
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between">
                                                <p class="font-semibold">John Doe - Agent</p>
                                                <span class="text-xs text-gray-500">2m ago</span>
                                            </div>
                                            <p class="text-sm text-gray-600">Great! Let's schedule a viewing for...</p>
                                        </div>
                                        <div class="w-2 h-2 bg-sky-500 rounded-full"></div>
                                    </div>
                                    
                                    <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                                        <div class="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center text-white">
                                            SM
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between">
                                                <p class="font-semibold">Sarah Miller - Lender</p>
                                                <span class="text-xs text-gray-500">1h ago</span>
                                            </div>
                                            <p class="text-sm text-gray-600">Your pre-approval is ready</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Video Call -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-black">
                            <!-- Video Area -->
                            <div class="h-2/3 bg-gradient-to-br from-gray-700 to-gray-900 relative">
                                <div class="absolute top-4 right-4 w-24 h-32 bg-gray-600 rounded-lg">
                                    <div class="absolute bottom-2 left-2 text-white text-xs">You</div>
                                </div>
                                <div class="absolute bottom-4 left-4 text-white">
                                    <p class="font-semibold">John Doe - Real Estate Agent</p>
                                    <p class="text-sm opacity-80">Property Viewing Call</p>
                                </div>
                            </div>
                            
                            <!-- Call Controls -->
                            <div class="h-1/3 bg-gray-900 p-4">
                                <div class="flex justify-center gap-4 mt-8">
                                    <button class="w-14 h-14 bg-red-500 rounded-full flex items-center justify-center">
                                        <span class="text-white">📞</span>
                                    </button>
                                    <button class="w-14 h-14 bg-gray-700 rounded-full flex items-center justify-center">
                                        <span class="text-white">🎤</span>
                                    </button>
                                    <button class="w-14 h-14 bg-gray-700 rounded-full flex items-center justify-center">
                                        <span class="text-white">📹</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 8: APPLICATION & TRANSACTION -->
        <div id="transaction" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #10B981 0%, #059669 100%);">
                    ✅
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Application & Transaction</h2>
                    <p class="text-gray-600">Offers, contracts, and settlements</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Make an Offer -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Make an Offer</h2>
                                </div>
                                
                                <!-- Property Summary -->
                                <div class="bg-gray-50 rounded-xl p-4 mb-4">
                                    <h3 class="font-semibold mb-2">123 Collins Street, Melbourne</h3>
                                    <p class="text-2xl font-bold text-sky-600">$850,000</p>
                                    <p class="text-sm text-gray-600">3 bed • 2 bath • 2 car</p>
                                </div>
                                
                                <!-- Offer Form -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Your Offer</label>
                                        <input type="text" class="w-full p-4 bg-gray-50 rounded-xl text-xl font-bold" value="$820,000">
                                    </div>
                                    
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Settlement Period</label>
                                        <select class="w-full p-4 bg-gray-50 rounded-xl">
                                            <option>30 days</option>
                                            <option>60 days</option>
                                            <option>90 days</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="text-sm text-gray-600 mb-2 block">Special Conditions</label>
                                        <textarea class="w-full p-4 bg-gray-50 rounded-xl" rows="3" placeholder="Any special conditions..."></textarea>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-green-500 text-white rounded-2xl py-4 font-semibold mt-6">
                                    Submit Offer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Offer Status -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <h2 class="text-xl font-bold mb-4">Offer Status</h2>
                                
                                <!-- Status Timeline -->
                                <div class="space-y-4">
                                    <div class="flex items-start gap-3">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">✓</div>
                                        <div>
                                            <p class="font-semibold">Offer Submitted</p>
                                            <p class="text-sm text-gray-600">Your offer of $820,000 has been sent</p>
                                            <p class="text-xs text-gray-500">2 hours ago</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-start gap-3">
                                        <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm">⏳</div>
                                        <div>
                                            <p class="font-semibold">Under Review</p>
                                            <p class="text-sm text-gray-600">Seller is considering your offer</p>
                                            <p class="text-xs text-gray-500">In progress</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-start gap-3 opacity-50">
                                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-white text-sm">?</div>
                                        <div>
                                            <p class="font-semibold">Response Pending</p>
                                            <p class="text-sm text-gray-600">Expected response within 24 hours</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Next Actions -->
                                <div class="mt-6 p-4 bg-blue-50 rounded-xl">
                                    <p class="text-sm font-semibold text-blue-900 mb-2">What's next?</p>
                                    <p class="text-sm text-blue-800">We'll notify you as soon as the seller responds to your offer.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 9: ANALYTICS & INSIGHTS -->
        <div id="analytics" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #F59E0B 0%, #DC2626 100%);">
                    📊
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Analytics & Insights</h2>
                    <p class="text-gray-600">Market data and personal insights</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Personal Dashboard -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-gray-50">
                            <div class="p-4">
                                <h2 class="text-xl font-bold mb-4">Your Insights</h2>
                                
                                <!-- Key Metrics -->
                                <div class="grid grid-cols-2 gap-3 mb-4">
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-sky-500">47</p>
                                        <p class="text-xs text-gray-600">Properties Viewed</p>
                                    </div>
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-green-500">12</p>
                                        <p class="text-xs text-gray-600">Favorites</p>
                                    </div>
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-purple-500">5</p>
                                        <p class="text-xs text-gray-600">Offers Made</p>
                                    </div>
                                    <div class="bg-white rounded-xl p-3 text-center">
                                        <p class="text-2xl font-bold text-orange-500">2</p>
                                        <p class="text-xs text-gray-600">Inspections</p>
                                    </div>
                                </div>
                                
                                <!-- Market Insights -->
                                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-4 text-white mb-4">
                                    <h3 class="font-semibold mb-2">Market Update</h3>
                                    <p class="text-sm opacity-90">Property prices in your target areas have increased 2.3% this month</p>
                                </div>
                                
                                <!-- Recommendations -->
                                <div class="bg-white rounded-xl p-4">
                                    <h3 class="font-semibold mb-3">AI Recommendations</h3>
                                    <div class="space-y-2">
                                        <div class="flex items-start gap-2">
                                            <span class="text-green-500">💡</span>
                                            <p class="text-sm">Consider expanding your search to nearby suburbs for better value</p>
                                        </div>
                                        <div class="flex items-start gap-2">
                                            <span class="text-blue-500">📈</span>
                                            <p class="text-sm">Your pre-approval amount could increase by $50k with current rates</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Market Trends -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Market Trends</h2>
                                </div>
                                
                                <!-- Price Chart -->
                                <div class="bg-gray-50 rounded-xl p-4 mb-4" style="height: 200px;">
                                    <div class="text-center text-gray-500">
                                        <p>Price Trend Chart</p>
                                        <p class="text-sm mt-2">Interactive price visualization</p>
                                    </div>
                                </div>
                                
                                <!-- Key Stats -->
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                        <span class="text-sm">Average Price</span>
                                        <span class="font-semibold">$875,000</span>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                        <span class="text-sm">Price Change (12m)</span>
                                        <span class="font-semibold text-green-600">+5.2%</span>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                        <span class="text-sm">Days on Market</span>
                                        <span class="font-semibold">28 days</span>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                        <span class="text-sm">Properties Available</span>
                                        <span class="font-semibold">156</span>
                                    </div>
                                </div>
                                
                                <!-- AI Insight -->
                                <div class="mt-4 p-3 bg-blue-50 rounded-xl">
                                    <div class="flex items-start gap-2">
                                        <span>🤖</span>
                                        <div>
                                            <p class="text-sm font-semibold text-blue-900">AI Analysis</p>
                                            <p class="text-sm text-blue-800">Market conditions favor buyers. Consider making offers 5-8% below asking price.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECTION 10: SETTINGS & SUPPORT -->
        <div id="settings" class="flow-category">
            <div class="category-header">
                <div class="category-icon" style="background: linear-gradient(135deg, #6B7280 0%, #374151 100%);">
                    ⚙️
                </div>
                <div>
                    <h2 class="text-2xl font-bold">Settings & Support</h2>
                    <p class="text-gray-600">Preferences, help, and account</p>
                </div>
            </div>

            <div class="screen-grid">
                <!-- Screen 1: Settings Main -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <h2 class="text-xl font-bold mb-4">Settings</h2>
                                
                                <!-- Account Section -->
                                <div class="mb-6">
                                    <h3 class="text-sm text-gray-600 mb-3">Account</h3>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>👤</span>
                                                <span class="font-medium">Profile</span>
                                            </div>
                                            <span>→</span>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>🔔</span>
                                                <span class="font-medium">Notifications</span>
                                            </div>
                                            <span>→</span>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>🔒</span>
                                                <span class="font-medium">Privacy & Security</span>
                                            </div>
                                            <span>→</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Preferences Section -->
                                <div class="mb-6">
                                    <h3 class="text-sm text-gray-600 mb-3">Preferences</h3>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>🌙</span>
                                                <span class="font-medium">Dark Mode</span>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-sky-500"></div>
                                            </label>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>🌐</span>
                                                <span class="font-medium">Language</span>
                                            </div>
                                            <span class="text-sm text-gray-600">English →</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Support Section -->
                                <div>
                                    <h3 class="text-sm text-gray-600 mb-3">Support</h3>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>❓</span>
                                                <span class="font-medium">Help Center</span>
                                            </div>
                                            <span>→</span>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                                            <div class="flex items-center gap-3">
                                                <span>💬</span>
                                                <span class="font-medium">Contact Support</span>
                                            </div>
                                            <span>→</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Screen 2: Help Center -->
                <div class="mobile-frame screen-animation">
                    <div class="mobile-screen">
                        <div class="notch"></div>
                        <div class="screen-content bg-white">
                            <div class="p-4">
                                <div class="flex items-center gap-3 mb-4">
                                    <button class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                                        <span>←</span>
                                    </button>
                                    <h2 class="text-xl font-bold">Help Center</h2>
                                </div>
                                
                                <!-- Search -->
                                <div class="mb-4">
                                    <input type="text" placeholder="Search help topics..." class="w-full p-3 bg-gray-50 rounded-xl">
                                </div>
                                
                                <!-- FAQ Categories -->
                                <div class="space-y-3">
                                    <div class="p-4 bg-gray-50 rounded-xl">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-3">
                                                <span class="text-xl">🏠</span>
                                                <div>
                                                    <p class="font-semibold">Property Search</p>
                                                    <p class="text-sm text-gray-600">Finding and comparing properties</p>
                                                </div>
                                            </div>
                                            <span>→</span>
                                        </div>
                                    </div>
                                    
                                    <div class="p-4 bg-gray-50 rounded-xl">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-3">
                                                <span class="text-xl">💰</span>
                                                <div>
                                                    <p class="font-semibold">Finance & Offers</p>
                                                    <p class="text-sm text-gray-600">Pre-approval and making offers</p>
                                                </div>
                                            </div>
                                            <span>→</span>
                                        </div>
                                    </div>
                                    
                                    <div class="p-4 bg-gray-50 rounded-xl">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-3">
                                                <span class="text-xl">📋</span>
                                                <div>
                                                    <p class="font-semibold">Documentation</p>
                                                    <p class="text-sm text-gray-600">Contracts and paperwork</p>
                                                </div>
                                            </div>
                                            <span>→</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Contact Support -->
                                <div class="mt-6 p-4 bg-blue-50 rounded-xl">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-semibold text-blue-900">Still need help?</p>
                                            <p class="text-sm text-blue-800">Contact our support team</p>
                                        </div>
                                        <button class="px-4 py-2 bg-blue-500 text-white rounded-xl text-sm">
                                            Contact
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Tracker -->
        <div class="progress-tracker">
            <span class="text-sm text-gray-600">Viewing</span>
            <span class="font-semibold">Complete Mobile Screen Flows</span>
            <span class="text-sm text-gray-600">•</span>
            <span class="text-sm text-sky-500">86 Total Screens</span>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Smooth scroll to sections
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all screen frames
        document.querySelectorAll('.mobile-frame').forEach(frame => {
            frame.style.opacity = '0';
            frame.style.transform = 'translateY(20px)';
            frame.style.transition = 'all 0.6s ease-out';
            observer.observe(frame);
        });

        // Add interactive hover effects
        document.querySelectorAll('.mobile-frame').forEach(frame => {
            frame.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
                this.style.boxShadow = '0 60px 120px rgba(0,0,0,0.4)';
            });
            
            frame.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 50px 100px rgba(0,0,0,0.3)';
            });
        });

        // Add category stats
        const categories = [
            { id: 'onboarding', count: 8 },
            { id: 'search', count: 12 },
            { id: 'property', count: 10 },
            { id: 'financial', count: 10 },
            { id: 'education', count: 8 },
            { id: 'documentation', count: 8 },
            { id: 'communication', count: 8 },
            { id: 'transaction', count: 10 },
            { id: 'analytics', count: 6 },
            { id: 'settings', count: 6 }
        ];

        // Update navigation with screen counts
        document.querySelectorAll('.nav-item').forEach((item, index) => {
            if (categories[index]) {
                const badge = document.createElement('span');
                badge.className = 'ml-auto text-xs bg-gray-200 px-2 py-1 rounded-full';
                badge.textContent = categories[index].count;
                item.appendChild(badge);
            }
        });

        console.log('HAUS Mobile Screen Flows loaded successfully!');
        console.log('Total screens designed: 86');
        console.log('Categories: 10');
        console.log('AI-powered features integrated throughout');
    </script>

    <!-- Footer -->
    <footer class="text-center py-8 text-white/80">
        <p class="text-sm">HAUS Mobile App © 2024</p>
        <p class="text-xs mt-2">AI-Powered Real Estate Platform</p>
        <p class="text-xs mt-4 opacity-60">Design System v1.0 • Optimized for Mobile</p>
    </footer>
</body>
</html>
