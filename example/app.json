{"expo": {"name": "HeroUI Native", "slug": "heroui-native", "scheme": "herouinative", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.herouinative.ios", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "edgeToEdgeEnabled": true, "package": "com.herouinative.android"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", "expo-font"], "extra": {"router": {"origin": false}, "eas": {"projectId": "13560f7b-27d6-41e8-a677-0b4d06705e7e"}}, "owner": "heroui-inc", "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/13560f7b-27d6-41e8-a677-0b4d06705e7e"}}}