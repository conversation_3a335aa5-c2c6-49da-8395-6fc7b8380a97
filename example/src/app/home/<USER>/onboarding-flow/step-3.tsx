import Feather from '@expo/vector-icons/Feather';
import { useRouter } from 'expo-router';
import { Button, cn, useTheme } from 'heroui-native';
import { View } from 'react-native';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText } from '../../../../components/app-text';

const AnimatedView = Animated.createAnimatedComponent(View);

const OnboardingStep3 = () => {
  const { isDark } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <View
      className="flex-1 bg-background"
      style={{
        paddingTop: insets.top + 12,
        paddingBottom: insets.bottom + 12,
      }}
    >
      <AnimatedView
        entering={FadeIn.duration(500)}
        className="px-4 flex-row items-center justify-between"
      >
        <Button
          size="sm"
          className={cn('rounded-full bg-black/20', isDark && 'bg-white/20')}
          isIconOnly
          onPress={() => router.push('/showcases/onboarding-flow/step-2')}
        >
          <Button.LabelContent>
            <Feather
              name="chevron-left"
              size={24}
              color={isDark ? 'black' : 'white'}
            />
          </Button.LabelContent>
        </Button>
        <Button
          size="sm"
          className={cn('rounded-full bg-black/20', isDark && 'bg-white/20')}
          isIconOnly
          onPress={() => router.push('/showcases')}
        >
          <Button.LabelContent>
            <Feather name="x" size={24} color={isDark ? 'black' : 'white'} />
          </Button.LabelContent>
        </Button>
      </AnimatedView>

      <View className="flex-1 items-center justify-center px-8">
        <AnimatedView entering={FadeInDown.springify()} className="items-center gap-2">
          <AppText className="text-xs font-semibold uppercase tracking-wide text-muted-foreground">
            Step 3 · Team
          </AppText>
          <AppText className="text-3xl font-semibold text-foreground text-center">
            Stay aligned with your crew
          </AppText>
          <AppText className="text-center text-foreground/75">
            Invite your agent, lender, and co-buyers. HAUS keeps everyone synced on tasks, document requests, and milestones without endless group texts.
          </AppText>
        </AnimatedView>
      </View>

      <AnimatedView entering={FadeInDown.delay(200).springify()}>
        <Button
          onPress={() => router.push('/showcases')}
          className="mx-8 rounded-full bg-[#F8DD00] mb-4"
        >
          <Button.LabelContent
            classNames={{ text: 'text-lg font-semibold text-black' }}
          >
            Finish setup
          </Button.LabelContent>
        </Button>
      </AnimatedView>
    </View>
  );
};

export default OnboardingStep3;
