import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const FiltersScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);
  const [minPrice, setMinPrice] = useState('700000');
  const [maxPrice, setMaxPrice] = useState('1000000');
  const [propertyType, setPropertyType] = useState('house');
  const [bedrooms, setBedrooms] = useState(3);
  const [bathrooms, setBathrooms] = useState(2);
  const [hasGarage, setHasGarage] = useState(false);
  const [hasOutdoor, setHasOutdoor] = useState(true);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/open-to-offers')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Filters
          </AppText>
          
          <Button
            size="sm"
            variant="ghost"
            onPress={() => {
              setMinPrice('700000');
              setMaxPrice('1000000');
              setPropertyType('house');
              setBedrooms(3);
              setBathrooms(2);
              setHasGarage(false);
              setHasOutdoor(true);
            }}
            className="rounded-xl"
          >
            <Button.LabelContent classNames={{ text: 'text-sm' }}>
              Reset
            </Button.LabelContent>
          </Button>
        </View>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="mb-4">
            <View className="flex-row items-center gap-2 mb-3">
              <Feather name="dollar-sign" size={16} color={colors.foreground} />
              <AppText className="text-sm font-medium text-foreground">
                Price range
              </AppText>
            </View>
            
            <View className="flex-row gap-3 mb-3">
              <View className="flex-1 rounded-xl border border-border p-3">
                <AppText className="text-xs text-muted-foreground mb-1">
                  Min
                </AppText>
                <AppText className="text-base font-medium text-foreground">
                  ${parseInt(minPrice).toLocaleString()}
                </AppText>
              </View>
              
              <View className="flex-1 rounded-xl border border-border p-3">
                <AppText className="text-xs text-muted-foreground mb-1">
                  Max
                </AppText>
                <AppText className="text-base font-medium text-foreground">
                  ${parseInt(maxPrice).toLocaleString()}
                </AppText>
              </View>
            </View>
            
            <View className="gap-3">
              <View className="h-2 bg-muted rounded-full overflow-hidden">
                <View 
                  className="h-full bg-primary rounded-full" 
                  style={{ width: '40%' }}
                />
              </View>
              <View className="h-2 bg-muted rounded-full overflow-hidden">
                <View 
                  className="h-full bg-primary rounded-full" 
                  style={{ width: '70%' }}
                />
              </View>
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="mb-4">
            <View className="flex-row items-center gap-2 mb-3">
              <Feather name="home" size={16} color={colors.foreground} />
              <AppText className="text-sm font-medium text-foreground">
                Property type
              </AppText>
            </View>
            
            <View className="flex-row flex-wrap gap-2">
              {['house', 'apartment', 'townhouse', 'unit'].map((type) => (
                <Button
                  key={type}
                  size="sm"
                  variant={propertyType === type ? 'default' : 'outline'}
                  onPress={() => setPropertyType(type)}
                  className="rounded-lg"
                >
                  <Button.LabelContent classNames={{ text: 'text-xs capitalize' }}>
                    {type}
                  </Button.LabelContent>
                </Button>
              ))}
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="mb-4">
            <View className="grid grid-cols-2 gap-4">
              <View>
                <View className="flex-row items-center gap-2 mb-3">
                  <Feather name="bed" size={16} color={colors.foreground} />
                  <AppText className="text-sm font-medium text-foreground">
                    Bedrooms
                  </AppText>
                </View>
                
                <View className="flex-row gap-2">
                  {[2, 3, 4].map((num) => (
                    <Button
                      key={num}
                      size="sm"
                      variant={bedrooms === num ? 'default' : 'outline'}
                      onPress={() => setBedrooms(num)}
                      className="rounded-lg"
                    >
                      <Button.LabelContent classNames={{ text: 'text-xs' }}>
                        {num}+
                      </Button.LabelContent>
                    </Button>
                  ))}
                </View>
              </View>
              
              <View>
                <View className="flex-row items-center gap-2 mb-3">
                  <Feather name="bath" size={16} color={colors.foreground} />
                  <AppText className="text-sm font-medium text-foreground">
                    Bathrooms
                  </AppText>
                </View>
                
                <View className="flex-row gap-2">
                  {[1, 2, 3].map((num) => (
                    <Button
                      key={num}
                      size="sm"
                      variant={bathrooms === num ? 'default' : 'outline'}
                      onPress={() => setBathrooms(num)}
                      className="rounded-lg"
                    >
                      <Button.LabelContent classNames={{ text: 'text-xs' }}>
                        {num}+
                      </Button.LabelContent>
                    </Button>
                  ))}
                </View>
              </View>
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="mb-4">
            <View className="flex-row items-center gap-2 mb-3">
              <Feather name="sparkles" size={16} color={colors.foreground} />
              <AppText className="text-sm font-medium text-foreground">
                Extras
              </AppText>
            </View>
            
            <View className="gap-3">
              <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
                <AppText className="text-sm text-foreground">
                  Garage
                </AppText>
                <Button
                  size="sm"
                  variant={hasGarage ? 'default' : 'outline'}
                  onPress={() => setHasGarage(!hasGarage)}
                  className="rounded-full w-12 h-6"
                >
                  <Button.LabelContent>
                    {hasGarage ? 'Yes' : 'No'}
                  </Button.LabelContent>
                </Button>
              </View>
              
              <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
                <AppText className="text-sm text-foreground">
                  Outdoor area
                </AppText>
                <Button
                  size="sm"
                  variant={hasOutdoor ? 'default' : 'outline'}
                  onPress={() => setHasOutdoor(!hasOutdoor)}
                  className="rounded-full w-12 h-6"
                >
                  <Button.LabelContent>
                    {hasOutdoor ? 'Yes' : 'No'}
                  </Button.LabelContent>
                </Button>
              </View>
            </View>
          </View>
        </Card>
        
        <Button
          size="lg"
          onPress={simulatePress}
          className="rounded-xl bg-primary"
        >
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            Apply 23 results
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default FiltersScreen;
