import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const FinanceTimelineScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  const timelineSteps = [
    {
      title: "Learn: Hearth Academy",
      description: "Modules 1–3 completed",
      status: "done",
      icon: "graduation-cap",
      iconColor: colors.emerald[500],
    },
    {
      title: "Prepare: Docs uploaded",
      description: "Payslip (1 remaining), bank statements",
      status: "in-progress",
      icon: "folder-check",
      iconColor: colors.sky[500],
    },
    {
      title: "Invite broker",
      description: "Ava invited, awaiting acceptance",
      status: "in-progress",
      icon: "user-plus",
      iconColor: colors.blue[500],
    },
    {
      title: "Review & submit",
      description: "Final checks complete",
      status: "ready",
      icon: "clipboard-check",
      iconColor: colors.amber[500],
    },
    {
      title: "Lender review",
      description: "Assessment underway",
      status: "pending",
      icon: "hourglass",
      iconColor: colors.purple[500],
    },
    {
      title: "Conditional approval",
      description: "Subject to valuation and LMI",
      status: "pending",
      icon: "badge-check",
      iconColor: colors.emerald[500],
    },
  ];

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/review-submit')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Finance Timeline
          </AppText>
          
          <AppText className="text-sm text-muted-foreground">
            ETA ~7–10d
          </AppText>
        </View>
        
        <View className="gap-5">
          {timelineSteps.map((step, index) => (
            <View key={index} className="flex-row gap-3">
              <View className="items-center">
                <View 
                  className="w-4 h-4 rounded-full border-2" 
                  style={{ 
                    backgroundColor: step.status === 'done' ? colors.emerald[500] : 
                                   step.status === 'in-progress' ? colors.sky[500] : 
                                   step.status === 'ready' ? colors.blue[500] : 
                                   colors.muted,
                    borderColor: step.status === 'done' ? colors.emerald[500] : 
                               step.status === 'in-progress' ? colors.sky[500] : 
                               step.status === 'ready' ? colors.blue[500] : 
                               colors.mutedForeground
                  }}
                />
                {index < timelineSteps.length - 1 && (
                  <View className="w-0.5 flex-1 bg-muted mt-1" />
                )}
              </View>
              
              <Card className="flex-1 rounded-xl bg-card border border-border">
                <View className="flex-row items-center justify-between mb-2">
                  <View className="flex-row items-center gap-2">
                    <Feather 
                      name={step.icon as any} 
                      size={16} 
                      color={step.iconColor} 
                    />
                    <AppText className="text-sm font-medium text-foreground">
                      {step.title}
                    </AppText>
                  </View>
                  <AppText className="text-xs" style={{ 
                    color: step.status === 'done' ? colors.emerald[500] : 
                          step.status === 'in-progress' ? colors.sky[500] : 
                          step.status === 'ready' ? colors.blue[500] : 
                          colors.mutedForeground
                  }}>
                    {step.status === 'done' ? 'Done' : 
                     step.status === 'in-progress' ? 'In progress' : 
                     step.status === 'ready' ? 'Ready' : 
                     'Pending'}
                  </AppText>
                </View>
                <AppText className="text-xs text-muted-foreground">
                  {step.description}
                </AppText>
              </Card>
            </View>
          ))}
        </View>
        
        <Button
          size="lg"
          onPress={() => router.push('/showcases/lender-offers')}
          className="rounded-xl bg-primary mt-6"
        >
          <Button.StartContent>
            <Feather name="scale" size={16} color="white" />
          </Button.StartContent>
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            View lender offers
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default FinanceTimelineScreen;