import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Button, Card, useTheme } from 'heroui-native';
import type { ComponentProps } from 'react';
import { useCallback, useRef } from 'react';
import { Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';
import { useRouter } from 'expo-router';

type FeatherIconName = ComponentProps<typeof Feather>['name'];

const DocumentVault = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');

      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-black">
      <ExpoLinearGradient
        colors={['rgba(15,15,15,0.98)', 'rgba(10,10,10,0.94)']}
        style={StyleSheet.absoluteFill}
      />
      <ExpoLinearGradient
        colors={['rgba(56,189,248,0.08)', 'transparent']}
        start={{ x: 0.1, y: 0 }}
        end={{ x: 0.9, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 20,
          paddingBottom: insets.bottom + 32,
          paddingHorizontal: 16,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="gap-5">
          {/* Header */}
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center gap-2">
              <Button
                size="sm"
                variant="ghost"
                isIconOnly
                onPress={() => router.push('/showcases')}
                className="rounded-xl"
              >
                <Button.LabelContent>
                  <Feather name="arrow-left" size={18} color="white" />
                </Button.LabelContent>
              </Button>
              <AppText className="text-[17px] tracking-tight font-semibold text-white">
                Document Vault
              </AppText>
            </View>
            <Button
              size="sm"
              isIconOnly
              onPress={simulatePress}
              className="w-9 h-9 rounded-xl bg-white/5 border border-white/10"
            >
              <Button.LabelContent>
                <Feather name="plus" size={18} color={colors.mutedForeground} />
              </Button.LabelContent>
            </Button>
          </View>

          {/* Status */}
          <Card className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center gap-2">
                <Feather name="shield-check" size={16} color="#7dd3fc" />
                <AppText className="text-sm font-medium text-white">
                  Verification status
                </AppText>
              </View>
              <View className="px-2 py-0.5 rounded-full bg-sky-400 text-black text-[11px] font-semibold">
                On track
              </View>
            </View>
            <View className="mt-3 w-full h-1.5 bg-neutral-800 rounded-full overflow-hidden">
              <View className="h-full bg-sky-400 rounded-full" style={{ width: '68%' }} />
            </View>
            <AppText className="mt-2 text-[12px] text-neutral-400">
              8/12 required docs uploaded
            </AppText>
          </Card>

          {/* Documents List */}
          <View className="gap-2">
            <Pressable
              onPress={simulatePress}
              className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between hover:border-neutral-700 transition"
            >
              <View className="flex-row items-center gap-2.5">
                <View className="w-8 h-8 rounded-xl bg-blue-500/15 border border-blue-400/20 text-blue-300 flex items-center justify-center">
                  <Feather name="id-card" size={16} color="#60a5fa" />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-white">
                    Primary ID
                  </AppText>
                  <AppText className="text-xs text-neutral-400">
                    Passport.pdf • 420 KB
                  </AppText>
                </View>
              </View>
              <AppText className="text-[11px] text-emerald-300">
                Verified
              </AppText>
            </Pressable>

            <Pressable
              onPress={simulatePress}
              className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between hover:border-neutral-700 transition"
            >
              <View className="flex-row items-center gap-2.5">
                <View className="w-8 h-8 rounded-xl bg-amber-500/15 border border-amber-400/20 text-amber-300 flex items-center justify-center">
                  <Feather name="file-text" size={16} color="#fbbf24" />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-white">
                    Payslips (last 3)
                  </AppText>
                  <AppText className="text-xs text-neutral-400">
                    2/3 uploaded • Pending
                  </AppText>
                </View>
              </View>
              <Feather name="chevron-right" size={16} color="#737373" />
            </Pressable>

            <Pressable
              onPress={simulatePress}
              className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between hover:border-neutral-700 transition"
            >
              <View className="flex-row items-center gap-2.5">
                <View className="w-8 h-8 rounded-xl bg-rose-500/15 border border-rose-400/20 text-rose-300 flex items-center justify-center">
                  <Feather name="banknote" size={16} color="#f43f5e" />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-white">
                    Bank statements
                  </AppText>
                  <AppText className="text-xs text-neutral-400">
                    Last 90 days • PDF
                  </AppText>
                </View>
              </View>
              <Feather name="chevron-right" size={16} color="#737373" />
            </Pressable>

            <Pressable
              onPress={simulatePress}
              className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between hover:border-neutral-700 transition"
            >
              <View className="flex-row items-center gap-2.5">
                <View className="w-8 h-8 rounded-xl bg-cyan-500/15 border border-cyan-400/20 text-cyan-300 flex items-center justify-center">
                  <Feather name="file-check-2" size={16} color="#22d3ee" />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-white">
                    Employment letter
                  </AppText>
                  <AppText className="text-xs text-neutral-400">
                    Optional • 180 KB
                  </AppText>
                </View>
              </View>
              <AppText className="text-[11px] text-neutral-400">
                Not provided
              </AppText>
            </Pressable>
          </View>

          {/* Footer */}
          <View className="pt-3">
            <View className="flex-row items-center gap-2">
              <Button
                size="md"
                onPress={simulatePress}
                className="flex-1 rounded-xl bg-white/10 border border-white/20"
              >
                <Button.StartContent>
                  <Feather name="upload-cloud" size={16} color="white" />
                </Button.StartContent>
                <Button.LabelContent classNames={{ text: 'text-sm font-medium' }}>
                  Upload document
                </Button.LabelContent>
              </Button>
              <Button
                size="sm"
                isIconOnly
                onPress={simulatePress}
                className="w-11 h-9 rounded-xl bg-white/5 border border-white/10"
              >
                <Button.LabelContent>
                  <Feather name="help-circle" size={16} color={colors.mutedForeground} />
                </Button.LabelContent>
              </Button>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default DocumentVault;