import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Button, Card, useTheme } from 'heroui-native';
import type { ComponentProps } from 'react';
import { useCallback, useRef } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText } from '../../../components/app-text';
import { useRouter } from 'expo-router';
import { simulatePress } from '../../../helpers/utils/simulate-press';

type FeatherIconName = ComponentProps<typeof Feather>['name'];

const Affordability = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');

      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-black">
      <ExpoLinearGradient
        colors={['rgba(15,15,15,0.98)', 'rgba(10,10,10,0.94)']}
        style={StyleSheet.absoluteFill}
      />
      <ExpoLinearGradient
        colors={['rgba(56,189,248,0.08)', 'transparent']}
        start={{ x: 0.1, y: 0 }}
        end={{ x: 0.9, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 20,
          paddingBottom: insets.bottom + 32,
          paddingHorizontal: 16,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="gap-5">
          {/* Header */}
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center gap-2">
              <Button
                size="sm"
                isIconOnly
                onPress={() => router.push('/showcases')}
                className="rounded-xl bg-white/5 border border-white/10"
              >
                <Button.LabelContent>
                  <Feather
                    name="arrow-left"
                    size={18}
                    color={colors.mutedForeground}
                  />
                </Button.LabelContent>
              </Button>
              <AppText className="text-[17px] tracking-tight font-semibold text-white">
                Affordability
              </AppText>
            </View>
            <Button
              size="sm"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl bg-white/5 border border-white/10"
            >
              <Button.LabelContent>
                <Feather
                  name="ellipsis"
                  size={18}
                  color={colors.mutedForeground}
                />
              </Button.LabelContent>
            </Button>
          </View>

          {/* Summary Card */}
          <Card className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
            <View className="flex-row items-center justify-between mb-2.5">
              <View className="flex-row items-center gap-2">
                <Feather name="wallet" size={16} color="#7dd3fc" />
                <AppText className="text-sm font-medium text-white">
                  Your range
                </AppText>
              </View>
              <AppText className="text-xs text-neutral-300">
                Updated 2m ago
              </AppText>
            </View>
            <View className="flex-row gap-3">
              <Card className="flex-1 rounded-xl bg-black/30 border border-white/10 p-3">
                <AppText className="text-[11px] text-neutral-400 mb-1">
                  Target
                </AppText>
                <AppText className="text-base font-semibold text-white">
                  $720k–$840k
                </AppText>
              </Card>
              <Card className="flex-1 rounded-xl bg-black/30 border border-white/10 p-3">
                <AppText className="text-[11px] text-neutral-400 mb-1">
                  Rate
                </AppText>
                <AppText className="text-base font-semibold text-white">
                  6.10% p.a.
                </AppText>
              </Card>
            </View>
          </Card>

          {/* Deposit Progress */}
          <Card className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
            <View className="flex-row items-center justify-between mb-2">
              <View className="flex-row items-center gap-2">
                <Feather name="piggy-bank" size={16} color="#60a5fa" />
                <AppText className="text-sm font-medium text-white">
                  Deposit saved
                </AppText>
              </View>
              <AppText className="text-xs text-neutral-300">
                $84k / $120k
              </AppText>
            </View>
            <View className="w-full h-2 bg-neutral-800 rounded-full overflow-hidden">
              <View
                className="h-full bg-blue-400 rounded-full"
                style={{ width: '70%' }}
              />
            </View>
            <View className="mt-3 flex-row gap-2">
              <Card className="flex-1 rounded-lg bg-black/30 border border-white/10 p-2">
                <AppText className="text-neutral-400 text-xs">LVR</AppText>
                <AppText className="font-medium text-white text-xs">
                  88%
                </AppText>
              </Card>
              <Card className="flex-1 rounded-lg bg-black/30 border border-white/10 p-2">
                <AppText className="text-neutral-400 text-xs">LMI</AppText>
                <AppText className="font-medium text-white text-xs">
                  Likely
                </AppText>
              </Card>
              <Card className="flex-1 rounded-lg bg-black/30 border border-white/10 p-2">
                <AppText className="text-neutral-400 text-xs">Buffer</AppText>
                <AppText className="font-medium text-white text-xs">
                  3 mo
                </AppText>
              </Card>
            </View>
          </Card>

          {/* Repayments */}
          <Card className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
            <View className="flex-row items-center justify-between mb-2">
              <View className="flex-row items-center gap-2">
                <Feather name="calendar" size={16} color="#fbbf24" />
                <AppText className="text-sm font-medium text-white">
                  Estimated repayments
                </AppText>
              </View>
              <AppText className="text-xs text-neutral-300">
                P&amp;I, 30y
              </AppText>
            </View>
            <View className="flex-row gap-3">
              <Card className="flex-1 rounded-xl bg-black/30 border border-white/10 p-3">
                <AppText className="text-[11px] text-neutral-400 mb-1">
                  Monthly
                </AppText>
                <AppText className="text-base font-semibold text-white">
                  $3,480
                </AppText>
              </Card>
              <Card className="flex-1 rounded-xl bg-black/30 border border-white/10 p-3">
                <AppText className="text-[11px] text-neutral-400 mb-1">
                  Weekly
                </AppText>
                <AppText className="text-base font-semibold text-white">
                  $803
                </AppText>
              </Card>
            </View>
            <Card className="mt-3 rounded-xl bg-black/30 border border-white/10 p-2">
              <View className="flex-row items-center justify-between mb-1">
                <AppText className="text-xs text-neutral-300">
                  Savings over time
                </AppText>
                <AppText className="text-xs text-neutral-400">12 mo</AppText>
              </View>
              <View className="h-16 items-center justify-center">
                <AppText className="text-neutral-400 text-xs">
                  Savings chart placeholder
                </AppText>
              </View>
            </Card>
          </Card>

          {/* Footer */}
          <View className="pt-3">
            <Button
              size="md"
              onPress={simulatePress}
              className="rounded-xl bg-white/10 border border-white/20"
            >
              <Button.StartContent>
                <Feather name="sparkles" size={16} color="white" />
              </Button.StartContent>
              <Button.LabelContent classNames={{
                text: 'text-sm font-medium'
              }}>
                Improve affordability tips
              </Button.LabelContent>
            </Button>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default Affordability;