import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { <PERSON>ton, Card, useTheme } from 'heroui-native';
import { useCallback, useRef, useState } from 'react';
import { ScrollView, View, TextInput } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const MessagesScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);
  
  const [message, setMessage] = useState('');

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  const messages = [
    {
      id: '1',
      sender: 'Ava',
      content: "Hey! I reviewed your affordability—looking great. Ready to move to pre-approval?",
      timestamp: '09:10',
      isUser: false,
    },
    {
      id: '2',
      sender: 'You',
      content: "Yes, what documents do you still need from me?",
      timestamp: '09:14',
      isUser: true,
    },
    {
      id: '3',
      sender: 'Ava',
      content: "Just the last payslip and your bank statements. You can add them in Vault.",
      timestamp: '09:16',
      isUser: false,
    },
  ];

  return (
    <View className="flex-1 bg-background">
      <View 
        className="flex-1"
        style={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 12,
        }}
      >
        <View className="flex-row items-center justify-between px-4 pb-4">
          <View className="flex-row items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={() => router.push('/showcases/lender-offers')}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="arrow-left" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
            
            <View>
              <AppText className="text-xs text-muted-foreground">
                Hearth Pro
              </AppText>
              <AppText className="text-base font-semibold text-foreground">
                Ava Reed
              </AppText>
            </View>
          </View>
          
          <View className="flex-row gap-2">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="phone" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="video" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
          </View>
        </View>
        
        <ScrollView 
          className="flex-1 px-4"
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          <View className="gap-4">
            {messages.map((msg) => (
              <View 
                key={msg.id} 
                className={`flex-row gap-2 ${msg.isUser ? 'justify-end' : 'justify-start'}`}
              >
                {!msg.isUser && (
                  <View className="w-8 h-8 rounded-full border border-border flex items-center justify-center">
                    <Feather name="user" size={16} color={colors.foreground} />
                  </View>
                )}
                
                <Card 
                  className={`max-w-[75%] rounded-2xl ${msg.isUser ? 'bg-primary rounded-tr-sm' : 'bg-card border border-border rounded-tl-sm'}`}
                >
                  <AppText className={`text-xs mb-1 ${msg.isUser ? 'text-primary-foreground' : 'text-muted-foreground'}`} style={{ textAlign: msg.isUser ? 'right' : 'left' }}>
                    {msg.sender} • {msg.timestamp}
                  </AppText>
                  <AppText className={`text-sm ${msg.isUser ? 'text-primary-foreground' : 'text-foreground'}`}>
                    {msg.content}
                  </AppText>
                </Card>
                
                {msg.isUser && (
                  <View className="w-8 h-8 rounded-full border border-border flex items-center justify-center">
                    <Feather name="user" size={16} color={colors.foreground} />
                  </View>
                )}
              </View>
            ))}
          </View>
        </ScrollView>
        
        <View className="px-4 pt-3">
          <View className="flex-row items-center gap-2 bg-card border border-border rounded-2xl p-2">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="plus" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
            
            <TextInput
              value={message}
              onChangeText={setMessage}
              placeholder="Message Ava..."
              className="flex-1 bg-transparent text-sm text-foreground placeholder:text-muted-foreground px-2 py-1"
            />
            
            <Button
              size="sm"
              onPress={simulatePress}
              className="rounded-xl bg-primary"
            >
              <Button.LabelContent>
                <Feather name="send" size={18} color="white" />
              </Button.LabelContent>
            </Button>
          </View>
        </View>
      </View>
    </View>
  );
};

export default MessagesScreen;