import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const UploadDocumentScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);
  const [documentType, setDocumentType] = useState('payslip');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  const documentTypes = [
    { value: 'primary-id', label: 'Primary ID' },
    { value: 'secondary-id', label: 'Secondary ID' },
    { value: 'payslip', label: 'Payslip' },
    { value: 'bank-statement', label: 'Bank statement' },
    { value: 'employment-letter', label: 'Employment letter' },
  ];

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/document-vault')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Upload Document
          </AppText>
          
          <AppText className="text-sm text-muted-foreground">
            Secure
          </AppText>
        </View>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View>
            <AppText className="text-xs text-muted-foreground mb-2">
              Document type
            </AppText>
            
            <View className="rounded-xl border border-border">
              {documentTypes.map((type) => (
                <Button
                  key={type.value}
                  size="md"
                  variant={documentType === type.value ? 'default' : 'ghost'}
                  onPress={() => setDocumentType(type.value)}
                  className="w-full justify-start rounded-none first:rounded-t-xl last:rounded-b-xl border-b border-border last:border-b-0"
                >
                  <Button.LabelContent classNames={{ text: 'text-sm' }}>
                    {type.label}
                  </Button.LabelContent>
                </Button>
              ))}
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="rounded-xl border border-dashed border-border bg-muted/20 p-6 items-center justify-center">
            <Feather name="upload-cloud" size={24} color={colors.primary} />
            <AppText className="text-base font-medium text-foreground my-2">
              Drag & drop files here
            </AppText>
            <AppText className="text-xs text-muted-foreground mb-4">
              PDF, JPG, PNG up to 10MB
            </AppText>
            <Button
              size="sm"
              variant="outline"
              onPress={simulatePress}
              className="rounded-lg border-border"
            >
              <Button.StartContent>
                <Feather name="file-plus" size={16} color={colors.foreground} />
              </Button.StartContent>
              <Button.LabelContent classNames={{ text: 'text-xs text-foreground' }}>
                Browse files
              </Button.LabelContent>
            </Button>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View>
            <AppText className="text-sm font-medium text-foreground mb-3">
              Selected
            </AppText>
            
            {selectedFiles.length > 0 ? (
              <View className="gap-2">
                {selectedFiles.map((file, index) => (
                  <View 
                    key={index} 
                    className="flex-row items-center justify-between rounded-lg border border-border p-3"
                  >
                    <View className="flex-row items-center gap-2">
                      <Feather name="file" size={16} color={colors.foreground} />
                      <AppText className="text-sm text-foreground">
                        Payslip_May.pdf • 380 KB
                      </AppText>
                    </View>
                    <Button
                      size="sm"
                      variant="ghost"
                      isIconOnly
                      onPress={simulatePress}
                      className="rounded-lg"
                    >
                      <Button.LabelContent>
                        <Feather name="x" size={16} color={colors.foreground} />
                      </Button.LabelContent>
                    </Button>
                  </View>
                ))}
              </View>
            ) : (
              <AppText className="text-sm text-muted-foreground">
                No files selected
              </AppText>
            )}
          </View>
        </Card>
        
        <Button
          size="lg"
          onPress={simulatePress}
          className="rounded-xl bg-primary"
          disabled={selectedFiles.length === 0}
        >
          <Button.StartContent>
            <Feather name="check-circle" size={16} color="white" />
          </Button.StartContent>
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            Upload and verify
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default UploadDocumentScreen;
