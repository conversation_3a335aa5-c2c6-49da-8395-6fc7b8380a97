import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef, useState } from 'react';
import { ScrollView, View, TextInput } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const RequestIntroScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [message, setMessage] = useState('');
  const [preferredTime, setPreferredTime] = useState('morning');

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/open-to-offers')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Request Intro
          </AppText>
          
          <AppText className="text-sm text-muted-foreground">
            1–2 min
          </AppText>
        </View>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="gap-4">
            <View>
              <AppText className="text-xs text-muted-foreground mb-2">
                Full name
              </AppText>
              <TextInput
                value={fullName}
                onChangeText={setFullName}
                placeholder="Alex Taylor"
                className="w-full rounded-xl border border-border px-4 py-3 text-sm bg-background text-foreground"
              />
            </View>
            
            <View>
              <AppText className="text-xs text-muted-foreground mb-2">
                Email
              </AppText>
              <TextInput
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                keyboardType="email-address"
                className="w-full rounded-xl border border-border px-4 py-3 text-sm bg-background text-foreground"
              />
            </View>
            
            <View>
              <AppText className="text-xs text-muted-foreground mb-2">
                Phone
              </AppText>
              <TextInput
                value={phone}
                onChangeText={setPhone}
                placeholder="+61 4xx xxx xxx"
                keyboardType="phone-pad"
                className="w-full rounded-xl border border-border px-4 py-3 text-sm bg-background text-foreground"
              />
            </View>
            
            <View>
              <AppText className="text-xs text-muted-foreground mb-2">
                Message
              </AppText>
              <TextInput
                value={message}
                onChangeText={setMessage}
                placeholder="Say hello and share your interest..."
                multiline
                numberOfLines={3}
                className="w-full rounded-xl border border-border px-4 py-3 text-sm bg-background text-foreground h-24"
              />
            </View>
            
            <View>
              <AppText className="text-xs text-muted-foreground mb-3">
                Preferred time
              </AppText>
              <View className="flex-row gap-2">
                {['morning', 'afternoon', 'evening'].map((time) => (
                  <Button
                    key={time}
                    size="sm"
                    variant={preferredTime === time ? 'default' : 'outline'}
                    onPress={() => setPreferredTime(time)}
                    className="flex-1 rounded-xl"
                  >
                    <Button.LabelContent classNames={{ text: 'text-xs capitalize' }}>
                      {time}
                    </Button.LabelContent>
                  </Button>
                ))}
              </View>
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="flex-row items-center gap-2">
            <Feather name="shield" size={16} color={colors.primary} />
            <AppText className="text-xs text-muted-foreground">
              Your details are shared securely with the listing agent.
            </AppText>
          </View>
        </Card>
        
        <Button
          size="lg"
          onPress={simulatePress}
          className="rounded-xl bg-primary"
        >
          <Button.StartContent>
            <Feather name="send" size={16} color="white" />
          </Button.StartContent>
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            Send request
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default RequestIntroScreen;
