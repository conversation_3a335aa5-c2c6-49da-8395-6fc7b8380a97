import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Button, Card, Chip, useTheme } from 'heroui-native';
import type { ComponentProps } from 'react';
import { useCallback, useRef, useState } from 'react';
import {
  ImageBackground,
  Pressable,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';
import { useRouter } from 'expo-router';

const featuredPropertyImage =
  'https://images.unsplash.com/photo-1575517111478-7f6f2c94b3b5?q=80&w=1600&auto=format&fit=crop';

type FeatherIconName = ComponentProps<typeof Feather>['name'];

const OpenToOffers = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-black">
      <ExpoLinearGradient
        colors={['rgba(15,15,15,0.98)', 'rgba(10,10,10,0.94)']}
        style={StyleSheet.absoluteFill}
      />
      <ExpoLinearGradient
        colors={['rgba(56,189,248,0.08)', 'transparent']}
        start={{ x: 0.1, y: 0 }}
        end={{ x: 0.9, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 60,
          paddingHorizontal: 12,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="gap-5">
          {/* Header */}
          <View className="flex-row items-center justify-between">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={() => router.push('/showcases')}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="arrow-left" size={18} color="white" />
              </Button.LabelContent>
            </Button>
            <AppText className="text-[17px] tracking-tight font-semibold text-white">
              Open to Offers
            </AppText>
            <View className="flex-row items-center gap-2.5">
              <Button
                size="sm"
                isIconOnly
                onPress={simulatePress}
                className="rounded-xl bg-white/5 border border-white/10"
              >
                <Button.LabelContent>
                  <Feather name="search" size={18} color={colors.mutedForeground} />
                </Button.LabelContent>
              </Button>
              <Pressable
                onPress={simulatePress}
                className="w-9 h-9 rounded-xl bg-white/5 border border-white/10 flex items-center justify-center"
              >
                <Feather name="sliders-horizontal" size={18} color={colors.mutedForeground} />
              </Pressable>
            </View>
          </View>

          {/* Featured Property */}
          <Card className="rounded-2xl overflow-hidden border border-white/10">
            <ImageBackground
              source={{ uri: featuredPropertyImage }}
              resizeMode="cover"
              className="h-44"
            >
              <ExpoLinearGradient
                colors={['rgba(0,0,0,0.8)', 'transparent']}
                style={StyleSheet.absoluteFill}
              />
              <View className="flex-1 justify-between p-4">
                <View className="flex-row items-center justify-between">
                  <Chip size="sm" className="bg-sky-400/20 border border-sky-300/30">
                    <Chip.LabelContent classNames={{ text: 'text-sky-100 text-[11px] font-medium' }}>
                      Featured
                    </Chip.LabelContent>
                  </Chip>
                  <View className="flex-row items-center gap-1">
                    <Feather name="map-pin" size={14} color="rgba(255,255,255,0.9)" />
                    <AppText className="text-[12px] text-white/90">
                      Melbourne 3000
                    </AppText>
                  </View>
                </View>
                <View>
                  <AppText className="text-[15px] font-medium text-white mb-1">
                    Owner's guide: $950k–$1.02m
                  </AppText>
                  <AppText className="text-[12px] text-neutral-200 mb-2.5">
                    3 bed • 2 bath • 1 car • 220m²
                  </AppText>
                  <View className="flex-row items-center gap-2">
                    <Button
                      size="sm"
                      onPress={simulatePress}
                      className="rounded-xl bg-white/10 border border-white/20"
                    >
                      <Button.StartContent>
                        <Feather name="send" size={16} color="white" />
                      </Button.StartContent>
                      <Button.LabelContent classNames={{ text: 'text-[12px] font-medium text-white' }}>
                        Request intro
                      </Button.LabelContent>
                    </Button>
                    <Button
                      variant="secondary"
                      size="sm"
                      onPress={simulatePress}
                      className="rounded-xl bg-white/5 border border-white/10"
                    >
                      <Button.StartContent>
                        <Feather name="bookmark" size={16} color={colors.mutedForeground} />
                      </Button.StartContent>
                      <Button.LabelContent classNames={{ text: 'text-[12px] text-white' }}>
                        Save
                      </Button.LabelContent>
                    </Button>
                  </View>
                </View>
              </View>
            </ImageBackground>
          </Card>

          {/* Browse Section */}
          <View>
            <AppText className="text-sm font-medium text-white mb-3">
              Browse
            </AppText>
            <View className="gap-2.5">
              <Pressable
                onPress={simulatePress}
                className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between"
              >
                <View className="flex-row items-center gap-2.5">
                  <View className="w-9 h-9 bg-blue-500/15 rounded-xl border border-blue-400/20 flex items-center justify-center">
                    <Feather name="wallet" size={18} color="#60a5fa" />
                  </View>
                  <View>
                    <AppText className="text-sm font-medium text-white">
                      Within your budget
                    </AppText>
                    <AppText className="text-xs text-neutral-400">
                      23 matches • Personalised
                    </AppText>
                  </View>
                </View>
                <Feather name="chevron-right" size={16} color="#737373" />
              </Pressable>

              <Pressable
                onPress={simulatePress}
                className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between"
              >
                <View className="flex-row items-center gap-2.5">
                  <View className="w-9 h-9 bg-amber-500/15 rounded-xl border border-amber-400/20 flex items-center justify-center">
                    <Feather name="radar" size={18} color="#fbbf24" />
                  </View>
                  <View>
                    <AppText className="text-sm font-medium text-white">
                      New signals in 3000
                    </AppText>
                    <AppText className="text-xs text-neutral-400">
                      7 homeowners updated price
                    </AppText>
                  </View>
                </View>
                <Feather name="chevron-right" size={16} color="#737373" />
              </Pressable>

              <Pressable
                onPress={simulatePress}
                className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3 flex-row items-center justify-between"
              >
                <View className="flex-row items-center gap-2.5">
                  <View className="w-9 h-9 bg-cyan-500/15 rounded-xl border border-cyan-400/20 flex items-center justify-center">
                    <Feather name="shield-check" size={18} color="#22d3ee" />
                  </View>
                  <View>
                    <AppText className="text-sm font-medium text-white">
                      Agent oversight
                    </AppText>
                    <AppText className="text-xs text-neutral-400">
                      HAUS Pro verified
                    </AppText>
                  </View>
                </View>
                <Feather name="chevron-right" size={16} color="#737373" />
              </Pressable>
            </View>
          </View>

          {/* Demand Trigger Progress */}
          <Card className="bg-neutral-900 border border-neutral-800 rounded-2xl p-3.5">
            <View className="flex-row items-center justify-between mb-2.5">
              <View className="flex-row items-center gap-2">
                <Feather name="flag" size={16} color="#22d3ee" />
                <AppText className="text-sm font-medium text-white">
                  Phase 2 Demand Trigger
                </AppText>
              </View>
              <AppText className="text-[11px] text-neutral-300">
                Postcode 3000
              </AppText>
            </View>

            <View className="gap-3">
              <View>
                <View className="flex-row items-center justify-between text-xs mb-1.5">
                  <AppText className="text-neutral-400">
                    Academy graduates
                  </AppText>
                  <AppText className="font-medium text-white">
                    842/1000
                  </AppText>
                </View>
                <View className="w-full h-2 bg-neutral-800 rounded-full overflow-hidden">
                  <View className="h-full bg-sky-400 rounded-full" style={{ width: '84%' }} />
                </View>
              </View>
              <View>
                <View className="flex-row items-center justify-between text-xs mb-1.5">
                  <AppText className="text-neutral-400">
                    Active "Open to Offers"
                  </AppText>
                  <AppText className="font-medium text-white">
                    412/500
                  </AppText>
                </View>
                <View className="w-full h-2 bg-neutral-800 rounded-full overflow-hidden">
                  <View className="h-full bg-blue-400 rounded-full" style={{ width: '82%' }} />
                </View>
              </View>
            </View>

            <View className="mt-3 flex-row items-center gap-2">
              <Feather name="info" size={14} color="#737373" />
              <AppText className="text-xs text-neutral-300">
                Unlock Agent Tools + Marketplace when both bars reach 100%.
              </AppText>
            </View>
          </Card>

          {/* Footer Actions */}
          <View className="pt-3">
            <View className="flex-row items-center justify-between gap-2">
              <Button
                size="md"
                onPress={simulatePress}
                className="flex-1 rounded-xl bg-white/10 border border-white/20"
              >
                <Button.StartContent>
                  <Feather name="message-square" size={16} color="white" />
                </Button.StartContent>
                <Button.LabelContent classNames={{ text: 'text-sm font-medium text-white' }}>
                  Talk to a HAUS Pro
                </Button.LabelContent>
              </Button>
              <Button
                size="sm"
                isIconOnly
                onPress={simulatePress}
                className="w-9 h-9 rounded-xl bg-white/5 border border-white/10"
              >
                <Button.LabelContent>
                  <Feather name="more-horizontal" size={16} color={colors.mutedForeground} />
                </Button.LabelContent>
              </Button>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default OpenToOffers;
