import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { <PERSON><PERSON>, Card, useTheme } from 'heroui-native';
import { useCallback, useRef } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const LenderOffersScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  const offers = [
    {
      lender: "BlueBank",
      loanType: "Variable • 30y • P&I",
      rate: "6.04%",
      comparisonRate: "6.21%",
      monthlyRepayment: "$3,440",
      recommended: true,
    },
    {
      lender: "Harbor Finance",
      loanType: "Fixed 2y • 30y • P&I",
      rate: "5.89%",
      comparisonRate: "6.35%",
      monthlyRepayment: "$3,510",
      recommended: false,
    },
    {
      lender: "Metro Mutual",
      loanType: "Variable • Offset",
      rate: "6.12%",
      comparisonRate: "6.28%",
      monthlyRepayment: "$3,470",
      recommended: false,
    },
  ];

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/finance-timeline')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Lender Offers
          </AppText>
          
          <View className="flex-row gap-2">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="arrow-up-down" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="sliders-horizontal" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
          </View>
        </View>
        
        <View className="gap-4">
          {offers.map((offer, index) => (
            <Card key={index} className="rounded-2xl bg-card border border-border">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-row items-center gap-2">
                  <View className="w-8 h-8 rounded-lg border border-border flex items-center justify-center">
                    <Feather name="landmark" size={16} color={colors.sky[500]} />
                  </View>
                  <View>
                    <AppText className="text-sm font-medium text-foreground">
                      {offer.lender}
                    </AppText>
                    <AppText className="text-xs text-muted-foreground">
                      {offer.loanType}
                    </AppText>
                  </View>
                </View>
                
                {offer.recommended && (
                  <View className="rounded-full bg-emerald-500/20 border border-emerald-500/30 px-3 py-1">
                    <AppText className="text-xs font-medium text-emerald-500">
                      Recommended
                    </AppText>
                  </View>
                )}
              </View>
              
              <View className="grid grid-cols-3 gap-3 mb-4">
                <View className="rounded-xl border border-border p-3">
                  <AppText className="text-xs text-muted-foreground mb-1">
                    Rate
                  </AppText>
                  <AppText className="text-base font-semibold text-foreground">
                    {offer.rate}
                  </AppText>
                </View>
                
                <View className="rounded-xl border border-border p-3">
                  <AppText className="text-xs text-muted-foreground mb-1">
                    Comp. rate
                  </AppText>
                  <AppText className="text-base font-semibold text-foreground">
                    {offer.comparisonRate}
                  </AppText>
                </View>
                
                <View className="rounded-xl border border-border p-3">
                  <AppText className="text-xs text-muted-foreground mb-1">
                    Monthly
                  </AppText>
                  <AppText className="text-base font-semibold text-foreground">
                    {offer.monthlyRepayment}
                  </AppText>
                </View>
              </View>
              
              <View className="flex-row gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onPress={simulatePress}
                  className="flex-1 rounded-lg border-border"
                >
                  <Button.LabelContent classNames={{ text: 'text-xs text-foreground' }}>
                    Compare
                  </Button.LabelContent>
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  isIconOnly
                  onPress={simulatePress}
                  className="rounded-lg"
                >
                  <Button.LabelContent>
                    <Feather name="bookmark" size={16} color={colors.foreground} />
                  </Button.LabelContent>
                </Button>
              </View>
            </Card>
          ))}
        </View>
        
        <Button
          size="lg"
          onPress={() => router.push('/showcases/messages')}
          className="rounded-xl bg-primary mt-6"
        >
          <Button.StartContent>
            <Feather name="message-square" size={16} color="white" />
          </Button.StartContent>
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            Ask a broker for guidance
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default LenderOffersScreen;