import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const ReviewSubmitScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);
  
  const [declarations, setDeclarations] = useState({
    confirmInfo: false,
    consentCreditCheck: false,
  });

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/readiness')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Review & Submit
          </AppText>
          
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={simulatePress}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="more-horizontal" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
        </View>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="flex-row items-center justify-between mb-4">
            <View className="flex-row items-center gap-2">
              <Feather name="file-badge" size={16} color={colors.primary} />
              <AppText className="text-sm font-medium text-foreground">
                Application summary
              </AppText>
            </View>
            <AppText className="text-xs text-muted-foreground">
              Draft
            </AppText>
          </View>
          
          <View className="grid grid-cols-2 gap-3">
            <View className="rounded-xl border border-border p-3">
              <AppText className="text-xs text-muted-foreground mb-1">
                Property range
              </AppText>
              <AppText className="text-base font-medium text-foreground">
                $720k–$840k
              </AppText>
            </View>
            
            <View className="rounded-xl border border-border p-3">
              <AppText className="text-xs text-muted-foreground mb-1">
                Deposit
              </AppText>
              <AppText className="text-base font-medium text-foreground">
                $84k (70%)
              </AppText>
            </View>
            
            <View className="rounded-xl border border-border p-3">
              <AppText className="text-xs text-muted-foreground mb-1">
                Loan type
              </AppText>
              <AppText className="text-base font-medium text-foreground">
                P&I • Variable
              </AppText>
            </View>
            
            <View className="rounded-xl border border-border p-3">
              <AppText className="text-xs text-muted-foreground mb-1">
                Term
              </AppText>
              <AppText className="text-base font-medium text-foreground">
                30 years
              </AppText>
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="flex-row items-center gap-2 mb-4">
            <Feather name="users" size={16} color={colors.emerald[500]} />
            <AppText className="text-sm font-medium text-foreground">
              Applicants
            </AppText>
          </View>
          
          <View className="gap-3">
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <View>
                <AppText className="text-sm font-medium text-foreground">
                  Alex Taylor
                </AppText>
                <AppText className="text-xs text-muted-foreground">
                  Primary • PAYG
                </AppText>
              </View>
              <Button
                size="sm"
                variant="ghost"
                isIconOnly
                onPress={simulatePress}
                className="rounded-lg"
              >
                <Button.LabelContent>
                  <Feather name="edit-3" size={16} color={colors.foreground} />
                </Button.LabelContent>
              </Button>
            </View>
            
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <View>
                <AppText className="text-sm font-medium text-foreground">
                  Sam Lee
                </AppText>
                <AppText className="text-xs text-muted-foreground">
                  Co-applicant • Contractor
                </AppText>
              </View>
              <Button
                size="sm"
                variant="ghost"
                isIconOnly
                onPress={simulatePress}
                className="rounded-lg"
              >
                <Button.LabelContent>
                  <Feather name="edit-3" size={16} color={colors.foreground} />
                </Button.LabelContent>
              </Button>
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="flex-row items-center gap-2 mb-4">
            <Feather name="shield-check" size={16} color={colors.blue[500]} />
            <AppText className="text-sm font-medium text-foreground">
              Declarations
            </AppText>
          </View>
          
          <View className="gap-3">
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <AppText className="text-sm text-foreground">
                I confirm information provided is true and correct.
              </AppText>
              <Button
                size="sm"
                variant={declarations.confirmInfo ? 'default' : 'outline'}
                onPress={() => setDeclarations(prev => ({ 
                  ...prev, 
                  confirmInfo: !prev.confirmInfo 
                }))}
                className="rounded-full w-12 h-6"
              >
                <Button.LabelContent>
                  {declarations.confirmInfo ? 'Yes' : 'No'}
                </Button.LabelContent>
              </Button>
            </View>
            
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <AppText className="text-sm text-foreground">
                I consent to a credit check with participating lenders.
              </AppText>
              <Button
                size="sm"
                variant={declarations.consentCreditCheck ? 'default' : 'outline'}
                onPress={() => setDeclarations(prev => ({ 
                  ...prev, 
                  consentCreditCheck: !prev.consentCreditCheck 
                }))}
                className="rounded-full w-12 h-6"
              >
                <Button.LabelContent>
                  {declarations.consentCreditCheck ? 'Yes' : 'No'}
                </Button.LabelContent>
              </Button>
            </View>
          </View>
        </Card>
        
        <Button
          size="lg"
          onPress={() => router.push('/showcases/finance-timeline')}
          className="rounded-xl bg-primary"
          disabled={!declarations.confirmInfo || !declarations.consentCreditCheck}
        >
          <Button.StartContent>
            <Feather name="send" size={16} color="white" />
          </Button.StartContent>
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            Submit to lenders
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default ReviewSubmitScreen;