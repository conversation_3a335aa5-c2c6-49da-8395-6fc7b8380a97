import Feather from '@expo/vector-icons/Feather';
import { useRouter } from 'expo-router';
import { Button, cn, Divider, useTheme } from 'heroui-native';
import { View } from 'react-native';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText } from '../../../components/app-text';
import MarqueeCarousel, {
  type CardProps,
} from '../../../components/showcases/onboarding/marquee-carousel';

const AnimatedView = Animated.createAnimatedComponent(View);

const cards: CardProps[] = [
  {
    title: 'North Loop Brownstone',
    image:
      'https://images.unsplash.com/photo-1570129477492-45c003edd2be?q=80&w=1600&auto=format&fit=crop',
    liveCount: 5,
    category: 'Minneapolis, MN',
    brands: 'Pre-market · 4 bd',
  },
  {
    title: "Sloan's Lake Townhome",
    image:
      'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?q=80&w=1600&auto=format&fit=crop',
    liveCount: 7,
    category: 'Denver, CO',
    brands: 'Tour-ready · 3 bd',
  },
  {
    title: 'Capitol Hill Condo',
    image:
      'https://images.unsplash.com/photo-1505691723518-36a5ac3be353?q=80&w=1600&auto=format&fit=crop',
    liveCount: 3,
    category: 'Seattle, WA',
    brands: 'Lock-and-leave · 2 bd',
  },
  {
    title: 'East Austin Bungalow',
    image:
      'https://images.unsplash.com/photo-1575517111478-7f6afd0973db?q=80&w=1600&auto=format&fit=crop',
    liveCount: 4,
    category: 'Austin, TX',
    brands: 'Coming soon · 3 bd',
  },
];

const OnboardingScreen = () => {
  const { isDark } = useTheme();

  const router = useRouter();

  const insets = useSafeAreaInsets();

  return (
    <View
      className="flex-1 bg-background"
      style={{
        paddingTop: insets.top + 12,
        paddingBottom: insets.bottom + 12,
      }}
    >
      <AnimatedView
        entering={FadeIn.duration(500)}
        className="px-4 flex-row items-center justify-between"
      >
        <Button
          size="sm"
          className={cn('rounded-full bg-black/20', isDark && 'bg-white/20')}
          isIconOnly
          onPress={router.back}
        >
          <Button.LabelContent>
            <Feather
              name="chevron-left"
              size={24}
              color={isDark ? 'black' : 'white'}
            />
          </Button.LabelContent>
        </Button>
        <Button
          size="sm"
          className={cn('rounded-full bg-black/20', isDark && 'bg-white/20')}
          isIconOnly
          onPress={router.back}
        >
          <Button.LabelContent>
            <Feather name="x" size={24} color={isDark ? 'black' : 'white'} />
          </Button.LabelContent>
        </Button>
      </AnimatedView>

      <MarqueeCarousel cards={cards} />

      <AnimatedView
        className="items-center gap-2 px-8"
        entering={FadeInDown.delay(300).springify()}
      >
        <AppText className="text-muted-foreground text-sm font-semibold uppercase tracking-wider">
          Step 1
        </AppText>
        <AppText className="text-4xl font-semibold text-foreground text-center">
          Build your HAUS plan
        </AppText>
        <AppText className="text-lg text-center text-foreground/75">
          Tell us your timeline, desired neighborhoods, and must-haves. We line
          up the right tasks, introductions, and insights to get your first week
          on HAUS dialed in.
        </AppText>
      </AnimatedView>

      <AnimatedView entering={FadeIn.delay(350)}>
        <Divider variant="thick" className="my-8 opacity-20" />
      </AnimatedView>

      <AnimatedView entering={FadeInDown.delay(400).springify()}>
        <Button
          onPress={() => router.push('/showcases/onboarding-flow/step-2')}
          className="mx-8 rounded-full bg-[#F8DD00]"
        >
          <Button.LabelContent
            classNames={{ text: 'text-lg font-semibold text-black' }}
          >
            Continue
          </Button.LabelContent>
        </Button>
      </AnimatedView>
    </View>
  );
};

export default OnboardingScreen;
