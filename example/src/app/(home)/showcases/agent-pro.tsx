import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const AgentProScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases/open-to-offers')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Agent Pro
          </AppText>
          
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={simulatePress}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="more-horizontal" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
        </View>
        
        <Card className="h-44 rounded-2xl bg-card border border-border mb-4 overflow-hidden relative">
          <View className="absolute inset-0 bg-gradient-to-b from-black/70 to-transparent" />
          
          <View className="absolute bottom-4 left-4 right-4">
            <View className="flex-row items-end justify-between">
              <View>
                <AppText className="text-xs text-muted-foreground mb-1">
                  Hearth Pro
                </AppText>
                <AppText className="text-xl font-semibold text-white">
                  Ava Reed
                </AppText>
              </View>
              
              <View className="flex-row items-center gap-1">
                <Feather name="star" size={16} color={colors.amber[500]} />
                <AppText className="text-sm text-white">
                  4.9 (128)
                </AppText>
              </View>
            </View>
          </View>
        </Card>
        
        <View className="grid grid-cols-3 gap-3 mb-5">
          <Card className="rounded-xl bg-card border border-border p-3 items-center">
            <AppText className="text-xs text-muted-foreground mb-1">
              Years
            </AppText>
            <AppText className="text-base font-semibold text-foreground">
              12
            </AppText>
          </Card>
          
          <Card className="rounded-xl bg-card border border-border p-3 items-center">
            <AppText className="text-xs text-muted-foreground mb-1">
              Clients
            </AppText>
            <AppText className="text-base font-semibold text-foreground">
              850+
            </AppText>
          </Card>
          
          <Card className="rounded-xl bg-card border border-border p-3 items-center">
            <AppText className="text-xs text-muted-foreground mb-1">
              Response
            </AppText>
            <AppText className="text-base font-semibold text-foreground">
              ~2h
            </AppText>
          </Card>
        </View>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <AppText className="text-base font-medium text-foreground mb-2">
            About
          </AppText>
          <AppText className="text-sm text-muted-foreground">
            Mortgage specialist focusing on first home buyers and complex incomes. Clear, fast communication with a practical approach.
          </AppText>
        </Card>
        
        <View className="flex-row gap-2">
          <Button
            size="lg"
            onPress={() => router.push('/showcases/messages')}
            className="flex-1 rounded-xl bg-primary"
          >
            <Button.StartContent>
              <Feather name="message-circle" size={16} color="white" />
            </Button.StartContent>
            <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
              Message
            </Button.LabelContent>
          </Button>
          
          <Button
            size="lg"
            variant="outline"
            onPress={simulatePress}
            className="rounded-xl border-border"
          >
            <Button.LabelContent>
              <Feather name="phone" size={16} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

export default AgentProScreen;