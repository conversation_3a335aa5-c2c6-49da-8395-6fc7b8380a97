/* eslint-disable react-native/no-inline-styles */
import { HeaderBackButton } from '@react-navigation/elements';
import { useRouter } from 'expo-router';
import { Platform, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Carousel } from '../../../components/showcase-carousel';

const data = [
  {
    imageLight:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/showcases-onboarding-light-1.png',
    imageDark:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/showcases-onboarding-dark-1.png',
    title: 'Onboarding',
    description: 'Onboarding step with marquee carousel of shadowed cards',
    href: '/showcases/onboarding',
    components: [
      { name: 'Button', href: '/components/button' },
      { name: 'Card', href: '/components/card' },
      { name: 'DropShadowView', href: '/components/drop-shadow-view' },
      { name: 'Divider', href: '/components/divider' },
    ],
  },
  {
    imageLight:
      'https://images.unsplash.com/photo-1600585154526-990dced4db0d?q=80&w=1600&auto=format&fit=crop',
    imageDark:
      'https://images.unsplash.com/photo-1600585154526-990dced4db0d?q=80&w=1600&auto=format&fit=crop',
    title: 'Today on HAUS',
    description:
      'Daily academy digest with quick actions, progress, and engagement insight.',
    href: '/showcases/today-on-haus',
    components: [
      { name: 'Card', href: '/components/card' },
      { name: 'Button', href: '/components/button' },
      { name: 'Chip', href: '/components/chip' },
    ],
  },
  {
    imageLight:
      'https://images.unsplash.com/photo-1575517111478-7f6f2c94b3b5?q=80&w=1600&auto=format&fit=crop',
    imageDark:
      'https://images.unsplash.com/photo-1575517111478-7f6f2c94b3b5?q=80&w=1600&auto=format&fit=crop',
    title: 'Open to Offers',
    description: 'Featured property listings with filtering and demand trigger progress.',
    href: '/showcases/open-to-offers',
    components: [
      { name: 'Button', href: '/components/button' },
      { name: 'Card', href: '/components/card' },
      { name: 'Chip', href: '/components/chip' },
    ],
  },
  {
    imageLight:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/showcase-paywall.png',
    imageDark:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/showcase-paywall.png',
    title: 'Hero Paywall',
    description:
      'Modern, animated paywall with free trial, secure checkout, and flexible plans.',
    href: '/showcases/paywall',
    components: [
      { name: 'Switch', href: '/components/switch' },
      { name: 'FormField', href: '/components/form-field' },
      { name: 'RadioGroup', href: '/components/radio' },
      { name: 'Button', href: '/components/button' },
    ],
  },
  {
    imageLight:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/showcase-auth-light.png',
    imageDark:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/showcase-auth-dark.png',
    title: 'Authentication',
    description:
      'Beautiful login/signup forms with social auth, validation, and smooth transitions.',
    href: '/showcases/auth',
    components: [
      { name: 'TextField', href: '/components/text-field' },
      { name: 'FormField', href: '/components/form-field' },
      { name: 'Checkbox', href: '/components/checkbox' },
      { name: 'Switch', href: '/components/switch' },
      { name: 'Button', href: '/components/button' },
      { name: 'Card', href: '/components/card' },
    ],
  },
  {
    imageLight:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/finance-affordability-light.png',
    imageDark:
      'https://heroui-assets.nyc3.cdn.digitaloceanspaces.com/images/heroui-native-example/finance-affordability-dark.png',
    title: 'Affordability',
    description: 'Finance affordability calculator with range, deposit progress, and repayment estimates.',
    href: '/showcases/affordability',
    components: [
      { name: 'Button', href: '/components/button' },
      { name: 'Card', href: '/components/card' },
    ],
  },
];

export default function ScaleCarousel() {
  const router = useRouter();

  const insets = useSafeAreaInsets();

  return (
    <View className="flex-1 bg-background">
      {Platform.OS === 'android' && (
        <HeaderBackButton
          displayMode="minimal"
          onPress={router.back}
          pressColor="transparent"
          style={{
            position: 'absolute',
            top: insets.top + 12,
            left: 16,
            zIndex: 99,
          }}
        />
      )}
      <Carousel data={data} />
    </View>
  );
}