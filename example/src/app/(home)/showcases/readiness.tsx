import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { Button, Card, useTheme } from 'heroui-native';
import { useCallback, useRef } from 'react';
import { ScrollView, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { AppText } from '../../../components/app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

const ReadinessScreen = () => {
  const insets = useSafeAreaInsets();
  const { theme, setTheme, colors } = useTheme();
  const router = useRouter();
  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');
      
      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-background">
      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 24,
          paddingHorizontal: 12,
        }}
      >
        <View className="flex-row items-center justify-between mb-6">
          <Button
            size="sm"
            variant="ghost"
            isIconOnly
            onPress={() => router.push('/showcases')}
            className="rounded-xl"
          >
            <Button.LabelContent>
              <Feather name="arrow-left" size={18} color={colors.foreground} />
            </Button.LabelContent>
          </Button>
          
          <AppText className="text-lg font-semibold text-foreground">
            Pre-Approval
          </AppText>
          
          <View className="flex-row gap-2">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="info" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={simulatePress}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="share-2" size={18} color={colors.foreground} />
              </Button.LabelContent>
            </Button>
          </View>
        </View>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="items-center mb-6">
            <View className="flex-row items-center gap-2 mb-3">
              <Feather name="target" size={16} color={colors.primary} />
              <AppText className="text-sm font-medium text-foreground">
                Readiness score
              </AppText>
            </View>
            
            <View className="w-32 h-32 rounded-full border-8 border-primary/30 relative">
              <View className="absolute inset-0 rounded-full border-8 border-primary" 
                style={{ 
                  clipPath: 'inset(0 0 0 0)', 
                  borderRightColor: 'transparent',
                  borderTopColor: 'transparent',
                  transform: 'rotate(230deg)',
                  transformOrigin: 'center center'
                }}
              />
              <View className="absolute inset-0 items-center justify-center flex">
                <AppText className="text-2xl font-bold text-foreground">64%</AppText>
              </View>
            </View>
          </View>
          
          <View className="gap-4">
            <View>
              <View className="flex-row items-center justify-between mb-2">
                <AppText className="text-xs text-muted-foreground">
                  Documents
                </AppText>
                <AppText className="text-xs font-medium text-foreground">
                  68%
                </AppText>
              </View>
              <View className="h-2 bg-muted rounded-full overflow-hidden">
                <View 
                  className="h-full bg-primary rounded-full" 
                  style={{ width: '68%' }}
                />
              </View>
            </View>
            
            <View>
              <View className="flex-row items-center justify-between mb-2">
                <AppText className="text-xs text-muted-foreground">
                  Income check
                </AppText>
                <AppText className="text-xs font-medium text-foreground">
                  72%
                </AppText>
              </View>
              <View className="h-2 bg-muted rounded-full overflow-hidden">
                <View 
                  className="h-full bg-blue-500 rounded-full" 
                  style={{ width: '72%' }}
                />
              </View>
            </View>
            
            <View>
              <View className="flex-row items-center justify-between mb-2">
                <AppText className="text-xs text-muted-foreground">
                  Credit
                </AppText>
                <AppText className="text-xs font-medium text-foreground">
                  58%
                </AppText>
              </View>
              <View className="h-2 bg-muted rounded-full overflow-hidden">
                <View 
                  className="h-full bg-amber-500 rounded-full" 
                  style={{ width: '58%' }}
                />
              </View>
            </View>
          </View>
        </Card>
        
        <Card className="rounded-2xl bg-card border border-border mb-5">
          <View className="flex-row items-center gap-2 mb-4">
            <Feather name="list-checks" size={16} color={colors.blue[500]} />
            <AppText className="text-sm font-medium text-foreground">
              Required steps
            </AppText>
          </View>
          
          <View className="gap-3">
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <View className="flex-row items-center gap-3">
                <View className="w-8 h-8 rounded-lg border border-border flex items-center justify-center">
                  <Feather name="file-check" size={16} color={colors.primary} />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-foreground">
                    ID Verification
                  </AppText>
                  <AppText className="text-xs text-muted-foreground">
                    Complete
                  </AppText>
                </View>
              </View>
              <Feather name="check" size={16} color={colors.emerald[500]} />
            </View>
            
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <View className="flex-row items-center gap-3">
                <View className="w-8 h-8 rounded-lg border border-border flex items-center justify-center">
                  <Feather name="file-text" size={16} color={colors.amber[500]} />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-foreground">
                    Payslips
                  </AppText>
                  <AppText className="text-xs text-muted-foreground">
                    2/3 Uploaded
                  </AppText>
                </View>
              </View>
              <Feather name="chevron-right" size={16} color={colors.mutedForeground} />
            </View>
            
            <View className="flex-row items-center justify-between rounded-xl border border-border p-3">
              <View className="flex-row items-center gap-3">
                <View className="w-8 h-8 rounded-lg border border-border flex items-center justify-center">
                  <Feather name="scan-text" size={16} color={colors.rose[500]} />
                </View>
                <View>
                  <AppText className="text-sm font-medium text-foreground">
                    Bank statements
                  </AppText>
                  <AppText className="text-xs text-muted-foreground">
                    Upload last 90 days
                  </AppText>
                </View>
              </View>
              <Feather name="chevron-right" size={16} color={colors.mutedForeground} />
            </View>
          </View>
        </Card>
        
        <Button
          size="lg"
          onPress={() => router.push('/showcases/review-submit')}
          className="rounded-xl bg-primary"
        >
          <Button.StartContent>
            <Feather name="user-plus" size={16} color="white" />
          </Button.StartContent>
          <Button.LabelContent classNames={{ text: 'text-sm font-medium text-primary-foreground' }}>
            Invite a broker
          </Button.LabelContent>
        </Button>
      </ScrollView>
    </View>
  );
};

export default ReadinessScreen;