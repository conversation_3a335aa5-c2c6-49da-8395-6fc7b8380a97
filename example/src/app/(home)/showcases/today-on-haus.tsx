import Feather from '@expo/vector-icons/Feather';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { Button, Card, Chip, useTheme } from 'heroui-native';
import type { ComponentProps } from 'react';
import { useCallback, useMemo, useRef, useState } from 'react';
import { Pressable, ScrollView, StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Svg, {
  Circle,
  Defs,
  LinearGradient as SvgLinearGradient,
  Path,
  Rect,
  Stop,
} from 'react-native-svg';
import { AppText } from '../../../components/app-text';
import { TodayFlipCard } from '../../../components/showcases/today/flip-card';
import { simulatePress } from '../../../helpers/utils/simulate-press';
import { useRouter } from 'expo-router';

const heroImage =
  'https://images.unsplash.com/photo-1600585154526-990dced4db0d?q=80&w=1600&auto=format&fit=crop';

const SPARKLINE_HEIGHT = 48;

const sparklineSeries = [12, 18, 16, 23, 21, 27, 24];

type FeatherIconName = ComponentProps<typeof Feather>['name'];

type QuickAction = {
  icon: FeatherIconName;
  accent: string;
  tint: string;
  label: string;
  title: string;
  subtitle: string;
  meta: string;
};

const quickActions: QuickAction[] = [
  {
    icon: 'pie-chart',
    accent: '#60a5fa',
    tint: 'rgba(96,165,250,0.12)',
    label: 'Budget',
    title: 'Affordability',
    subtitle: 'Real costs, real number',
    meta: '5 min',
  },
  {
    icon: 'shield',
    accent: '#38bdf8',
    tint: 'rgba(56,189,248,0.12)',
    label: 'Vault',
    title: 'Document Vault',
    subtitle: 'IDs, payslips, bank statements',
    meta: 'Secure',
  },
];

type SparklinePaths = {
  areaPath: string;
  linePath: string;
  lastPoint: { x: number; y: number } | null;
};

const Sparkline = ({ data }: { data: number[] }) => {
  const [width, setWidth] = useState(0);

  const { areaPath, linePath, lastPoint }: SparklinePaths = useMemo(() => {
    if (width === 0 || data.length === 0) {
      return { areaPath: '', linePath: '', lastPoint: null };
    }

    if (data.length === 1) {
      const x = width;
      const y = SPARKLINE_HEIGHT / 2;
      const fallbackPath = `M 0 ${y} L ${x} ${y}`;
      const fallbackArea = `M 0 ${SPARKLINE_HEIGHT} L 0 ${y} L ${x} ${y} L ${x} ${SPARKLINE_HEIGHT} Z`;

      return {
        areaPath: fallbackArea,
        linePath: fallbackPath,
        lastPoint: { x, y },
      };
    }

    const max = Math.max(...data);
    const min = Math.min(...data);
    const range = max - min || 1;
    const stepX = width / (data.length - 1);

    const points = data.map((value, index) => {
      const x = index * stepX;
      const normalized = (value - min) / range;
      const y = SPARKLINE_HEIGHT - normalized * SPARKLINE_HEIGHT;

      return { x, y };
    });

    const linePath = points
      .map((point, index) => `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`)
      .join(' ');

    const areaPathSegments = [
      `M ${points[0].x} ${SPARKLINE_HEIGHT}`,
      ...points.map((point) => `L ${point.x} ${point.y}`),
      `L ${points[points.length - 1].x} ${SPARKLINE_HEIGHT}`,
      'Z',
    ];

    return {
      areaPath: areaPathSegments.join(' '),
      linePath,
      lastPoint: points[points.length - 1],
    };
  }, [data, width]);

  const handleLayout = useCallback((event: {
    nativeEvent: { layout: { width: number } };
  }) => {
    setWidth(event.nativeEvent.layout.width);
  }, []);

  return (
    <View className="h-12 justify-center" onLayout={handleLayout}>
      {width > 0 && (
        <Svg width={width} height={SPARKLINE_HEIGHT}>
          <Defs>
            <SvgLinearGradient id="spark-area" x1="0%" x2="0%" y1="0%" y2="100%">
              <Stop offset="0%" stopColor="rgba(56,189,248,0.45)" />
              <Stop offset="100%" stopColor="rgba(14,116,144,0.02)" />
            </SvgLinearGradient>
            <SvgLinearGradient id="spark-line" x1="0%" x2="100%" y1="0%" y2="0%">
              <Stop offset="0%" stopColor="#38bdf8" />
              <Stop offset="100%" stopColor="#a855f7" />
            </SvgLinearGradient>
          </Defs>

          <Rect
            x={0}
            y={0}
            width={width}
            height={SPARKLINE_HEIGHT}
            fill="url(#spark-area)"
            opacity={0.2}
            rx={8}
            ry={8}
          />

          {areaPath !== '' && (
            <Path d={areaPath} fill="url(#spark-area)" opacity={0.55} />
          )}

          {linePath !== '' && (
            <Path
              d={linePath}
              fill="none"
              stroke="url(#spark-line)"
              strokeWidth={2.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}

          {lastPoint && (
            <Circle
              cx={lastPoint.x}
              cy={lastPoint.y}
              r={3.5}
              fill="#38bdf8"
              stroke="white"
              strokeWidth={1.5}
            />
          )}
        </Svg>
      )}
    </View>
  );
};

const QuickActionCard = ({
  icon,
  accent,
  tint,
  label,
  title,
  subtitle,
  meta,
}: QuickAction) => {
  return (
    <Pressable
      onPress={simulatePress}
      className="flex-1 bg-neutral-900/90 border border-neutral-800 rounded-2xl p-3.5"
    >
      <View className="flex-row items-center justify-between mb-3">
        <View className="flex-row items-center gap-2">
          <View
            className="w-9 h-9 rounded-2xl items-center justify-center border"
            style={{ backgroundColor: tint, borderColor: `${accent}33` }}
          >
            <Feather name={icon} size={18} color={accent} />
          </View>
          <AppText className="text-[12px] text-white/70">{label}</AppText>
        </View>
        <AppText className="text-[11px] text-white/50">{meta}</AppText>
      </View>
      <AppText className="text-sm font-medium text-white mb-1">{title}</AppText>
      <AppText className="text-xs text-white/60">{subtitle}</AppText>
    </Pressable>
  );
};

const TodayOnHaus = () => {
  const insets = useSafeAreaInsets();
  const router = useRouter();

  const { theme, setTheme, colors } = useTheme();

  const prevTheme = useRef(theme);

  useFocusEffect(
    useCallback(() => {
      prevTheme.current = theme;
      setTheme('dark');

      return () => {
        setTheme(prevTheme.current);
      };
    }, [setTheme, theme])
  );

  return (
    <View className="flex-1 bg-black">
      <ExpoLinearGradient
        colors={['rgba(15,15,15,0.98)', 'rgba(10,10,10,0.94)']}
        style={StyleSheet.absoluteFill}
      />
      <ExpoLinearGradient
        colors={['rgba(56,189,248,0.08)', 'transparent']}
        start={{ x: 0.1, y: 0 }}
        end={{ x: 0.9, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{
          paddingTop: insets.top + 12,
          paddingBottom: insets.bottom + 60,
          paddingHorizontal: 12,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View className="gap-5">
          <View className="flex-row items-center justify-between">
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={() => router.push('/showcases')}
              className="rounded-xl"
            >
              <Button.LabelContent>
                <Feather name="arrow-left" size={18} color="white" />
              </Button.LabelContent>
            </Button>
            <View className="flex-row items-center gap-3">
              <View className="w-9 h-9 rounded-2xl bg-sky-500/15 border border-sky-400/30 items-center justify-center">
                <Feather name="home" size={18} color="#7dd3fc" />
              </View>
              <AppText className="text-base font-medium tracking-tight text-white">
                HAUS
              </AppText>
            </View>
            <View className="flex-row items-center gap-3">
              <View className="items-end">
                <AppText className="text-[11px] text-white/50">Engagement</AppText>
                <AppText className="text-sm font-semibold text-sky-300 leading-none">
                  Streak 8
                </AppText>
              </View>
              <Button
                size="sm"
                isIconOnly
                onPress={simulatePress}
                className="rounded-full bg-white/5 border border-white/15"
              >
                <Button.LabelContent>
                  <Feather name="bell" size={18} color={colors.mutedForeground} />
                </Button.LabelContent>
              </Button>
            </View>
          </View>

          <TodayFlipCard
            title="Module 3: True Affordability"
            subtitle="HAUS Academy"
            duration="12 min"
            description="Including fees, insurance, and ongoing rates"
            ctaLabel="Continue Module"
            imageUri={heroImage}
            features={[
              { label: 'True cost breakdowns' },
              { label: 'Scenario-based budgeting' },
              { label: 'Expert commentary' },
              { label: 'Downloadable checklists' },
            ]}
          />

          <View className="flex-row gap-3">
            {quickActions.map((item) => (
              <View key={item.title} className="flex-1">
                <QuickActionCard {...item} />
              </View>
            ))}
          </View>

          <Card className="bg-neutral-900/90 border border-neutral-800 rounded-[28px] p-5">
            <View className="flex-row items-center justify-between mb-4">
              <View className="flex-row items-center gap-2">
                <Feather name="activity" size={18} color="#fcd34d" />
                <AppText className="text-sm font-medium text-white">
                  This Week
                </AppText>
              </View>
              <AppText className="px-2 py-0.5 rounded-full bg-amber-400 text-black text-[11px] font-semibold">
                +24%
              </AppText>
            </View>

            <View className="gap-2">
              <View className="flex-row items-center justify-between">
                <AppText className="text-xs text-white/60">Academy modules</AppText>
                <AppText className="text-xs font-medium text-white">3/5</AppText>
              </View>
              <View className="flex-row items-center justify-between">
                <AppText className="text-xs text-white/60">Pre-approval readiness</AppText>
                <AppText className="text-xs font-medium text-white">64%</AppText>
              </View>
              <View className="w-full h-1.5 bg-neutral-800 rounded-full overflow-hidden">
                <View className="h-full bg-blue-400 rounded-full" style={{ width: '64%' }} />
              </View>
            </View>

            <View className="mt-5 pt-4 border-t border-neutral-800">
              <View className="flex-row items-center justify-between mb-3">
                <View className="flex-row items-center gap-2">
                  <Feather name="users" size={18} color="#7dd3fc" />
                  <AppText className="text-sm font-medium text-white">
                    Weekly Active Users
                  </AppText>
                </View>
                <AppText className="text-xs text-white/80">1,128</AppText>
              </View>
              <View className="rounded-2xl border border-white/10 bg-black/30 p-3">
                <Sparkline data={sparklineSeries} />
              </View>
            </View>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
};

export default TodayOnHaus;
