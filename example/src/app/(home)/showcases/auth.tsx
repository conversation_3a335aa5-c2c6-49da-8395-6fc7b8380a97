import Feather from '@expo/vector-icons/Feather';
import Ionicons from '@expo/vector-icons/Ionicons';
import { Image } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import {
  <PERSON>ton,
  Card,
  Checkbox,
  Divider,
  FormField,
  Switch,
  TextField,
  useTheme,
} from 'heroui-native';
import { useState } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import Animated, {
  FadeIn,
  FadeInDown,
  FadeInUp,
  FadeOut,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppText } from '../../../components/app-text';
import BG from '../../../../assets/images/paywall-showcase-bg.jpeg';

const AnimatedView = Animated.createAnimatedComponent(View);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const AuthShowcase = () => {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();

  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      console.log(isSignUp ? 'Sign up' : 'Login', {
        email,
        password,
        ...(isSignUp && { name, confirmPassword, acceptTerms }),
        ...(!isSignUp && { rememberMe }),
      });
    }, 2000);
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
    // Reset form
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setName('');
    setRememberMe(false);
    setAcceptTerms(false);
  };

  return (
    <View
      className="flex-1 bg-black"
      style={{ paddingTop: insets.top }}
    >
      {/* Background Image with Blur */}
      <Animated.View
        style={StyleSheet.absoluteFill}
        entering={FadeInUp.duration(1000)}
      >
        <Image source={BG} style={StyleSheet.absoluteFill} blurRadius={60} />
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.9)']}
          style={StyleSheet.absoluteFill}
        />
      </Animated.View>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <AnimatedView
            entering={FadeIn.duration(500)}
            className="px-4 py-3 flex-row items-center justify-between"
          >
            <Button
              size="sm"
              variant="ghost"
              isIconOnly
              onPress={router.back}
              className="bg-white/10"
            >
              <Button.LabelContent>
                <Feather
                  name="arrow-left"
                  size={24}
                  color="white"
                />
              </Button.LabelContent>
            </Button>

            <View className="flex-row items-center gap-2">
              <AppText className="text-sm text-white/70">
                {isSignUp ? 'Login' : 'Sign up'}
              </AppText>
              <Switch
                size="sm"
                checked={isSignUp}
                onCheckedChange={toggleMode}
              />
            </View>
          </AnimatedView>

          <View className="flex-1 justify-center px-6 py-8">
            {/* Logo/Brand */}
            <AnimatedView
              key={`logo-${isSignUp}`}
              entering={FadeInDown.duration(400)}
              exiting={FadeOut.duration(200)}
              className="items-center mb-8"
            >
              <View className="w-20 h-20 rounded-2xl bg-white/10 backdrop-blur-xl items-center justify-center mb-4 border border-white/20">
                <Feather name="zap" size={40} color="white" />
              </View>
              <AppText className="text-3xl font-bold text-white">
                {isSignUp ? 'Create Account' : 'Welcome Back'}
              </AppText>
              <AppText className="text-base text-white/70 mt-2">
                {isSignUp
                  ? 'Sign up to get started with our app'
                  : 'Login to continue to your account'}
              </AppText>
            </AnimatedView>

            {/* Form Card */}
            <AnimatedView
              key={`form-${isSignUp}`}
              entering={FadeInUp.duration(400).delay(100)}
              exiting={FadeOut.duration(200)}
            >
              <Card className="p-6 bg-white/10 backdrop-blur-xl border border-white/20">
                <View className="gap-4">
                  {/* Name Field (Sign up only) */}
                  {isSignUp && (
                    <TextField isRequired>
                      <TextField.Label>Full Name</TextField.Label>
                      <TextField.Input
                        placeholder="John Doe"
                        value={name}
                        onChangeText={setName}
                        autoCapitalize="words"
                      >
                        <TextField.InputStartContent className="pointer-events-none">
                          <Feather
                            name="user"
                            size={18}
                            color={colors.mutedForeground}
                          />
                        </TextField.InputStartContent>
                      </TextField.Input>
                    </TextField>
                  )}

                  {/* Email Field */}
                  <TextField isRequired>
                    <TextField.Label>Email</TextField.Label>
                    <TextField.Input
                      placeholder="<EMAIL>"
                      value={email}
                      onChangeText={setEmail}
                      keyboardType="email-address"
                      autoCapitalize="none"
                    >
                      <TextField.InputStartContent className="pointer-events-none">
                        <Feather
                          name="mail"
                          size={18}
                          color={colors.mutedForeground}
                        />
                      </TextField.InputStartContent>
                    </TextField.Input>
                  </TextField>

                  {/* Password Field */}
                  <TextField isRequired>
                    <TextField.Label>Password</TextField.Label>
                    <TextField.Input
                      placeholder="Enter password"
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry={!showPassword}
                    >
                      <TextField.InputStartContent className="pointer-events-none">
                        <Ionicons
                          name="lock-closed-outline"
                          size={18}
                          color={colors.mutedForeground}
                        />
                      </TextField.InputStartContent>
                      <TextField.InputEndContent>
                        <Pressable onPress={() => setShowPassword(!showPassword)}>
                          <Ionicons
                            name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                            size={18}
                            color={colors.mutedForeground}
                          />
                        </Pressable>
                      </TextField.InputEndContent>
                    </TextField.Input>
                  </TextField>

                  {/* Confirm Password Field (Sign up only) */}
                  {isSignUp && (
                    <TextField isRequired>
                      <TextField.Label>Confirm Password</TextField.Label>
                      <TextField.Input
                        placeholder="Confirm password"
                        value={confirmPassword}
                        onChangeText={setConfirmPassword}
                        secureTextEntry={!showPassword}
                      >
                        <TextField.InputStartContent className="pointer-events-none">
                          <Ionicons
                            name="lock-closed-outline"
                            size={18}
                            color={colors.mutedForeground}
                          />
                        </TextField.InputStartContent>
                      </TextField.Input>
                    </TextField>
                  )}

                  {/* Remember Me / Accept Terms */}
                  <View className="mt-2">
                    {!isSignUp ? (
                      <View className="flex-row items-center justify-between">
                        <FormField
                          isSelected={rememberMe}
                          onSelectedChange={setRememberMe}
                          isInline
                        >
                          <FormField.Indicator alignIndicator="start">
                            <Checkbox />
                          </FormField.Indicator>
                          <FormField.Content>
                            <FormField.Title>Remember me</FormField.Title>
                          </FormField.Content>
                        </FormField>
                        <Pressable>
                          <AppText className="text-sm text-white/80 font-medium">
                            Forgot password?
                          </AppText>
                        </Pressable>
                      </View>
                    ) : (
                      <FormField
                        isSelected={acceptTerms}
                        onSelectedChange={setAcceptTerms}
                        isInline
                      >
                        <FormField.Indicator alignIndicator="start">
                          <Checkbox />
                        </FormField.Indicator>
                        <FormField.Content>
                          <FormField.Title>
                            I accept the{' '}
                            <AppText className="text-white font-semibold">Terms</AppText> and{' '}
                            <AppText className="text-white font-semibold">Privacy Policy</AppText>
                          </FormField.Title>
                        </FormField.Content>
                      </FormField>
                    )}
                  </View>

                  {/* Submit Button */}
                  <Button
                    onPress={handleSubmit}
                    size="lg"
                    className="mt-4"
                    isLoading={isLoading}
                    isDisabled={
                      !email ||
                      !password ||
                      (isSignUp && (!name || !confirmPassword || !acceptTerms))
                    }
                  >
                    <Button.LabelContent>
                      {isSignUp ? 'Create Account' : 'Login'}
                    </Button.LabelContent>
                  </Button>
                </View>
              </Card>
            </AnimatedView>

            {/* Social Login */}
            <AnimatedView
              entering={FadeInUp.duration(400).delay(200)}
              className="mt-6"
            >
              <View className="flex-row items-center gap-4 mb-4">
                <View className="flex-1 h-[1px] bg-white/20" />
                <AppText className="text-sm text-white/60">
                  OR CONTINUE WITH
                </AppText>
                <View className="flex-1 h-[1px] bg-white/20" />
              </View>

              <View className="flex-row gap-3">
                <Button
                  variant="outline"
                  className="flex-1 bg-white/10 border-white/20"
                  onPress={() => console.log('Google login')}
                >
                  <Button.StartContent>
                    <Ionicons name="logo-google" size={20} color="white" />
                  </Button.StartContent>
                  <Button.LabelContent classNames={{ text: 'text-white' }}>Google</Button.LabelContent>
                </Button>

                <Button
                  variant="outline"
                  className="flex-1 bg-white/10 border-white/20"
                  onPress={() => console.log('Apple login')}
                >
                  <Button.StartContent>
                    <Ionicons name="logo-apple" size={20} color="white" />
                  </Button.StartContent>
                  <Button.LabelContent classNames={{ text: 'text-white' }}>Apple</Button.LabelContent>
                </Button>
              </View>
            </AnimatedView>

            {/* Footer */}
            <AnimatedView
              entering={FadeIn.duration(400).delay(300)}
              className="items-center mt-8"
            >
              <AppText className="text-sm text-white/60 text-center">
                {isSignUp
                  ? 'Already have an account? '
                  : "Don't have an account? "}
                <AppText
                  className="text-white font-semibold"
                  onPress={toggleMode}
                >
                  {isSignUp ? 'Login' : 'Sign up'}
                </AppText>
              </AppText>
            </AnimatedView>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default AuthShowcase;