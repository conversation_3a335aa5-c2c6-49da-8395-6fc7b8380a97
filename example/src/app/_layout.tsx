import {
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold,
  useFonts,
} from '@expo-google-fonts/inter';
import { Abel_400Regular } from '@expo-google-fonts/abel';
import { Slot, usePathname, useRouter } from 'expo-router';
import { HeroUINativeProvider } from 'heroui-native';
import { StyleSheet, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from 'react-native-reanimated';
import '../../global.css';
import { AppThemeProvider, useAppTheme } from '../contexts/app-theme-context';
import AssistantNavbar from '../components/assistant-navbar';

configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
});

function ThemedLayout() {
  const { currentTheme } = useAppTheme();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <HeroUINativeProvider
      config={{
        colorScheme: 'system',
        theme: currentTheme,
        textProps: {
          allowFontScaling: false,
        },
      }}
    >
      <View style={styles.screenContainer}>
        <Slot />
        <AssistantNavbar
          activeRoute={pathname ?? '/'}
          onNavigate={(route) => {
            if (!route) return;
            if (route === pathname) {
              return;
            }
            router.push(route);
          }}
          items={[
            { id: 'new', label: 'Agent' },
            {
              id: 'docs',
              label: 'Docs',
              route: '/showcases/document-vault',
            },
            { id: 'more', label: 'More', route: '/showcases' },
          ]}
        />
      </View>
    </HeroUINativeProvider>
  );
}

export default function Layout() {
  useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
    Inter_700Bold,
    Abel_400Regular,
  });

  return (
    <GestureHandlerRootView style={styles.root}>
      <AppThemeProvider>
        <ThemedLayout />
      </AppThemeProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
  },
});
