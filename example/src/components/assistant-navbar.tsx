import { useEffect, useMemo, useRef, useState } from 'react';
import { Easing } from 'react-native-reanimated';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { Dimensions, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Bell, FileText, MoreHorizontal, Send, Settings } from 'lucide-react-native';
import { useTheme } from 'heroui-native';
import { AppText } from './app-text';
import Svg, { ClipPath, Defs, G, Path, Rect } from 'react-native-svg';
import { Pressable } from 'react-native';
import { StyleSheet } from 'react-native';

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export type AssistantNavbarItem = {
  id: string;
  label: string;
  route?: string;
};

export interface AssistantNavbarProps {
  activeRoute?: string;
  onNavigate?: (route: string) => void;
  onSend?: (message: string) => void;
  onVoiceStart?: () => void;
  onVoiceStop?: () => void;
  items?: AssistantNavbarItem[];
  initialExpanded?: boolean;
}

const DEFAULT_ITEMS: AssistantNavbarItem[] = [
  { id: 'new', label: 'Agent' },
  { id: 'docs', label: 'Docs', route: '/showcases/document-vault' },
  { id: 'more', label: 'More', route: '/showcases' },
];

const COLLAPSED_HEIGHT = 64;
const EXPANDED_HEIGHT = 352;

const LogoButton = ({
  size = 56,
  isActive,
  onPress,
  disabled,
  accessibilityLabel = 'HAUS Agent',
}: {
  size?: number;
  isActive?: boolean;
  onPress?: () => void;
  disabled?: boolean;
  accessibilityLabel?: string;
}) => {
  const { isDark } = useTheme();
  return (
    <Pressable
      onPress={onPress}
      disabled={disabled}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel}
      style={[styles.logoButton, { width: size, height: size }]}
    >
      <Svg
        width={size}
        height={size}
        viewBox="0 0 1024 1024"
        fill="none"
      >
        <Defs>
          <ClipPath id="assistant-logo-clip">
            <Rect width="1024" height="1024" rx="20" />
          </ClipPath>
        </Defs>
        <G clipPath="url(#assistant-logo-clip)">
          <Path
            d="M507.748 413.784L568.657 283.359C571.714 277.036 576.441 273.728 582.935 272.908C584.341 272.755 586.555 272.676 588.352 272.676H680.794L620.265 402.312C617.232 408.647 611.894 412.671 605.425 413.46C604.012 413.594 602.037 413.784 600.239 413.784H507.748Z"
            fill="#A4D321"
          />
          <Path
            d="M639.398 413.784L700.313 283.359C703.352 277.036 708.078 273.728 714.584 272.908C715.997 272.755 718.174 272.676 719.99 272.676H812.413L751.884 402.312C748.869 408.647 743.531 412.671 737.062 413.46C735.649 413.594 733.687 413.784 731.895 413.784H639.398Z"
            fill="#A4D321"
          />
          <Path
            d="M424.344 310.076C434.507 288.991 451.01 276.161 472.662 273.33C474.319 273.128 477.242 272.841 486.249 272.841V272.859H548.705L488.175 402.495C485.142 408.83 479.823 412.873 473.335 413.661C471.91 413.814 469.947 413.967 468.15 413.967H375.671L424.344 310.076Z"
            fill="#A4D321"
          />
          <Path
            d="M607.687 710.86C597.488 731.957 581.003 744.768 559.32 747.586C557.657 747.818 554.758 748.107 545.77 748.107H483.296L543.825 618.459C546.84 612.136 552.208 608.094 558.666 607.305C560.072 607.17 562.035 606.987 563.845 606.987H656.323L607.687 710.86Z"
            fill="#A4D321"
          />
          <Path
            d="M555.761 540.013C545.55 561.111 529.083 573.922 507.394 576.741C505.737 576.992 502.832 577.261 493.825 577.261V577.23H431.37L491.918 447.595C494.95 441.253 500.252 437.229 506.758 436.422C508.164 436.306 510.115 436.104 511.925 436.104H604.416L555.761 540.013Z"
            fill="#A4D321"
          />
          <Path
            d="M315.735 528.486L354.245 446.066C356.171 442.091 359.155 439.975 363.252 439.492C364.139 439.37 365.545 439.302 366.67 439.302H425.084L386.849 521.24C384.935 525.233 381.578 527.783 377.469 528.284C376.564 528.382 375.341 528.486 374.185 528.486H315.735Z"
            fill={isDark ? '#3D4D61' : '#fff'}
          />
          <Path
            d="M263.613 640.107L302.117 557.686C304.013 553.675 307.015 551.614 311.124 551.113C312.029 550.996 313.375 550.929 314.549 550.929H372.962L334.721 632.879C332.807 636.854 329.419 639.422 325.347 639.905C324.442 639.991 323.201 640.107 322.039 640.107H263.613Z"
            fill={isDark ? '#3D4D61' : '#fff'}
          />
          <Path
            d="M211.62 751.31L250.112 668.873C252.02 664.898 255.04 662.801 259.119 662.28C260.024 662.194 261.412 662.127 262.555 662.127H320.987L282.728 744.066C280.82 748.052 277.414 750.62 273.335 751.091C272.449 751.189 271.208 751.31 270.082 751.31H211.62Z"
            fill={isDark ? '#3D4D61' : '#fff'}
          />
          <Path
            d="M297.672 751.31L336.195 668.873C338.09 664.898 341.092 662.801 345.202 662.28C346.094 662.194 347.47 662.127 348.626 662.127H407.058L368.786 744.066C366.872 748.052 363.503 750.62 359.394 751.091C358.489 751.189 357.296 751.31 356.141 751.31H297.672Z"
            fill={isDark ? '#3D4D61' : '#fff'}
          />
          <Path
            d="M383.058 751.31L421.562 668.873C423.476 664.898 426.478 662.801 430.587 662.28C431.455 662.194 432.813 662.127 433.987 662.127H492.419L454.147 744.066C452.239 748.052 448.864 750.62 444.792 751.091C443.887 751.189 442.664 751.31 441.52 751.31H383.058Z"
            fill={isDark ? '#3D4D61' : '#fff'}
          />
          <Path
            d="M512.041 46.7322C255.493 46.7322 46.7322 255.431 46.7322 512C46.7322 768.562 255.493 977.262 512.041 977.262C768.583 977.262 977.265 768.562 977.265 512C977.265 255.431 768.583 46.7322 512.041 46.7322ZM512.041 1024C229.701 1024 0 794.356 0 512C0 229.637 229.701 2.20136e-06 512.041 2.20136e-06C794.375 2.20136e-06 1024 229.637 1024 512C1024 794.356 794.375 1024 512.041 1024Z"
            fill={isDark ? '#3D4D61' : '#fff'}
          />
        </G>
      </Svg>
      {isActive && (
        <View style={styles.logoPulse} pointerEvents="none">
          <View style={styles.logoPulseInner} />
        </View>
      )}
    </Pressable>
  );
};

const randomHeight = () => 12 + Math.random() * 56;

const AudioVisualizer = ({ visible }: { visible: boolean }) => {
  const animated = useSharedValue(visible ? 1 : 0);
  const [bars, setBars] = useState(() => Array.from({ length: 32 }, randomHeight));

  useEffect(() => {
    animated.value = withTiming(visible ? 1 : 0, {
      duration: 240,
      easing: Easing.out(Easing.quad),
    });
  }, [visible]);

  useEffect(() => {
    if (!visible) {
      return;
    }
    const timer = setInterval(() => {
      setBars((prev) => prev.map(() => randomHeight()));
    }, 180);
    return () => clearInterval(timer);
  }, [visible]);

  if (!visible) {
    return null;
  }

  const style = useAnimatedStyle(() => ({
    opacity: animated.value,
  }));

  return (
    <Animated.View pointerEvents="none" style={[styles.visualiserContainer, style]}>
      {bars.map((height, index) => (
        <View
          key={index}
          style={[styles.visualiserBar, { height }]}
        />
      ))}
    </Animated.View>
  );
};

export const AssistantNavbar = ({
  activeRoute,
  onNavigate,
  onSend,
  onVoiceStart,
  onVoiceStop,
  items = DEFAULT_ITEMS,
  initialExpanded = false,
}: AssistantNavbarProps) => {
  const insets = useSafeAreaInsets();

  const [transcription, setTranscription] = useState('');
  const [isExpanded, setIsExpanded] = useState(initialExpanded);
  const [isRecording, setIsRecording] = useState(false);
  const [cursorVisible, setCursorVisible] = useState(true);

  const height = useSharedValue(initialExpanded ? EXPANDED_HEIGHT : COLLAPSED_HEIGHT);
  const offsetY = useSharedValue(initialExpanded ? 0 : 24);

  useEffect(() => {
    height.value = withTiming(isExpanded ? EXPANDED_HEIGHT : COLLAPSED_HEIGHT, {
      duration: 280,
      easing: Easing.out(Easing.quad),
    });
    offsetY.value = withTiming(isExpanded ? 0 : 24, {
      duration: 280,
      easing: Easing.out(Easing.ease),
    });
  }, [isExpanded]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCursorVisible((prev) => !prev);
    }, 550);
    return () => clearInterval(timer);
  }, []);

  const typingTimerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const runSimulatedTranscription = () => {
    const samples = [
      'Show me what I can afford this month.',
      'Share today\'s matches near North Loop.',
      'What did my lender upload just now?',
      'Draft an intro to the sellers.',
    ];
    const phrase = samples[Math.floor(Math.random() * samples.length)];
    let index = 0;
    typingTimerRef.current && clearInterval(typingTimerRef.current);
    setTranscription('');
    typingTimerRef.current = setInterval(() => {
      index += 1;
      setTranscription(phrase.slice(0, index));
      if (index >= phrase.length && typingTimerRef.current) {
        clearInterval(typingTimerRef.current);
      }
    }, 80);
  };

  const handleAssistantToggle = () => {
    const next = !isExpanded;
    setIsExpanded(next);
    if (!next && typingTimerRef.current) {
      clearInterval(typingTimerRef.current);
      setTranscription('');
    }
  };

  const handleVoiceToggle = () => {
    const next = !isRecording;
    setIsRecording(next);
    if (next) {
      onVoiceStart?.();
      runSimulatedTranscription();
    } else {
      onVoiceStop?.();
      typingTimerRef.current && clearInterval(typingTimerRef.current);
      setTranscription('');
    }
  };

  useEffect(() => () => {
    if (typingTimerRef.current) {
      clearInterval(typingTimerRef.current);
    }
  }, []);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    height: height.value,
    transform: [{ translateY: offsetY.value }],
  }));

  const rightNavItems = useMemo(
    () => items.filter((item) => item.id !== 'new'),
    [items],
  );
  const centerItem = useMemo(
    () => items.find((item) => item.id === 'new'),
    [items],
  );

  const activeLookup = useMemo(() => {
    if (!activeRoute) {
      return undefined;
    }
    const normalized = activeRoute
      .replace(/\/\([^/]+\)/g, '')
      .replace(/\/+/g, '/');
    return items
      .filter((item) => item.route)
      .sort((a, b) => (b.route?.length ?? 0) - (a.route?.length ?? 0))
      .find((item) => item.route && normalized.startsWith(item.route))?.id;
  }, [activeRoute, items]);

  const navWidth = Math.min(Dimensions.get('window').width * 0.94, 520);
  const navBottomOffset = insets.bottom + 4;

  return (
    <View pointerEvents="box-none" style={StyleSheet.absoluteFill}>
      <Animated.View
        pointerEvents="box-none"
        style={[
          styles.container,
          animatedContainerStyle,
          {
            bottom: navBottomOffset,
            width: navWidth,
            marginLeft: -(navWidth / 2),
            borderRadius: 48,
          },
        ]}
      >
        <LinearGradient
          start={{ x: 0.1, y: 0 }}
          end={{ x: 0.9, y: 1 }}
          colors={['rgba(16,16,16,0.88)', 'rgba(28,28,28,0.94)']}
          style={[styles.gradient, { borderRadius: 48 }]}
        >
          {isExpanded && (
            <View style={styles.chatArea}>
              <View style={styles.chatHeader}>
                <View style={styles.chatHeaderLeft}>
                  <LogoButton
                    size={36}
                    onPress={handleVoiceToggle}
                    isActive={isRecording}
                    accessibilityLabel={isRecording ? 'Stop listening' : 'Start listening'}
                  />
                  <View style={styles.chatHeaderText}>
                    <AppText className="text-white/80 text-xs">
                      {isRecording ? 'Listening…' : 'Tap to speak'}
                    </AppText>
                    <AppText className="text-white text-base font-semibold">
                      HAUS Agent
                    </AppText>
                  </View>
                </View>
                <View style={styles.chatHeaderActions}>
                  <Pressable style={styles.headerIconButton}>
                    <Bell size={16} color="rgba(255,255,255,0.7)" />
                  </Pressable>
                  <Pressable style={styles.headerIconButton}>
                    <Settings size={16} color="rgba(255,255,255,0.7)" />
                  </Pressable>
                </View>
              </View>

              <View style={styles.messagesPane}>
                <View style={styles.stubMessage}>
                  <AppText className="text-white text-sm">
                    How can I help you today?
                  </AppText>
                </View>
              </View>

              <View style={styles.chatInputBar}>
                <LogoButton
                  size={40}
                  onPress={handleVoiceToggle}
                  isActive={isRecording}
                  accessibilityLabel={isRecording ? 'Stop listening' : 'Start listening'}
                />
                <View style={styles.transcriptionField}>
                  <AppText className="text-white text-sm">
                    {transcription}
                    {cursorVisible && (
                      <AppText className="text-[#A4D321]">▍</AppText>
                    )}
                  </AppText>
                </View>
                <AnimatedPressable
                  onPress={() => {
                    if (transcription.trim().length) {
                      onSend?.(transcription.trim());
                      setTranscription('');
                      setIsExpanded(false);
                    }
                  }}
                  style={({ pressed }) => [
                    styles.sendButton,
                    { opacity: pressed ? 0.7 : 1 },
                  ]}
                >
                  <Send size={16} color="#0F0F0F" />
                </AnimatedPressable>
              </View>
            </View>
          )}

          <View style={styles.navRow}>
            <View style={styles.navSide} />

            {centerItem && (
              <View style={styles.centerButtonSlot}>
                <LogoButton
                  size={64}
                  onPress={handleAssistantToggle}
                  isActive={isRecording}
                  accessibilityLabel={isExpanded ? 'Close HAUS Agent' : 'Open HAUS Agent'}
                />
              </View>
            )}

            <View style={[styles.navSide, styles.navSideRight]}>
              {rightNavItems.map((item) => {
                const iconById: Record<string, typeof FileText> = {
                  docs: FileText,
                  more: MoreHorizontal,
                };
                const Icon = iconById[item.id] ?? MoreHorizontal;
                const isActive = activeLookup === item.id;
                return (
                  <Pressable
                    key={item.id}
                    style={styles.navItem}
                    accessibilityRole="button"
                    accessibilityLabel={item.label}
                    onPress={() => {
                      if (item.route) {
                        onNavigate?.(item.route);
                      }
                    }}
                  >
                    <Icon
                      size={20}
                      color={
                        isActive
                          ? '#A4D321'
                          : 'rgba(255,255,255,0.7)'
                      }
                    />
                  </Pressable>
                );
              })}
            </View>
          </View>
        </LinearGradient>
      </Animated.View>

      <AudioVisualizer visible={isRecording} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: '50%',
    borderRadius: 48,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 12 },
    shadowRadius: 24,
    elevation: 12,
  },
  gradient: {
    flex: 1,
    borderRadius: 48,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.08)',
    overflow: 'hidden',
  },
  chatArea: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    gap: 16,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  chatHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  chatHeaderText: {
    flexDirection: 'column',
    gap: 2,
  },
  chatHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerIconButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.06)',
  },
  messagesPane: {
    flex: 1,
  },
  stubMessage: {
    borderRadius: 14,
    padding: 14,
    backgroundColor: 'rgba(0,0,0,0.35)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.08)',
  },
  chatInputBar: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingBottom: 16,
  },
  transcriptionField: {
    flex: 1,
    borderRadius: 18,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.12)',
    backgroundColor: 'rgba(0,0,0,0.35)',
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#A4D321',
    alignItems: 'center',
    justifyContent: 'center',
  },
  navRow: {
    flexDirection: 'row',
    height: COLLAPSED_HEIGHT,
    alignItems: 'center',
    paddingHorizontal: 24,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: 'rgba(255,255,255,0.08)',
  },
  navSide: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  navSideRight: {
    justifyContent: 'flex-end',
    columnGap: 18,
  },
  centerButtonSlot: {
    width: 96,
    alignItems: 'center',
    justifyContent: 'center',
  },
  navItem: {
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  visualiserContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 92,
    height: 72,
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 24,
    gap: 4,
  },
  visualiserBar: {
    flex: 1,
    height: Math.random() * 64 + 8,
    borderRadius: 12,
    backgroundColor: 'rgba(164, 211, 33, 0.35)',
  },
  logoButton: {
    borderRadius: 999,
    backgroundColor: 'rgba(12,12,12,0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  logoPulse: {
    position: 'absolute',
    inset: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoPulseInner: {
    width: '70%',
    height: '70%',
    borderRadius: 999,
    backgroundColor: 'rgba(164,211,33,0.22)',
  },
});

export default AssistantNavbar;
