import { useHeaderHeight } from '@react-navigation/elements';
import { cn } from 'heroui-native';
import { type FC, type PropsWithChildren, useRef } from 'react';
import { Platform, ScrollView, type ScrollViewProps } from 'react-native';
import Animated, { type AnimatedProps } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const AnimatedScrollView = Animated.createAnimatedComponent(ScrollView);

interface Props extends AnimatedProps<ScrollViewProps> {
  className?: string;
  contentContainerClassName?: string;
}

export const ScreenScrollView: FC<PropsWithChildren<Props>> = ({
  children,
  className,
  contentContainerClassName,
  ...props
}) => {
  const insets = useSafeAreaInsets();
  const fallbackLoggedRef = useRef(false);
  let headerHeight = Platform.OS === 'ios' ? insets.top : 0;

  try {
    headerHeight = useHeaderHeight();
  } catch (error) {
    if (__DEV__ && !fallbackLoggedRef.current) {
      console.warn(
        'ScreenScrollView: falling back to safe-area top inset because no header context was found.',
        error,
      );
      fallbackLoggedRef.current = true;
    }
  }
  return (
    <AnimatedScrollView
      className={cn('bg-background', className)}
      contentContainerClassName={cn('px-5', contentContainerClassName)}
      contentContainerStyle={{
        paddingTop: Platform.select({
          ios: headerHeight,
          android: 0,
        }),
        paddingBottom: insets.bottom + 32,
      }}
      showsVerticalScrollIndicator={false}
      {...props}
    >
      {children}
    </AnimatedScrollView>
  );
};
