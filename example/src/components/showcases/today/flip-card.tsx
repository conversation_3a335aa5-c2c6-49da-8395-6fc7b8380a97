import Feather from '@expo/vector-icons/Feather';
import { LinearGradient } from 'expo-linear-gradient';
import { <PERSON>ton, Chip } from 'heroui-native';
import { useMemo, useState } from 'react';
import { ImageBackground, Pressable, StyleSheet, View } from 'react-native';
import Animated, {
  Easing,
  FadeIn,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { AppText } from '../../app-text';
import { simulatePress } from '../../../helpers/utils/simulate-press';

type Feature = {
  label: string;
  delay?: number;
};

type Props = {
  title: string;
  subtitle: string;
  duration: string;
  description: string;
  ctaLabel: string;
  features: Feature[];
  imageUri: string;
};

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);
const AnimatedView = Animated.createAnimatedComponent(View);

export const TodayFlipCard = ({
  title,
  subtitle,
  duration,
  description,
  ctaLabel,
  features,
  imageUri,
}: Props) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const rotation = useSharedValue(0);

  const toggle = () => {
    setIsFlipped((prev) => {
      const next = prev ? 0 : 180;
      rotation.value = withTiming(next, {
        duration: 520,
        easing: Easing.out(Easing.ease),
      });
      return !prev;
    });
  };

  const frontAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { perspective: 1000 },
      { rotateY: `${rotation.value}deg` },
    ],
    opacity: interpolate(rotation.value, [0, 90], [1, 0]),
  }));

  const backAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { perspective: 1000 },
      { rotateY: `${rotation.value + 180}deg` },
    ],
    opacity: interpolate(rotation.value, [90, 180], [0, 1]),
  }));

  const featureItems = useMemo(() => features.slice(0, 4), [features]);

  return (
    <View style={styles.container}>
      <AnimatedPressable onPress={toggle} style={styles.perspective}>
        <AnimatedView style={[styles.cardFace, frontAnimatedStyle]}>
          <ImageBackground
            source={{ uri: imageUri }}
            resizeMode="cover"
            blurRadius={12}
            style={styles.image}
          >
            <LinearGradient
              colors={['rgba(0,0,0,0.8)', 'rgba(0,0,0,0.4)', 'rgba(0,0,0,0.85)']}
              locations={[0, 0.55, 1]}
              style={StyleSheet.absoluteFill}
            />
            <View style={styles.frontContent}>
              <View style={styles.frontHeader}>
                <Chip size="sm" className="bg-white/20 border border-white/30">
                  <Chip.LabelContent classNames={{ text: 'text-white text-[11px]' }}>
                    {subtitle}
                  </Chip.LabelContent>
                </Chip>
                <View className="flex-row items-center gap-1.5">
                  <Feather name="clock" size={14} color="rgba(255,255,255,0.85)" />
                  <AppText className="text-[11px] text-white/90">{duration}</AppText>
                </View>
              </View>

              <View className="gap-4">
                <View className="gap-1.5">
                  <AppText className="text-2xl font-semibold text-white tracking-tight">
                    {title}
                  </AppText>
                  <AppText className="text-[12px] text-sky-100/90">
                    {description}
                  </AppText>
                </View>
                <Button
                  size="md"
                  className="rounded-2xl bg-white/12 border border-white/25"
                  onPress={simulatePress}
                >
                  <Button.StartContent>
                    <Feather name="play" size={16} color="white" />
                  </Button.StartContent>
                  <Button.LabelContent classNames={{ text: 'text-[13px] font-medium text-white' }}>
                    {ctaLabel}
                  </Button.LabelContent>
                </Button>
              </View>
            </View>
          </ImageBackground>
        </AnimatedView>

        <AnimatedView style={[styles.cardFace, styles.cardBack, backAnimatedStyle]}>
          <LinearGradient
            colors={['rgba(15,15,15,0.95)', 'rgba(15,15,15,0.75)']}
            style={StyleSheet.absoluteFill}
          />
          <View style={styles.backContent}>
            <View className="gap-1.5">
              <AppText className="text-lg font-semibold text-white tracking-tight">
                {title}
              </AppText>
              <AppText className="text-sm text-white/70">
                {description}
              </AppText>
            </View>
            <View className="gap-3">
              {featureItems.map((feature, index) => (
                <AnimatedView
                  key={feature.label}
                  entering={FadeIn.delay(120 + index * 40)}
                  className="flex-row items-center gap-2"
                >
                  <Feather name="arrow-right" size={14} color="rgba(125,211,252,0.9)" />
                  <AppText className="text-sm text-white/85">{feature.label}</AppText>
                </AnimatedView>
              ))}
            </View>
            <View className="items-end">
              <AppText className="text-xs text-white/55">
                Tap to flip back
              </AppText>
            </View>
          </View>
        </AnimatedView>
      </AnimatedPressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 260,
  },
  perspective: {
    flex: 1,
    height: '100%',
    position: 'relative',
    alignItems: 'stretch',
    justifyContent: 'center',
  },
  cardFace: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 28,
    overflow: 'hidden',
    backfaceVisibility: 'hidden',
  },
  cardBack: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderColor: 'rgba(255,255,255,0.12)',
    borderWidth: StyleSheet.hairlineWidth,
  },
  image: {
    flex: 1,
  },
  frontContent: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  frontHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backContent: {
    flex: 1,
    padding: 22,
    justifyContent: 'space-between',
  },
});
