import React, { useEffect, useMemo, useRef, useState } from "react";
import { View, Text, StyleSheet, TextInput, ScrollView, TouchableOpacity, Platform } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import Animated, { useSharedValue, useAnimatedStyle, withSpring, withTiming } from "react-native-reanimated";
import { 
  Paperclip, 
  Send, 
  Mic, 
  Square, 
  Bot, 
  User, 
  Loader2, 
  FileText, 
  Sparkles,
  ChevronDown,
  X
} from "lucide-react-native";

const AnimatedView = Animated.createAnimatedComponent(View);

export default function AssistantDock({
  isOpen,
  onOpenChange,
  context,
  onSend,
  onAttachFiles,
  onStartVoice,
  onStopVoice,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  context?: { type?: string; refId?: string; title?: string };
  onSend?: (message: string) => Promise<string> | string;
  onAttachFiles?: (files: any) => void;
  onStartVoice?: () => void;
  onStopVoice?: () => void;
}) {
  const insets = useSafeAreaInsets();
  const [messages, setMessages] = useState<
    { id: string; role: "assistant" | "user"; content: string; ts: number }[]
  >([
    {
      id: "m0",
      role: "assistant",
      content:
        "Hey! I'm HAUS—your property co-pilot. Ask me about a listing, borrowing power, or upload a doc and I'll extract what matters.",
      ts: Date.now(),
    },
  ]);
  const [input, setInput] = useState("");
  const [sending, setSending] = useState(false);
  const [streaming, setStreaming] = useState(false);
  const [draft, setDraft] = useState("");
  const scrollRef = useRef<ScrollView>(null);

  const height = useSharedValue(0);
  const isExpanded = useSharedValue(false);

  // Auto-scroll to bottom on new messages
  useEffect(() => {
    if (scrollRef.current) {
      setTimeout(() => {
        scrollRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length, draft]);

  // Simple suggestion chips – adapt to context
  const suggestions = useMemo(() => {
    if (context?.type === "property") {
      return [
        "Summarise this property",
        "What's the fair price range?",
        "Compare with similar listings",
      ];
    }
    if (context?.type === "finance") {
      return ["Estimate borrowing power", "Explain pre-approval steps", "What docs do I need?"];
    }
    if (context?.type === "docs") {
      return ["Extract key figures", "Is anything missing?", "Fill lender checklist"];
    }
    return ["Show today's picks", "What can I afford?", "Help me shortlist"];
  }, [context?.type]);

  // Animation for dock height
  const animatedStyle = useAnimatedStyle(() => {
    return {
      height: withSpring(height.value, { damping: 20, stiffness: 100 }),
    };
  });

  // Toggle dock expansion
  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
    height.value = isExpanded.value ? withSpring(400) : withSpring(200);
  };

  async function handleSend(msg?: string) {
    const text = (msg ?? input).trim();
    if (!text) return;

    const userMsg = { 
      id: Math.random().toString(36).substr(2, 9), 
      role: "user" as const, 
      content: text, 
      ts: Date.now() 
    };
    setMessages((m) => [...m, userMsg]);
    setInput("");
    setSending(true);

    // Simulated streaming while awaiting provider
    setStreaming(true);
    let partial = "";
    const streamTimer = setInterval(() => {
      partial += ".";
      setDraft(`Thinking${partial}`);
    }, 250);

    try {
      let reply = "";
      if (onSend) {
        const r = await onSend(text);
        reply = typeof r === "string" ? r : "";
      } else {
        // Fallback local reply (replace with your provider call)
        reply = defaultLocalReply(text, context);
      }

      clearInterval(streamTimer);
      setStreaming(false);
      setDraft("");

      const asstMsg = {
        id: Math.random().toString(36).substr(2, 9),
        role: "assistant" as const,
        content: reply,
        ts: Date.now(),
      };
      setMessages((m) => [...m, asstMsg]);
    } catch (e) {
      clearInterval(streamTimer);
      setStreaming(false);
      setDraft("");
      const errMsg = {
        id: Math.random().toString(36).substr(2, 9),
        role: "assistant" as const,
        content: "I hit a snag sending that. Try again?",
        ts: Date.now(),
      };
      setMessages((m) => [...m, errMsg]);
    } finally {
      setSending(false);
    }
  }

  function handleAttachClick() {
    // In a real implementation, this would open a file picker
    onAttachFiles?.([]);
    setMessages((m) => [
      ...m,
      {
        id: Math.random().toString(36).substr(2, 9),
        role: "assistant",
        content: "File attachment functionality would be implemented here",
        ts: Date.now(),
      },
    ]);
  }

  // Set initial height when dock opens
  useEffect(() => {
    if (isOpen) {
      height.value = withTiming(200, { duration: 300 });
    } else {
      height.value = withTiming(0, { duration: 300 });
    }
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  return (
    <AnimatedView 
      style={[styles.dockContainer, animatedStyle, { paddingBottom: insets.bottom }]}
      onTouchEnd={toggleExpand}
    >
      <LinearGradient
        colors={['rgba(255,255,255,0.9)', 'rgba(255,255,255,0.7)']}
        style={styles.dockBackground}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Bot color="#000" size={24} />
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>HAUS AI</Text>
              <View style={styles.statusContainer}>
                <Sparkles color="#666" size={12} />
                <Text style={styles.statusText}>online</Text>
              </View>
            </View>
          </View>
          
          <TouchableOpacity onPress={() => onOpenChange(false)} style={styles.closeButton}>
            <X color="#000" size={24} />
          </TouchableOpacity>
        </View>

        {/* Messages */}
        <View style={styles.messagesContainer}>
          <ScrollView ref={scrollRef} style={styles.scrollArea}>
            <View style={styles.messagesContent}>
              {messages.map((m) => (
                <View key={m.id} style={styles.messageRow}>
                  {m.role === "assistant" ? (
                    <Bot color="#000" size={24} style={styles.avatar} />
                  ) : (
                    <User color="#000" size={24} style={styles.avatar} />
                  )}
                  <View style={styles.messageBubble}>
                    <Text style={styles.messageText}>{m.content}</Text>
                  </View>
                </View>
              ))}

              {/* Streaming draft */}
              {streaming && (
                <View style={styles.messageRow}>
                  <Bot color="#000" size={24} style={styles.avatar} />
                  <View style={[styles.messageBubble, styles.streamingBubble]}>
                    <View style={styles.streamingContent}>
                      <Loader2 color="#000" size={16} style={styles.spinner} />
                      <Text style={styles.messageText}>{draft || "Thinking…"}</Text>
                    </View>
                  </View>
                </View>
              )}
            </View>
          </ScrollView>

          {/* Composer */}
          <View style={styles.composerContainer}>
            {/* suggestions */}
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.suggestionsContainer}>
              <View style={styles.suggestionsContent}>
                {suggestions.map((s, i) => (
                  <TouchableOpacity 
                    key={i} 
                    style={styles.suggestionButton}
                    onPress={() => handleSend(s)}
                  >
                    <Text style={styles.suggestionText}>{s}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>

            <View style={styles.inputContainer}>
              <TouchableOpacity style={styles.iconButton} onPress={handleAttachClick}>
                <Paperclip color="#000" size={20} />
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.iconButton} onPress={() => onStartVoice?.()}>
                <Mic color="#000" size={20} />
              </TouchableOpacity>

              <TextInput
                value={input}
                onChangeText={setInput}
                placeholder="Ask anything…"
                style={styles.textInput}
                multiline
                numberOfLines={2}
                onKeyPress={(e) => {
                  if (e.nativeEvent.key === "Enter" && !e.nativeEvent.shiftKey) {
                    handleSend();
                  }
                }}
              />

              {sending ? (
                <TouchableOpacity style={styles.sendButton} onPress={() => onStopVoice?.()}>
                  <Square color="#fff" size={20} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity style={styles.sendButton} onPress={() => handleSend()}>
                  <Send color="#fff" size={20} />
                </TouchableOpacity>
              )}
            </View>

            {/* System hint */}
            <View style={styles.systemHintContainer}>
              <FileText color="#666" size={12} />
              <Text style={styles.systemHintText}>Files are private. I'll only use them in this chat.</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </AnimatedView>
  );
}

function defaultLocalReply(input: string, context?: { type?: string; refId?: string }) {
  if (/borrow|afford|power/i.test(input)) {
    return "Roughly speaking, most lenders cap DTI around 6–7x. If you share income, liabilities, and deposit, I'll estimate a range and the top 3 lenders likely to fit.";
  }
  if (/doc|upload|pdf|scan/i.test(input)) {
    return "Upload statements, payslips, or ID—I'll OCR and build the lender checklist.";
  }
  if (/price|range|value|valuation/i.test(input)) {
    return "For a pricing band I'll blend recent comps, listing traits, and suburb trendlines. Want me to fetch comparable sales?";
  }
  return context?.type === "property"
    ? "I can summarise this listing, flag risks, and suggest inspection questions."
    : "I can help shortlist properties, estimate borrowing, and prep your application—where do you want to start?";
}

const styles = StyleSheet.create({
  dockContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -5 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    zIndex: 1000,
  },
  dockBackground: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTextContainer: {
    flexDirection: 'column',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#666',
  },
  closeButton: {
    padding: 8,
  },
  messagesContainer: {
    flex: 1,
  },
  scrollArea: {
    flex: 1,
  },
  messagesContent: {
    padding: 12,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginBottom: 16,
  },
  avatar: {
    marginTop: 4,
  },
  messageBubble: {
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    padding: 12,
    maxWidth: '80%',
  },
  streamingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  streamingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  spinner: {
    alignSelf: 'center',
  },
  messageText: {
    fontSize: 14,
  },
  composerContainer: {
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    backgroundColor: '#fff',
  },
  suggestionsContainer: {
    marginBottom: 12,
  },
  suggestionsContent: {
    flexDirection: 'row',
    gap: 8,
  },
  suggestionButton: {
    backgroundColor: '#e0e0e0',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  suggestionText: {
    fontSize: 12,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 80,
    minHeight: 40,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
  },
  systemHintContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  systemHintText: {
    fontSize: 12,
    color: '#666',
  },
});