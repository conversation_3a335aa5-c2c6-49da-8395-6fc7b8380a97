import { cn } from 'heroui-native';
import React from 'react';
import { Text as RNText, type TextProps as RNTextProps } from 'react-native';

export const AppText = React.forwardRef<
  RNText,
  RNTextProps & { className?: string }
>(({ className, style, children, ...restProps }, ref) => {
  // Check if this is a heading (based on common heading classes)
  const isHeading = className && (
    className.includes('text-4xl') || 
    className.includes('text-3xl') || 
    className.includes('text-2xl') || 
    className.includes('text-xl') || 
    className.includes('text-lg') || 
    className.includes('font-bold') || 
    className.includes('font-semibold')
  );

  const processedChildren = isHeading && typeof children === 'string' 
    ? children.toUpperCase() 
    : children;

  return (
    <RNText 
      ref={ref} 
      className={cn('font-normal', className)} 
      style={[
        { fontFamily: 'Abel_400Regular' },
        style
      ]}
      {...restProps}
    >
      {processedChildren}
    </RNText>
  );
});

AppText.displayName = 'AppText';