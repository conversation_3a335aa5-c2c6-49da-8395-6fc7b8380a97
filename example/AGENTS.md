# Repository Guidelines

## Project Structure & Module Organization
The Expo Router surfaces live in `src/app`, and each folder maps directly to a route segment or nested stack. Share building blocks from `src/components`, wire global state through `src/contexts`, and keep utilities in `src/helpers`. Theme tokens stay centralized in `src/themes` and mirrored in `tailwind.config.js`. Store fonts and media inside `assets/` and load them with Expo modules. Product flows in `Screenflows/` should guide layout, copy, and navigation before introducing new screens.

## Build, Test, and Development Commands
Install dependencies with `pnpm install` whenever dependencies shift. Use `pnpm start` for the default Expo dev server with a cleared cache. Target devices with `pnpm ios`, `pnpm android`, or `pnpm web`; stop any running Metro instance before switching platforms to avoid stale bundles. When upgrading packages, re-run the platform command and verify asset loading from the simulator or browser.

## Coding Style & Naming Conventions
Write TypeScript functional components with two-space indentation, single quotes, and trailing commas—Prettier with the Tailwind plugin should stay enabled. Name routed files in kebab-case (e.g. `profile-overview.tsx`) and export hooks or helpers in camelCase. Compose styles with NativeWind classes and the `cn` helper; only reach for inline `StyleSheet.create` blocks when component-specific performance demands it.

## Testing Guidelines
Automated suites are not yet wired, so plan for manual QA across iOS, Android, and web before shipping. Use `@testing-library/react-native` for new interaction coverage and co-locate specs like `button-card.test.tsx` beside the source. Capture Expo screenshots or screen recordings for regressions and confirm accessibility basics: focus order, VoiceOver labels, and color contrast.

## Commit & Pull Request Guidelines
Follow the repo history with lowercase `<type>: <summary>` messages such as `feat: add theme toggle`, and keep each commit scoped to one concern. Pull requests must note user-facing impact, link Linear or Jira tickets, list verification steps, and attach platform-specific visuals for UI changes. Flag design or theming shifts for review and loop in mobile leads when touching navigation or route schema.

## Security & Configuration Tips
Store API keys and secrets outside the repo; reference them through Expo config plugins or environment files ignored by git. Review `app.json` and `eas.json` before releases to ensure the correct bundle identifiers, icon assets, and update channels. When adding dependencies, confirm they are compatible with Expo SDK 54 and do not require native changes beyond the managed workflow.
